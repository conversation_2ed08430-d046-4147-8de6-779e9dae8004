#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练单个工况的PatchTST模型
用于逐步训练和调试每个工况模型
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from condition_specific_trainer import ConditionSpecificTrainer

def main():
    parser = argparse.ArgumentParser(description='训练单个工况的PatchTST模型')
    parser.add_argument('--condition', type=str, required=True,
                       choices=['正常钻进', '起钻', '下钻', '正划眼', '倒划眼'],
                       help='要训练的工况名称')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--train_epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    
    args = parser.parse_args()
    
    print(f"开始训练工况: {args.condition}")
    print("=" * 50)
    print(f"配置参数:")
    print(f"  批次大小: {args.batch_size}")
    print(f"  学习率: {args.learning_rate}")
    print(f"  训练轮数: {args.train_epochs}")
    print(f"  模型维度: {args.d_model}")
    print(f"  早停耐心值: {args.patience}")
    print("=" * 50)
    
    # 创建训练配置
    training_config = {
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'train_epochs': args.train_epochs,
        'patience': args.patience,
    }
    
    model_config = {
        'd_model': args.d_model,
    }
    
    try:
        # 创建训练器
        trainer = ConditionSpecificTrainer(
            condition_name=args.condition,
            model_config=model_config,
            training_config=training_config
        )
        
        # 开始训练
        best_acc = trainer.train_model()
        
        print(f"\n训练完成!")
        print(f"工况: {args.condition}")
        print(f"最佳验证准确率: {best_acc:.4f}")
        print(f"模型保存路径: checkpoints/{args.condition}/")
        
    except Exception as e:
        print(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

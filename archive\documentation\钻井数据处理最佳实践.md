# 钻井数据处理最佳实践

## 🎯 核心实施方案

### 1. 处理顺序：**先工况分类，后时间窗口切分**

```
原始数据 → 工况分类(RIGSTA) → 时间段识别 → 3分钟窗口切分 → 训练数据
```

**为什么这样做？**
- ✅ 钻井工况具有明显的操作特征差异
- ✅ 不同工况的异常模式完全不同
- ✅ PatchTST模型需要学习一致的时间序列模式
- ✅ 避免跨工况的虚假关联

### 2. 工况内数据组织：**独立处理方式**

**❌ 错误方式（拼接）**：
```
工况A: [时间段1] + [时间段2] + [时间段3] → 长时间序列 → 滑动窗口
问题：创造了不存在的时间连续性，误导模型学习
```

**✅ 正确方式（独立处理）**：
```
工况A: 
  - 时间段1 → 独立的3分钟窗口切分
  - 时间段2 → 独立的3分钟窗口切分  
  - 时间段3 → 独立的3分钟窗口切分
```

## 🔧 具体技术实施

### 1. 工况边界处理

**问题**：工况变化频繁的数据文件（如几分钟内从"坐卡"切换到"起钻"）

**解决方案**：
```python
# 识别工况变化点
df['condition_change'] = (df['RIGSTA'] != df['RIGSTA'].shift(1)) | 
                        (df['date'].diff() > timedelta(minutes=5))
df['segment_id'] = df['condition_change'].cumsum()

# 按工况和段ID分组，每个段独立处理
for (condition, segment_id), group in df.groupby(['RIGSTA', 'segment_id']):
    # 独立处理每个时间段
```

**关键策略**：
- 🔹 工况变化立即分段
- 🔹 时间间隔超过5分钟也分段
- 🔹 每个段独立进行窗口切分

### 2. 时间窗口切分策略

**参数配置**：
- **窗口长度**：3分钟（180秒）
- **采样间隔**：10秒（假设）
- **每窗口记录数**：18条记录
- **滑动步长**：1分钟（6条记录）

**质量控制**：
```python
def validate_window(self, window: pd.DataFrame) -> bool:
    # 1. 数据完整性检查
    missing_ratio = window[required_params].isnull().sum().sum() / total_cells
    if missing_ratio > 0.1:  # 超过10%缺失
        return False
    
    # 2. 时间连续性检查
    time_gaps = window['date'].diff().dt.total_seconds()
    if time_gaps.max() > 60:  # 超过1分钟间隔
        return False
    
    return True
```

### 3. 输出数据结构

**推荐结构**：每个工况生成独立的数据文件夹
```
training_data/
├── 起钻/
│   ├── normal/
│   │   ├── window_001.csv
│   │   ├── window_002.csv
│   │   └── ...
│   └── anomaly/
│       ├── window_001.csv
│       ├── window_002.csv
│       └── ...
├── 下钻/
│   ├── normal/
│   └── anomaly/
├── 正常钻进/
│   ├── normal/
│   └── anomaly/
└── dataset_summary.json
```

**优势**：
- ✅ 便于按工况训练专门的模型
- ✅ 便于分析不同工况的数据分布
- ✅ 便于调整不同工况的训练策略
- ✅ 便于数据质量检查和验证

## 📊 数据规格要求

### 必需字段
```python
required_params = [
    'DEP',      # 井深
    'BITDEP',   # 钻头深度
    'WOB',      # 钻压
    'HKLD',     # 大钩载荷
    'RPM',      # 转速
    'TOR',      # 扭矩
    'SPP'       # 立管压力
]

key_fields = [
    'RIGSTA',   # 工况特征（关键分类字段）
    'date'      # 时间戳
]
```

### 可选字段
```python
optional_params = [
    'HOKHEI',   # 大钩高度
    'DRITIME',  # 钻井时间
    'CSIP'      # 套管压力
]
```

## 🚀 使用方法

### 1. 基本使用
```python
from optimized_drilling_processor import OptimizedDrillingProcessor

# 初始化处理器
processor = OptimizedDrillingProcessor()

# 处理已分类数据
dataset_summary = processor.process_classified_data(
    normal_folder="正常数据文件夹路径",
    anomaly_folder="异常数据文件夹路径", 
    output_dir="训练数据输出路径"
)
```

### 2. 自定义配置
```python
# 可以调整的参数
processor.window_minutes = 3          # 时间窗口长度
processor.min_segment_minutes = 3     # 最小时间段长度
processor.sampling_interval_seconds = 10  # 采样间隔
```

## 📈 预期效果

### 1. 数据质量提升
- 🔹 消除跨工况的虚假关联
- 🔹 保持真实的时间序列特征
- 🔹 提高数据的时间一致性

### 2. 模型训练效果
- 🔹 每个工况的异常模式更清晰
- 🔹 减少模型混淆和误判
- 🔹 提高预测准确性

### 3. 工程实用性
- 🔹 便于按工况部署模型
- 🔹 便于针对性优化
- 🔹 便于故障诊断和分析

## ⚠️ 注意事项

### 1. 数据量考虑
- 确保每个工况有足够的训练样本
- 平衡正常和异常样本的比例
- 考虑数据增强技术

### 2. 时间窗口调优
- 根据实际钻井操作特点调整窗口长度
- 考虑不同工况的时间特征差异
- 验证窗口切分的合理性

### 3. 质量监控
- 定期检查数据质量
- 监控模型性能变化
- 及时调整处理策略

---

**总结**：这种处理方式充分考虑了钻井作业的实际特点，既保持了数据的真实性，又满足了机器学习模型的训练需求，是钻井卡钻预警系统的最佳实践方案。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
早期信号检测模型运行脚本
适用于VSCode环境，支持调试和参数管理

作者: TSlib项目组
日期: 2024-06-29
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# 延迟导入run模块，避免依赖问题
run_main = None


class EarlySignalDetectionConfig:
    """早期信号检测配置类"""
    
    def __init__(self):
        """初始化默认配置参数"""
        # 基础任务配置
        self.task_name = 'earlysignaldet'
        self.is_training = 1
        self.model_id = 'earlysignaldetection'
        self.model = 'PatchTST'
        self.data = 'Earlysignaldet'
        
        # 数据路径配置
        self.root_path = './dataset/earlysignaldetection'
        
        # 模型架构参数
        self.e_layers = 3           # Encoder层数
        self.d_model = 128          # 模型维度
        self.d_ff = 256            # Feed-forward维度
        self.n_heads = 8           # 注意力头数
        self.top_k = 3             # Top-k注意力
        
        # 训练参数
        self.batch_size = 16        # 批次大小
        self.learning_rate = 0.001  # 学习率
        self.train_epochs = 100     # 训练轮数
        self.patience = 10          # 早停耐心值
        
        # 实验配置
        self.des = 'Exp'           # 实验描述
        self.itr = 1               # 实验迭代次数
        
        # 预测配置
        self.do_predict = True     # 是否进行预测
        
        # 其他默认参数
        self.seq_len = 96          # 序列长度（将根据数据自动调整）
        self.label_len = 48        # 标签长度
        self.pred_len = 96         # 预测长度
        self.enc_in = 10           # 编码器输入维度（钻井参数数量）
        self.dec_in = 10           # 解码器输入维度
        self.c_out = 2             # 输出类别数（正常/异常）
        self.num_class = 2         # 分类类别数
        self.dropout = 0.1         # Dropout率
        self.activation = 'gelu'   # 激活函数
        self.output_attention = False  # 是否输出注意力权重

        # PatchTST特有参数
        self.patch_len = 16        # Patch长度
        self.stride = 8            # Patch步长
        self.fc_dropout = 0.1      # 全连接层Dropout
        self.head_dropout = 0.0    # 分类头Dropout
        self.padding_patch = 'end' # Padding策略
        self.revin = 1             # RevIN标准化
        self.affine = 0            # 仿射变换
        self.subtract_last = 0     # 减去最后一个值
        self.decomposition = 0     # 序列分解
        self.kernel_size = 25      # 移动平均核大小
        self.individual = 0        # 个体建模

        # 训练策略参数
        self.d_layers = 1          # 解码器层数
        self.factor = 1            # 注意力因子
        self.embed = 'timeF'       # 嵌入类型
        self.distil = True         # 蒸馏
        self.mix = True            # 混合注意力

        # 优化器配置
        self.use_amp = False       # 是否使用混合精度训练
        self.lradj = 'type1'       # 学习率调整策略

        # 设备配置
        self.use_gpu = True        # 是否使用GPU
        self.gpu = 0               # GPU设备号
        self.use_multi_gpu = False # 是否使用多GPU
        self.devices = '0,1,2,3'   # 多GPU设备列表

        # 文件保存配置
        self.checkpoints = './checkpoints/'  # 模型保存路径
        
    def to_args(self):
        """将配置转换为argparse.Namespace对象"""
        args_dict = {}
        for key, value in self.__dict__.items():
            args_dict[key] = value
        return argparse.Namespace(**args_dict)
    
    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"✅ 更新参数: {key} = {value}")
            else:
                print(f"⚠️  警告: 未知参数 {key}")
    
    def print_config(self):
        """打印当前配置"""
        print("\n" + "="*60)
        print("🚀 早期信号检测模型配置")
        print("="*60)
        
        print("\n📋 基础配置:")
        print(f"  任务类型: {self.task_name}")
        print(f"  模型类型: {self.model}")
        print(f"  数据类型: {self.data}")
        print(f"  是否训练: {'是' if self.is_training else '否'}")
        print(f"  是否预测: {'是' if self.do_predict else '否'}")
        
        print("\n🏗️  模型架构:")
        print(f"  编码器层数: {self.e_layers}")
        print(f"  模型维度: {self.d_model}")
        print(f"  前馈维度: {self.d_ff}")
        print(f"  注意力头数: {self.n_heads}")
        print(f"  输入特征数: {self.enc_in}")
        print(f"  输出类别数: {self.num_class}")
        
        print("\n🎯 训练参数:")
        print(f"  批次大小: {self.batch_size}")
        print(f"  学习率: {self.learning_rate}")
        print(f"  训练轮数: {self.train_epochs}")
        print(f"  早停耐心: {self.patience}")
        print(f"  Dropout率: {self.dropout}")
        
        print("\n📁 路径配置:")
        print(f"  数据路径: {self.root_path}")
        print(f"  模型保存: {self.checkpoints}")
        
        print("\n💻 设备配置:")
        print(f"  使用GPU: {'是' if self.use_gpu else '否'}")
        print(f"  GPU设备: {self.gpu}")
        print(f"  混合精度: {'是' if self.use_amp else '否'}")
        
        print("="*60)


def check_dependencies():
    """检查必要的依赖包"""
    print("🔍 检查依赖包...")

    required_packages = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('sklearn', 'Scikit-learn'),
        ('matplotlib', 'Matplotlib'),
    ]

    optional_packages = [
        ('patoolib', 'patoolib'),
        ('tqdm', 'tqdm'),
    ]

    missing_required = []
    missing_optional = []

    # 检查必需包
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name}: 已安装")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing_required.append(package)

    # 检查可选包
    for package, name in optional_packages:
        try:
            __import__(package)
            print(f"✅ {name}: 已安装")
        except ImportError:
            print(f"⚠️  {name}: 未安装 (可选)")
            missing_optional.append(package)

    if missing_required:
        print(f"\n❌ 缺少必需依赖包: {', '.join(missing_required)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_required)}")
        return False

    if missing_optional:
        print(f"\n⚠️  缺少可选依赖包: {', '.join(missing_optional)}")
        print("建议运行以下命令安装:")
        print(f"pip install {' '.join(missing_optional)}")

    return True


def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")

    # 检查依赖包
    if not check_dependencies():
        return False

    # 检查数据路径
    data_path = Path('./dataset/earlysignaldetection')
    if not data_path.exists():
        print(f"❌ 错误: 数据路径不存在 {data_path.absolute()}")
        print("请确保数据集位于正确的路径下")
        return False

    # 创建checkpoints目录
    checkpoint_path = Path('./checkpoints')
    checkpoint_path.mkdir(exist_ok=True)
    print(f"✅ 检查点目录: {checkpoint_path.absolute()}")

    # 检查GPU可用性
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ GPU可用: {torch.cuda.get_device_name(0)}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️  GPU不可用，将使用CPU训练")
    except ImportError:
        print("⚠️  PyTorch未安装，请先安装PyTorch")
        return False

    return True


def run_early_signal_detection(config=None, **kwargs):
    """
    运行早期信号检测模型

    Args:
        config: EarlySignalDetectionConfig对象，如果为None则使用默认配置
        **kwargs: 额外的配置参数，用于覆盖默认配置
    """
    global run_main

    # 设置环境
    if not setup_environment():
        return False

    # 延迟导入run模块的依赖
    if run_main is None:
        try:
            print("📦 导入运行模块...")
            # 导入必要的模块和类
            import torch
            import random
            import numpy as np
            from exp.exp_long_term_forecasting import Exp_Long_Term_Forecast
            from exp.exp_short_term_forecasting import Exp_Short_Term_Forecast
            from exp.exp_imputation import Exp_Imputation
            from exp.exp_classification import Exp_Classification
            from exp.exp_anomaly_detection import Exp_Anomaly_Detection
            from exp.exp_earlysignaldet import Exp_Earlysignaldet
            from utils.print_args import print_args

            # 创建执行函数
            def execute_run_logic(args):
                # 设置随机种子
                fix_seed = 2021
                random.seed(fix_seed)
                torch.manual_seed(fix_seed)
                np.random.seed(fix_seed)

                # 设置GPU
                args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False

                if args.use_gpu and args.use_multi_gpu:
                    args.devices = args.devices.replace(' ', '')
                    device_ids = args.devices.split(',')
                    args.device_ids = [int(id_) for id_ in device_ids]
                    args.gpu = args.device_ids[0]

                print('Args in experiment:')
                print_args(args)

                # 选择实验类
                if args.task_name == 'long_term_forecast':
                    Exp = Exp_Long_Term_Forecast
                elif args.task_name == 'short_term_forecast':
                    Exp = Exp_Short_Term_Forecast
                elif args.task_name == 'imputation':
                    Exp = Exp_Imputation
                elif args.task_name == 'anomaly_detection':
                    Exp = Exp_Anomaly_Detection
                elif args.task_name == 'classification':
                    Exp = Exp_Classification
                elif args.task_name == 'earlysignaldet':
                    Exp = Exp_Earlysignaldet
                else:
                    Exp = Exp_Long_Term_Forecast

                # 执行训练或测试
                if args.is_training:
                    for ii in range(args.itr):
                        exp = Exp(args)
                        setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                            args.task_name, args.model_id, args.model, args.data, args.features,
                            args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                            args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                            args.distil, args.des, ii)

                        print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
                        exp.train(setting)

                        print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                        exp.test(setting)

                        if args.do_predict:
                            print('>>>>>>>predicting : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                            exp.predict(setting, True)

                        torch.cuda.empty_cache()
                else:
                    ii = 0
                    setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                        args.task_name, args.model_id, args.model, args.data, args.features,
                        args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                        args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                        args.distil, args.des, ii)

                    exp = Exp(args)
                    print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                    exp.test(setting, test=1)
                    torch.cuda.empty_cache()

            run_main = execute_run_logic
            print("✅ 模块导入成功")
        except ImportError as e:
            print(f"❌ 导入run模块失败: {str(e)}")
            print("请检查依赖包是否完整安装")
            return False

    # 初始化配置
    if config is None:
        config = EarlySignalDetectionConfig()

    # 更新配置参数
    if kwargs:
        config.update_config(**kwargs)

    # 打印配置信息
    config.print_config()

    # 转换为args对象
    args = config.to_args()

    try:
        print("\n🚀 开始运行早期信号检测模型...")
        print("-" * 60)

        # 调用主函数
        run_main(args)

        print("-" * 60)
        print("✅ 模型运行完成!")

        return True

    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    """主函数 - 支持命令行参数和直接运行"""
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='早期信号检测模型运行脚本')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--train_epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--e_layers', type=int, default=3, help='编码器层数')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--no_predict', action='store_true', help='不进行预测')
    parser.add_argument('--no_training', action='store_true', help='不进行训练')
    
    # 解析命令行参数
    cmd_args = parser.parse_args()
    
    # 创建配置对象
    config = EarlySignalDetectionConfig()
    
    # 根据命令行参数更新配置
    config.update_config(
        batch_size=cmd_args.batch_size,
        learning_rate=cmd_args.learning_rate,
        train_epochs=cmd_args.train_epochs,
        d_model=cmd_args.d_model,
        e_layers=cmd_args.e_layers,
        patience=cmd_args.patience,
        do_predict=not cmd_args.no_predict,
        is_training=0 if cmd_args.no_training else 1
    )
    
    # 运行模型
    success = run_early_signal_detection(config)
    
    if success:
        print("\n🎉 程序执行成功!")
    else:
        print("\n💥 程序执行失败!")
        sys.exit(1)

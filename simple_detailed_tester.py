#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版详细预测测试器
显示具体时间点的卡钻预测结果
"""

import sys
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from real_data_tester import RealDataTester

def analyze_file_with_timestamps(file_path):
    """分析单个文件并显示时间戳级别的预测结果"""
    
    # 初始化测试器
    tester = RealDataTester()
    tester.load_trained_models()
    
    if not tester.models:
        print("没有可用的模型")
        return
    
    print(f"\n分析文件: {Path(file_path).name}")
    print("=" * 80)
    
    try:
        # 读取数据
        data = pd.read_csv(file_path, encoding='utf-8')
        print(f"数据形状: {data.shape}")
        
        # 检查时间列
        if 'date' in data.columns:
            data['date'] = pd.to_datetime(data['date'])
            print(f"时间范围: {data['date'].min()} 到 {data['date'].max()}")
        
        # 确定工况
        if 'RIGSTA' in data.columns:
            conditions_in_data = data['RIGSTA'].value_counts()
            print(f"工况分布:")
            for condition, count in conditions_in_data.items():
                print(f"  {condition}: {count} 条记录")
            
            main_condition = conditions_in_data.index[0]
            if main_condition in tester.condition_mapping:
                test_condition = tester.condition_mapping[main_condition]
            else:
                test_condition = '正常钻进'
        else:
            test_condition = '正常钻进'
        
        print(f"使用模型: {test_condition}")
        
        # 进行预测
        result, error = tester.predict_condition(data, test_condition)
        
        if error:
            print(f"预测失败: {error}")
            return
        
        # 分析预测结果
        predictions = result['predictions']
        probabilities = result['probabilities']
        timestamps = result['timestamps']
        
        print(f"\n预测结果概览:")
        print(f"  总时间窗口数: {len(predictions)}")
        print(f"  异常窗口数: {sum(predictions)}")
        print(f"  异常比例: {np.mean(predictions)*100:.2f}%")
        
        # 找出异常时间点
        anomaly_indices = [i for i, pred in enumerate(predictions) if pred == 1]
        
        if anomaly_indices:
            print(f"\n检测到 {len(anomaly_indices)} 个异常时间窗口:")
            print(f"序号  时间戳                异常概率    风险等级")
            print("-" * 60)
            
            # 显示前10个异常点
            for i, idx in enumerate(anomaly_indices[:10]):
                timestamp = timestamps[idx] if idx < len(timestamps) else f"Window_{idx+1}"
                anomaly_prob = probabilities[idx][1] if len(probabilities[idx]) > 1 else 0
                
                if anomaly_prob > 0.9:
                    risk_level = "极高风险"
                elif anomaly_prob > 0.8:
                    risk_level = "高风险"
                elif anomaly_prob > 0.6:
                    risk_level = "中风险"
                else:
                    risk_level = "低风险"
                
                print(f"{i+1:<4}  {str(timestamp):<20} {anomaly_prob:<10.4f} {risk_level}")
            
            if len(anomaly_indices) > 10:
                print(f"... 还有 {len(anomaly_indices) - 10} 个异常点")
            
            # 最高风险时间点
            max_prob_idx = np.argmax([probabilities[idx][1] if len(probabilities[idx]) > 1 else 0 for idx in anomaly_indices])
            max_risk_idx = anomaly_indices[max_prob_idx]
            max_timestamp = timestamps[max_risk_idx] if max_risk_idx < len(timestamps) else f"Window_{max_risk_idx+1}"
            max_prob = probabilities[max_risk_idx][1] if len(probabilities[max_risk_idx]) > 1 else 0
            
            print(f"\n最高风险时间点:")
            print(f"  时间: {max_timestamp}")
            print(f"  异常概率: {max_prob:.4f}")
            print(f"  对应数据行: {max_risk_idx*60} - {max_risk_idx*60+180}")
            
            # 保存详细结果
            save_detailed_csv(file_path, anomaly_indices, timestamps, probabilities, test_condition)
            
        else:
            print(f"\n未检测到异常时间窗口")
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

def save_detailed_csv(file_path, anomaly_indices, timestamps, probabilities, condition):
    """保存详细结果到CSV"""
    try:
        filename = Path(file_path).stem
        output_file = f"{filename}_异常时间点.csv"
        
        # 创建详细结果
        detailed_data = []
        for idx in anomaly_indices:
            timestamp = timestamps[idx] if idx < len(timestamps) else f"Window_{idx+1}"
            anomaly_prob = probabilities[idx][1] if len(probabilities[idx]) > 1 else 0
            normal_prob = probabilities[idx][0] if len(probabilities[idx]) > 0 else 0
            
            if anomaly_prob > 0.9:
                risk_level = "极高风险"
            elif anomaly_prob > 0.8:
                risk_level = "高风险"
            elif anomaly_prob > 0.6:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            detailed_data.append({
                '时间戳': timestamp,
                '异常概率': anomaly_prob,
                '正常概率': normal_prob,
                '风险等级': risk_level,
                '工况': condition,
                '窗口索引': idx,
                '对应数据行开始': idx * 60,
                '对应数据行结束': idx * 60 + 180
            })
        
        df = pd.DataFrame(detailed_data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n详细结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存CSV失败: {e}")

def main():
    """主函数"""
    # 选择几个有代表性的测试文件
    test_files = [
        "dataset/processed_data/test_data/宁209H67-6_20220528_160000_测试数据.csv",  # 异常比例最高
        "dataset/processed_data/test_data/泸203H12-4_20220308_220000_测试数据.csv",  # 最高异常概率
        "dataset/processed_data/test_data/宁209H70-5_20220513_055000_测试数据.csv",  # 正划眼工况
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            analyze_file_with_timestamps(test_file)
            print("\n" + "="*80)
            print("按回车键继续...")
            input()
        else:
            print(f"文件不存在: {test_file}")

if __name__ == "__main__":
    main()

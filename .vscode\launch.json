{"version": "0.2.0", "configurations": [{"name": "🚀 早期信号检测 - 完整训练+预测 (推荐)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet_simple.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--do_predict"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🚀 早期信号检测 - 完整训练+预测 (原版)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": [], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🎯 早期信号检测 - 仅训练 (推荐)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet_simple.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": [], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🎯 早期信号检测 - 仅训练 (原版)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--no_predict"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🔮 早期信号检测 - 仅预测", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--no_training"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "⚡ 早期信号检测 - 快速测试 (推荐)", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet_simple.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--train_epochs", "5", "--batch_size", "8", "--patience", "3", "--do_predict"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🔧 早期信号检测 - 自定义参数", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--batch_size", "32", "--learning_rate", "0.0005", "--d_model", "256", "--e_layers", "4", "--train_epochs", "50"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}, {"name": "🐛 调试模式 - 早期信号检测", "type": "python", "request": "launch", "program": "${workspaceFolder}/run_earlysignaldet.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--train_epochs", "2", "--batch_size", "4"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": true, "stopOnEntry": true, "logToFile": true}, {"name": "📊 原始run.py - 命令行方式", "type": "python", "request": "launch", "program": "${workspaceFolder}/run.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "args": ["--task_name", "early<PERSON><PERSON><PERSON>", "--is_training", "1", "--root_path", "./dataset/earlysignaldetection", "--model_id", "earlysignaldetection", "--model", "PatchTST", "--data", "<PERSON><PERSON><PERSON><PERSON>", "--e_layers", "3", "--batch_size", "16", "--d_model", "128", "--d_ff", "256", "--top_k", "3", "--des", "Exp", "--itr", "1", "--learning_rate", "0.001", "--train_epochs", "100", "--patience", "10", "--do_predict"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "justMyCode": false, "stopOnEntry": false}]}
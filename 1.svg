graph TD
    A[钻井数据] --> B[按工况分类]
    B --> C[正常钻进]
    B --> D[起钻]
    B --> E[下钻]
    B --> F[正划眼]
    B --> G[倒划眼]
    
    H[模型架构选择] --> I[方案A: 工况专用模型]
    H --> J[方案B: 统一多工况模型]
    
    I --> I1[为每种工况<br/>训练专用模型]
    I1 --> I2[实际应用时<br/>先识别工况<br/>再选择模型]
    
    J --> J1[训练一个<br/>多工况通用模型]
    J1 --> J2[实际应用时<br/>直接预测<br/>无需工况识别]
    
    K[测试数据策略] --> L[方案A: 按工况分类测试<br/>验证各工况专用模型]
    K --> M[方案B: 混合工况测试<br/>验证实际应用场景]
    
    style I1 fill:#e8f5e8
    style J1 fill:#fff3e0
    style L fill:#f3e5f5
    style M fill:#f3e5f5
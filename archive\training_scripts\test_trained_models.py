#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已训练的工况专用模型
验证模型性能并生成测试报告
"""

import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from condition_specific_data_loader import create_condition_dataloaders
from condition_specific_trainer import ConditionSpecificTrainer

def load_trained_model(condition_name):
    """加载已训练的模型"""
    checkpoint_path = Path("checkpoints") / condition_name / "best_model.pth"
    
    if not checkpoint_path.exists():
        raise FileNotFoundError(f"模型文件不存在: {checkpoint_path}")
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 创建训练器来重建模型
    trainer = ConditionSpecificTrainer(
        condition_name=condition_name,
        model_config=checkpoint['model_config'],
        training_config=checkpoint['training_config']
    )
    
    # 创建模型并加载权重
    model = trainer.create_model()
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, checkpoint

def test_model(condition_name):
    """测试单个工况模型"""
    print(f"\n测试工况模型: {condition_name}")
    print("-" * 40)
    
    try:
        # 加载模型
        model, checkpoint = load_trained_model(condition_name)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        print(f"模型加载成功")
        print(f"训练时最佳验证准确率: {checkpoint['val_acc']:.4f}")
        
        # 创建测试数据加载器
        dataloaders = create_condition_dataloaders(
            condition_name=condition_name,
            batch_size=16,
            seq_len=180
        )
        
        test_loader = dataloaders['test']
        print(f"测试数据批次数: {len(test_loader)}")
        
        # 测试模型
        all_predictions = []
        all_labels = []
        total_loss = 0.0
        criterion = nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                batch_x = batch_x.float().to(device)
                batch_y = batch_y.long().to(device)
                
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                total_loss += loss.item()
                
                _, predicted = torch.max(outputs.data, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(batch_y.cpu().numpy())
        
        # 计算指标
        avg_loss = total_loss / len(test_loader)
        accuracy = accuracy_score(all_labels, all_predictions)
        precision = precision_score(all_labels, all_predictions, average='weighted', zero_division=0)
        recall = recall_score(all_labels, all_predictions, average='weighted', zero_division=0)
        f1 = f1_score(all_labels, all_predictions, average='weighted', zero_division=0)
        
        # 混淆矩阵
        cm = confusion_matrix(all_labels, all_predictions)
        
        print(f"测试结果:")
        print(f"  测试损失: {avg_loss:.4f}")
        print(f"  测试准确率: {accuracy:.4f}")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  F1分数: {f1:.4f}")
        print(f"  混淆矩阵:")
        print(f"    {cm}")
        
        # 类别分布
        unique_labels, label_counts = np.unique(all_labels, return_counts=True)
        unique_preds, pred_counts = np.unique(all_predictions, return_counts=True)
        
        print(f"  真实标签分布: {dict(zip(unique_labels, label_counts))}")
        print(f"  预测标签分布: {dict(zip(unique_preds, pred_counts))}")
        
        return {
            'condition': condition_name,
            'test_loss': avg_loss,
            'test_accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': cm.tolist(),
            'label_distribution': dict(zip(unique_labels.tolist(), label_counts.tolist())),
            'prediction_distribution': dict(zip(unique_preds.tolist(), pred_counts.tolist())),
            'training_best_val_acc': checkpoint['val_acc']
        }
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_all_trained_models():
    """测试所有已训练的模型"""
    print("测试所有已训练的工况专用模型")
    print("=" * 50)
    
    conditions = ['正常钻进', '起钻', '下钻', '正划眼', '倒划眼']
    test_results = []
    
    for condition in conditions:
        checkpoint_path = Path("checkpoints") / condition / "best_model.pth"
        
        if checkpoint_path.exists():
            result = test_model(condition)
            if result:
                test_results.append(result)
        else:
            print(f"\n跳过工况: {condition} (模型不存在)")
    
    # 生成测试总结
    if test_results:
        print(f"\n{'='*50}")
        print("测试总结")
        print(f"{'='*50}")
        
        avg_accuracy = sum(r['test_accuracy'] for r in test_results) / len(test_results)
        avg_f1 = sum(r['f1_score'] for r in test_results) / len(test_results)
        
        print(f"测试的模型数量: {len(test_results)}")
        print(f"平均测试准确率: {avg_accuracy:.4f}")
        print(f"平均F1分数: {avg_f1:.4f}")
        
        # 保存测试结果
        results_file = Path("test_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n测试结果已保存到: {results_file}")
        
        # 生成测试报告
        generate_test_report(test_results)
    
    return test_results

def generate_test_report(test_results):
    """生成测试报告"""
    report_file = Path("test_report.md")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 工况专用模型测试报告\n\n")
        f.write(f"测试时间: {Path().resolve()}\n\n")
        
        f.write("## 测试结果总结\n\n")
        f.write("| 工况 | 测试准确率 | 精确率 | 召回率 | F1分数 | 训练最佳验证准确率 |\n")
        f.write("|------|------------|--------|--------|--------|-----------------|\n")
        
        for result in test_results:
            f.write(f"| {result['condition']} | {result['test_accuracy']:.4f} | "
                   f"{result['precision']:.4f} | {result['recall']:.4f} | "
                   f"{result['f1_score']:.4f} | {result['training_best_val_acc']:.4f} |\n")
        
        if test_results:
            avg_accuracy = sum(r['test_accuracy'] for r in test_results) / len(test_results)
            avg_f1 = sum(r['f1_score'] for r in test_results) / len(test_results)
            
            f.write(f"\n## 统计信息\n\n")
            f.write(f"- 测试的模型数量: {len(test_results)}\n")
            f.write(f"- 平均测试准确率: {avg_accuracy:.4f}\n")
            f.write(f"- 平均F1分数: {avg_f1:.4f}\n")
        
        f.write(f"\n## 详细结果\n\n")
        for result in test_results:
            f.write(f"### {result['condition']}\n\n")
            f.write(f"- 测试损失: {result['test_loss']:.4f}\n")
            f.write(f"- 测试准确率: {result['test_accuracy']:.4f}\n")
            f.write(f"- 精确率: {result['precision']:.4f}\n")
            f.write(f"- 召回率: {result['recall']:.4f}\n")
            f.write(f"- F1分数: {result['f1_score']:.4f}\n")
            f.write(f"- 真实标签分布: {result['label_distribution']}\n")
            f.write(f"- 预测标签分布: {result['prediction_distribution']}\n\n")
    
    print(f"测试报告已保存到: {report_file}")

if __name__ == "__main__":
    test_all_trained_models()

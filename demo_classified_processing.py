#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示已分类数据的处理流程
模拟用户已经人工分类好的正常数据和征兆数据的处理过程
"""

import pandas as pd
import numpy as np
from pathlib import Path
from process_classified_data import ClassifiedDataProcessor

def create_demo_data():
    """
    创建演示用的已分类数据
    """
    print("🔧 创建演示数据...")
    
    # 创建目录
    Path("demo_正常数据").mkdir(exist_ok=True)
    Path("demo_征兆数据").mkdir(exist_ok=True)
    
    # 创建正常数据文件
    for i in range(3):
        df_normal = create_sample_drilling_data(is_normal=True, file_id=i)
        df_normal.to_csv(f"demo_正常数据/normal_file_{i+1}.csv", index=False, encoding='utf-8-sig')
    
    # 创建征兆数据文件
    for i in range(2):
        df_anomaly = create_sample_drilling_data(is_normal=False, file_id=i)
        df_anomaly.to_csv(f"demo_征兆数据/anomaly_file_{i+1}.csv", index=False, encoding='utf-8-sig')
    
    print("✅ 演示数据创建完成")
    print("  - 正常数据: 3个文件")
    print("  - 征兆数据: 2个文件")

def create_sample_drilling_data(is_normal: bool, file_id: int) -> pd.DataFrame:
    """
    创建示例钻井数据
    """
    n_points = 200
    
    # 基础数据
    data = {
        'DEP': np.linspace(2970 + file_id, 2975 + file_id, n_points),
        'BITDEP': np.linspace(2970 + file_id, 2975 + file_id, n_points),
    }
    
    # 创建不同工况的数据段
    conditions = []
    for i in range(n_points):
        if i < 60:
            conditions.append('正常钻进')
        elif i < 100:
            conditions.append('起钻')
        elif i < 140:
            conditions.append('坐卡')
        else:
            conditions.append('下钻')
    
    data['CW'] = conditions
    
    if is_normal:
        # 正常数据 - 参数在正常范围内
        data['WOB'] = np.random.normal(160, 15, n_points)
        data['RPM'] = np.random.normal(105, 5, n_points)
        data['HKLD'] = np.random.normal(970, 20, n_points)
        data['TOR'] = np.random.normal(18, 3, n_points)
        data['SPP'] = np.random.normal(25.8, 2, n_points)
    else:
        # 征兆数据 - 参数有异常
        data['WOB'] = np.random.normal(200, 30, n_points)  # 钻压偏高
        data['RPM'] = np.random.normal(85, 15, n_points)   # 转速偏低
        data['HKLD'] = np.random.normal(1050, 40, n_points)  # 大钩载荷偏高
        data['TOR'] = np.random.normal(28, 8, n_points)    # 扭矩偏高
        data['SPP'] = np.random.normal(32, 4, n_points)    # 立管压力偏高
        
        # 添加一些突发异常点
        anomaly_indices = np.random.choice(n_points, size=n_points//10, replace=False)
        for idx in anomaly_indices:
            data['WOB'][idx] += np.random.normal(50, 20)
            data['TOR'][idx] += np.random.normal(15, 5)
    
    return pd.DataFrame(data)

def demonstrate_processing():
    """
    演示处理流程
    """
    print("\n🎯 演示已分类数据处理流程")
    print("=" * 60)
    
    # 配置
    config = {
        'window_size': 50,           # 较小的窗口便于演示
        'step_size': 10,             # 较小的步长
        'min_quality_score': 0.5,    # 较低的质量阈值
        'min_segment_length': 20,    # 较小的最小段长度
    }
    
    processor = ClassifiedDataProcessor(config)
    
    try:
        # 1. 处理正常数据
        print("\n🔵 处理正常数据文件夹...")
        normal_windows = processor.process_data_folder("demo_正常数据", "正常")
        
        # 2. 处理征兆数据
        print("\n🔴 处理征兆数据文件夹...")
        anomaly_windows = processor.process_data_folder("demo_征兆数据", "征兆")
        
        # 3. 创建训练数据集
        print("\n📊 创建训练数据集...")
        dataset_info = processor.create_training_dataset(
            normal_windows, anomaly_windows, "demo_training_dataset"
        )
        
        # 4. 显示结果
        print(f"\n📈 处理结果统计:")
        print(f"总工况数: {len(dataset_info['conditions'])}")
        print(f"总样本数: {dataset_info['total_samples']}")
        
        for condition, info in dataset_info['conditions'].items():
            print(f"\n工况: {condition}")
            print(f"  - 正常样本: {info['normal_samples']}")
            print(f"  - 异常样本: {info['anomaly_samples']}")
            print(f"  - 异常比例: {info['anomaly_ratio']:.3f}")
        
        return dataset_info
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def show_correct_workflow():
    """
    展示正确的工作流程
    """
    print("📋 正确的钻井卡钻预警数据处理工作流程")
    print("=" * 70)
    
    print("""
🎯 您的实际情况：
✅ 已经有人工挑选分类好的数据：
   - 正常数据文件夹/
   - 征兆数据文件夹/

🔧 正确的处理流程：

1️⃣ 数据预处理
   - 处理中文编码问题
   - 标准化列名
   - 数据质量检查

2️⃣ 按工况分类
   - 对正常数据按工况（起钻、下钻、正常钻进等）分类
   - 对征兆数据按工况分类
   - 提取高质量的数据段

3️⃣ 创建滑动窗口
   - 从每个工况的数据段创建滑动窗口
   - 确保窗口大小适合PatchTST模型

4️⃣ 生成训练数据集
   - 每个工况都有：正常样本 + 异常样本
   - 按照标准格式保存
   - 生成数据集信息文件

5️⃣ 模型训练
   - 可以为每个工况单独训练模型
   - 也可以多工况联合训练
   - 使用PatchTST进行早期信号检测

❌ 不需要的环节：
   - ❌ 人工标注（因为您已经分类好了）
   - ❌ 基于工况标签判断正负样本
   - ❌ 复杂的异常检测算法

💡 关键优势：
   - 利用您的专业经验和人工分类
   - 按工况细分，提高模型针对性
   - 直接生成可用的训练数据
   - 支持多工况预警模型
    """)

def cleanup_demo_files():
    """
    清理演示文件
    """
    import shutil
    
    demo_folders = [
        "demo_正常数据",
        "demo_征兆数据", 
        "demo_training_dataset"
    ]
    
    for folder in demo_folders:
        if Path(folder).exists():
            shutil.rmtree(folder)
            print(f"🗑️ 清理演示文件夹: {folder}")

def main():
    """
    主演示函数
    """
    print("🎯 已分类钻井数据处理演示")
    print("=" * 60)
    
    try:
        # 1. 展示正确的工作流程
        show_correct_workflow()
        
        # 2. 创建演示数据
        create_demo_data()
        
        # 3. 演示处理过程
        result = demonstrate_processing()
        
        if result:
            print(f"\n" + "="*60)
            print("🎉 演示完成!")
            print("="*60)
            
            print(f"\n📁 生成的文件结构:")
            print("demo_training_dataset/")
            for condition in result['conditions'].keys():
                print(f"├── {condition}/")
                print(f"│   ├── normal/     # 正常样本")
                print(f"│   └── anomaly/    # 异常样本")
            print("├── dataset_info.json")
            print("└── usage_instructions.md")
            
            print(f"\n🚀 实际使用时：")
            print("1. 将您的正常数据放在一个文件夹")
            print("2. 将您的征兆数据放在另一个文件夹")
            print("3. 修改 process_classified_data.py 中的文件夹路径")
            print("4. 运行处理脚本")
            print("5. 使用生成的数据训练PatchTST模型")
            
            # 询问是否清理演示文件
            print(f"\n🗑️ 演示完成，是否清理演示文件？")
            print("演示文件将在5秒后自动清理...")
            
            import time
            time.sleep(5)
            cleanup_demo_files()
            
        return result
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()

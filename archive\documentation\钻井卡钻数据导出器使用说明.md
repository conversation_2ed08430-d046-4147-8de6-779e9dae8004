# 钻井卡钻数据导出器使用说明

## 🎯 功能概述

`drilling_stuck_data_exporter.py` 是专门为钻井卡钻预警模型设计的数据导出工具，基于钻井数据处理最佳实践，能够自动从数据库中提取三种类型的训练和测试数据。

## 📋 数据导出规格

### 1. 征兆数据（异常样本）
- **时间范围**: 每个卡钻事件发生前15分钟的精确数据
- **用途**: 作为异常样本进行模型训练
- **后续处理**: 按3分钟窗口切分成5个训练样本
- **输出文件夹**: `symptom_data/`

### 2. 测试数据（模型验证）
- **时间范围**: 每个卡钻事件发生前24小时的完整数据
- **用途**: 模型性能测试和预警效果验证
- **特点**: 包含卡钻征兆期，用于测试模型的预警能力
- **输出文件夹**: `test_data/`

### 3. 正常数据（正常样本）
- **时间范围**: 卡钻事件发生前7-30天随机选择24小时
- **用途**: 作为正常样本进行模型训练
- **筛选条件**: 排除任何已知的异常时间段
- **输出文件夹**: `normal_data/`

## 🔧 使用方法

### 1. 准备卡钻事件表

创建CSV文件（如 `卡钻事件表.csv`），包含以下列：

```csv
井名,卡钻时间,事件描述
自201H35-2,2022-03-04 17:33:08,钻具卡钻事件
泸203H153-1,2022-01-17 05:00:00,下钻过程中发生卡钻
自225,2022-01-26 19:51:00,正常钻进时卡钻
```

**字段说明**：
- `井名`: 井的名称，必须与数据库文件夹名称一致
- `卡钻时间`: 卡钻发生的精确时间 (YYYY-MM-DD HH:MM:SS)
- `事件描述`: 可选，事件的描述信息

### 2. 配置参数

修改 `main()` 函数中的配置参数：

```python
BASE_PATH = r"D:\采数据\PROCESS"  # 数据库文件的根目录
OUTPUT_DIR = r"D:\采数据\PROCESS\stuck_prediction_data"  # 输出目录
STUCK_EVENTS_CSV = "卡钻事件表.csv"  # 卡钻事件CSV文件
```

### 3. 运行导出

```bash
python drilling_stuck_data_exporter.py
```

## 📊 输出结构

```
stuck_prediction_data/
├── symptom_data/           # 征兆数据（异常样本）
│   ├── 自201H35-2_20220304_173308_征兆数据.csv
│   ├── 泸203H153-1_20220117_050000_征兆数据.csv
│   └── ...
├── test_data/              # 测试数据（模型验证）
│   ├── 自201H35-2_20220304_173308_测试数据.csv
│   ├── 泸203H153-1_20220117_050000_测试数据.csv
│   └── ...
├── normal_data/            # 正常数据（正常样本）
│   ├── 自201H35-2_20220304_173308_正常数据_20220205_120000.csv
│   ├── 泸203H153-1_20220117_050000_正常数据_20211220_080000.csv
│   └── ...
└── 导出报告.md             # 详细的导出报告
```

## 🔍 关键特性

### 1. 工况字段标准化

自动将数据库中的RIGSTA字段标准化为以下工况：
- 起钻
- 下钻  
- 正常钻进
- 正划眼
- 倒划眼
- 坐卡
- 循环
- 接单根
- 其他

### 2. 数据质量控制

- **缺失数据检查**: 验证必需字段的完整性
- **时间连续性检查**: 识别异常的时间间隔
- **质量评分**: 为每个数据集计算质量分数
- **异常处理**: 自动处理数据缺失和时间间隔异常

### 3. 跨月数据查询

- 自动识别时间范围跨越的月份
- 查询多个数据库文件并合并结果
- 按时间排序确保数据连续性

### 4. 批量处理优化

- 支持同时处理多个卡钻事件
- 详细的进度监控和错误处理
- 生成完整的处理报告

## 📈 数据字段

### 必需字段（基于您选中的代码）
```python
required_fields = [
    'DEP',      # 井深
    'BITDEP',   # 钻头深度
    'HOKHEI',   # 大钩高度
    'DRITIME',  # 钻井时间
    'WOB',      # 钻压
    'HKLD',     # 大钩载荷
    'RPM',      # 转速
    'TOR',      # 扭矩
    'SPP',      # 立管压力
    'CSIP',     # 套管压力
    'RIGSTA'    # 工况（关键分类字段）
]
```

### 可选字段
```python
optional_fields = [
    'FLOWIN',   # 进口流量
    'FLOWOUT'   # 出口流量
]
```

## ⚠️ 注意事项

### 1. 数据库文件结构
- 数据库文件命名格式: `0YYMM.db` (如 `02203.db` 表示2022年3月)
- 井文件夹名称必须与CSV中的井名完全一致
- 数据库表名为 `data`，包含 `WELLDATE` 和 `WELLTIME` 字段

### 2. 时间格式要求
- CSV中的时间格式: `YYYY-MM-DD HH:MM:SS`
- 支持多种时间格式的自动识别和转换

### 3. 编码处理
- 自动检测CSV文件编码（UTF-8, GBK, GB2312, UTF-8-SIG）
- 输出文件使用UTF-8-SIG编码，确保中文正确显示

### 4. 性能考虑
- 大数据量时建议分批处理
- 监控内存使用情况
- 定期清理临时文件

## 🔧 自定义配置

### 修改时间范围
```python
export_configs = {
    'symptom': {
        'time_before_minutes': 15,  # 可调整征兆数据时间范围
    },
    'test': {
        'time_before_hours': 24,    # 可调整测试数据时间范围
    },
    'normal': {
        'time_before_days_start': 7,   # 可调整正常数据起始天数
        'time_before_days_end': 30,    # 可调整正常数据结束天数
    }
}
```

### 修改工况映射
```python
rigsta_mapping = {
    '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT', '起', '上'],
    # 根据实际数据库情况添加更多映射
}
```

## 📋 故障排除

### 常见问题

1. **井路径不存在**
   - 检查BASE_PATH配置是否正确
   - 确认井名与文件夹名称一致

2. **数据库文件不存在**
   - 检查数据库文件命名格式
   - 确认时间范围内的数据库文件存在

3. **未提取到数据**
   - 检查时间范围是否合理
   - 确认数据库中有相应时间段的数据

4. **CSV读取失败**
   - 检查CSV文件格式和编码
   - 确认必需列名正确

### 调试模式
在代码中添加更多调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

**总结**: 这个导出器专门针对钻井卡钻预警模型的数据需求设计，集成了数据质量控制、工况标准化、批量处理等功能，是构建高质量训练数据集的最佳工具。

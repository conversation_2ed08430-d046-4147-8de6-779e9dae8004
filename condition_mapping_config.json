{"标准工况映射表": {"description": "基于实际钻进数据的工况标签标准化映射", "version": "1.0", "last_updated": "2024-06-29", "mapping": {"起钻相关": {"起钻": "起钻", "坐卡": "起钻", "起下钻": "起钻", "起钻具": "起钻", "上提": "起钻", "提升": "起钻", "tripping_up": "起钻", "pulling": "起钻", "1": "起钻"}, "下钻相关": {"下钻": "下钻", "下钻具": "下钻", "下放": "下钻", "入井": "下钻", "tripping_down": "下钻", "running": "下钻", "2": "下钻"}, "正常钻进相关": {"正常钻进": "正常钻进", "钻进": "正常钻进", "正常": "正常钻进", "drilling": "正常钻进", "normal_drilling": "正常钻进", "钻井": "正常钻进", "0": "正常钻进"}, "正划眼相关": {"正划眼": "正划眼", "划眼": "正划眼", "扩眼": "正划眼", "reaming": "正划眼", "forward_reaming": "正划眼", "3": "正划眼"}, "倒划眼相关": {"倒划眼": "倒划眼", "倒划": "倒划眼", "反划": "倒划眼", "反向划眼": "倒划眼", "reverse_reaming": "倒划眼", "back_reaming": "倒划眼", "4": "倒划眼"}, "其他操作": {"停钻": "正常钻进", "循环": "正常钻进", "接单根": "下钻", "卸单根": "起钻", "circulation": "正常钻进", "connection": "下钻", "disconnection": "起钻"}}}, "无效标签模式": {"description": "需要标记为UNKNOWN的无效标签模式", "patterns": ["^$", "^\\s*$", "^nan$", "^null$", "^none$", "^N/A$", "^-$", "^\\d{4}", "^[^\\u4e00-\\u9fa5a-zA-Z]", "^\\d+\\.\\d+$", "^test$", "^unknown$"], "case_insensitive": true}, "工况参数特征": {"description": "不同工况的典型参数特征，用于验证标签准确性", "起钻": {"WOB": {"typical_range": [0, 30], "warning_above": 50}, "HKLD": {"typical_range": [800, 2000], "warning_below": 600}, "RPM": {"typical_range": [0, 50], "warning_above": 100}, "depth_change": {"expected": "decreasing", "tolerance": 0.5}, "description": "起钻时钻压较低，大钩载荷较高，深度递减"}, "下钻": {"WOB": {"typical_range": [0, 30], "warning_above": 50}, "HKLD": {"typical_range": [400, 1200], "warning_below": 300}, "RPM": {"typical_range": [0, 50], "warning_above": 100}, "depth_change": {"expected": "increasing", "tolerance": 0.5}, "description": "下钻时钻压较低，大钩载荷中等，深度递增"}, "正常钻进": {"WOB": {"typical_range": [30, 200], "warning_below": 20}, "HKLD": {"typical_range": [600, 1500], "warning_outside": [400, 2000]}, "RPM": {"typical_range": [40, 150], "warning_below": 20}, "depth_change": {"expected": "variable", "tolerance": 2.0}, "description": "正常钻进时钻压较高，转速较高，深度变化"}, "正划眼": {"WOB": {"typical_range": [20, 100], "warning_above": 150}, "HKLD": {"typical_range": [500, 1300], "warning_outside": [400, 1600]}, "RPM": {"typical_range": [30, 120], "warning_below": 20}, "depth_change": {"expected": "minimal", "tolerance": 1.0}, "description": "正划眼时钻压中等，转速中等，深度变化较小"}, "倒划眼": {"WOB": {"typical_range": [10, 80], "warning_above": 120}, "HKLD": {"typical_range": [600, 1400], "warning_outside": [400, 1800]}, "RPM": {"typical_range": [20, 100], "warning_below": 15}, "depth_change": {"expected": "minimal", "tolerance": 1.0}, "description": "倒划眼时钻压较低，转速中等，深度变化较小"}}, "数据质量标准": {"description": "数据质量评估标准", "segment_length": {"minimum": 30, "maximum": 500, "preferred_by_condition": {"起钻": {"min": 50, "max": 400, "preferred": 200}, "下钻": {"min": 50, "max": 400, "preferred": 200}, "正常钻进": {"min": 80, "max": 600, "preferred": 300}, "正划眼": {"min": 60, "max": 500, "preferred": 250}, "倒划眼": {"min": 40, "max": 300, "preferred": 150}}}, "missing_data": {"max_missing_ratio": 0.1, "critical_columns": ["DEP", "BITDEP", "WOB", "HKLD", "RPM"], "optional_columns": ["TOR", "SPP", "CSIP"]}, "consistency": {"condition_consistency_threshold": 0.9, "parameter_consistency_threshold": 0.8, "time_continuity_threshold": 3.0}, "quality_score_weights": {"condition_consistency": 0.4, "data_completeness": 0.3, "parameter_validity": 0.2, "temporal_continuity": 0.1}}, "预处理配置": {"description": "针对频繁工况切换数据的推荐预处理配置", "default_config": {"min_segment_length": 30, "max_segment_length": 400, "overlap_ratio": 0.1, "transition_buffer": 8, "min_quality_score": 0.65, "enable_transition_removal": true, "enable_short_segment_filtering": true, "min_condition_duration_seconds": 120, "min_condition_duration_points": 25}, "aggressive_config": {"description": "适用于噪声较多的数据", "min_segment_length": 50, "max_segment_length": 300, "transition_buffer": 12, "min_quality_score": 0.75, "min_condition_duration_seconds": 180}, "conservative_config": {"description": "适用于数据质量较好的情况", "min_segment_length": 25, "max_segment_length": 500, "transition_buffer": 5, "min_quality_score": 0.55, "min_condition_duration_seconds": 90}}, "训练样本配置": {"description": "不同工况的训练样本构建配置", "sample_requirements": {"起钻": {"min_samples": 200, "preferred_samples": 800, "positive_ratio": 0.25, "quality_threshold": 0.7}, "下钻": {"min_samples": 200, "preferred_samples": 800, "positive_ratio": 0.25, "quality_threshold": 0.7}, "正常钻进": {"min_samples": 500, "preferred_samples": 1500, "positive_ratio": 0.3, "quality_threshold": 0.65}, "正划眼": {"min_samples": 150, "preferred_samples": 600, "positive_ratio": 0.2, "quality_threshold": 0.7}, "倒划眼": {"min_samples": 100, "preferred_samples": 400, "positive_ratio": 0.15, "quality_threshold": 0.7}}, "balancing_strategy": {"method": "condition_aware", "max_samples_per_condition": 300, "quality_priority": true, "temporal_distribution": true}}, "验证规则": {"description": "段一致性验证规则", "critical_issues": ["condition_mismatch", "parameter_out_of_range", "depth_change_inconsistent"], "warning_issues": ["condition_inconsistency", "parameter_anomaly", "time_discontinuity"], "severity_thresholds": {"high": 0.3, "medium": 0.6, "low": 0.8}}}
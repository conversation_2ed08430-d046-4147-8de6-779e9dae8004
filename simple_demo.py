#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的人工标注演示
展示正确的卡钻预警数据处理思路
"""

import pandas as pd
import numpy as np
from pathlib import Path

def create_simple_drilling_data():
    """
    创建简单的钻井数据示例
    """
    print("🔧 创建钻井数据示例...")
    
    n_points = 300
    
    # 创建基础数据
    data = {
        'DEP': np.linspace(2970, 2975, n_points),
        'BITDEP': np.linspace(2970, 2975, n_points),
        'WOB': np.random.normal(160, 20, n_points),
        'RPM': np.random.normal(105, 5, n_points),
        'HKLD': np.random.normal(970, 25, n_points),
        'TOR': np.random.normal(18, 3, n_points),
        'SPP': np.random.normal(25.8, 2, n_points),
        'timestamp': pd.date_range('2022-06-23 18:00:00', periods=n_points, freq='10s')
    }
    
    # 创建工况标签
    conditions = []
    for i in range(n_points):
        if i < 100:
            conditions.append('正常钻进')
        elif i < 150:
            conditions.append('起钻')
        elif i < 200:
            conditions.append('坐卡')  # 注意：这只是工况，不是真正的卡钻
        else:
            conditions.append('下钻')
    
    data['CW'] = conditions
    
    # 在某些区域添加异常（这些才是真正的卡钻前兆）
    # 异常区域1: 80-120 (正常钻进过程中的异常)
    for i in range(80, 120):
        data['WOB'][i] += np.random.normal(50, 20)  # 钻压异常升高
        data['TOR'][i] += np.random.normal(10, 5)   # 扭矩异常升高
        data['RPM'][i] -= np.random.normal(20, 5)   # 转速下降
    
    # 异常区域2: 250-290 (下钻过程中的异常)
    for i in range(250, 290):
        data['HKLD'][i] += np.random.normal(100, 30)  # 大钩载荷异常
        data['SPP'][i] += np.random.normal(10, 3)     # 立管压力异常
    
    df = pd.DataFrame(data)
    
    print(f"✅ 创建了 {n_points} 个数据点")
    print(f"📊 工况分布: {df['CW'].value_counts().to_dict()}")
    
    # 标记真实的异常区域（用于后续验证）
    true_anomalies = [
        {'start': 80, 'end': 120, 'description': '正常钻进中的异常'},
        {'start': 250, 'end': 290, 'description': '下钻中的异常'}
    ]
    
    return df, true_anomalies

def explain_correct_approach():
    """
    解释正确的卡钻预警方法
    """
    print("\n" + "="*70)
    print("📚 正确的卡钻预警方法说明")
    print("="*70)
    
    print("""
🎯 关键理解：

❌ 错误理解：
   • 认为"坐卡"工况标签 = 卡钻事件
   • 直接用工况标签作为正负样本
   • 忽略了工况只是操作类型，不是异常指标

✅ 正确理解：
   • "坐卡"、"起钻"、"下钻" = 正常的钻井操作工况
   • 真正的卡钻 = 钻井参数出现异常变化
   • 需要人工识别参数异常，而不是依赖工况标签

🔍 卡钻识别依据：

1. 钻压 (WOB) 异常：
   - 突然大幅升高
   - 剧烈波动
   - 与其他参数不协调

2. 扭矩 (TOR) 异常：
   - 异常增大
   - 突然下降到零
   - 波动剧烈

3. 转速 (RPM) 异常：
   - 异常下降
   - 停转
   - 不稳定

4. 大钩载荷 (HKLD) 异常：
   - 异常增高（阻力增大）
   - 与钻压变化不匹配

5. 立管压力 (SPP) 异常：
   - 异常升高或下降
   - 与循环系统不匹配

⚖️ 正负样本定义：

🔴 正样本 (卡钻前兆)：
   • 钻井参数出现上述异常组合的时间段
   • 不管当时的工况标签是什么
   • 例如：在"正常钻进"工况下，WOB和TOR同时异常升高

🔵 负样本 (正常操作)：
   • 钻井参数正常变化的时间段
   • 各参数协调一致
   • 例如：在"起钻"工况下，各参数按预期变化

🔧 数据处理流程：

1. 加载原始钻井数据
2. 创建滑动时间窗口
3. 人工分析每个窗口的参数异常情况
4. 标注：0=正常，1=卡钻前兆
5. 创建平衡的训练数据集
6. 训练预警模型

💡 关键点：
   • 工况标签仅供参考，不是判断依据
   • 重点关注参数的异常变化模式
   • 需要结合多个参数综合判断
   • 人工经验是关键
    """)

def demonstrate_window_analysis():
    """
    演示窗口分析方法
    """
    print("\n" + "="*70)
    print("🔍 窗口分析演示")
    print("="*70)
    
    # 创建示例数据
    df, true_anomalies = create_simple_drilling_data()
    
    # 保存示例数据
    df.to_csv("drilling_sample.csv", index=False, encoding='utf-8-sig')
    print(f"✅ 示例数据已保存: drilling_sample.csv")
    
    # 演示几个典型窗口的分析
    window_size = 50
    
    print(f"\n📊 分析几个典型的 {window_size} 点窗口:")
    
    # 窗口1: 正常区域 (20-70)
    analyze_window(df, 20, 70, "正常区域", expected_label=0)
    
    # 窗口2: 异常区域 (90-140) - 包含第一个异常段
    analyze_window(df, 90, 140, "异常区域1", expected_label=1)
    
    # 窗口3: 工况切换区域 (140-190) - 从起钻到坐卡
    analyze_window(df, 140, 190, "工况切换区域", expected_label=0)
    
    # 窗口4: 第二个异常区域 (260-310)
    analyze_window(df, 260, 310, "异常区域2", expected_label=1)
    
    print(f"\n💡 分析总结:")
    print(f"• 窗口1: 参数正常，标注为 0 (正常)")
    print(f"• 窗口2: WOB和TOR异常升高，RPM下降，标注为 1 (卡钻前兆)")
    print(f"• 窗口3: 虽然工况从'起钻'变为'坐卡'，但参数正常，标注为 0 (正常)")
    print(f"• 窗口4: HKLD和SPP异常，标注为 1 (卡钻前兆)")

def analyze_window(df: pd.DataFrame, start: int, end: int, description: str, expected_label: int):
    """
    分析单个窗口
    """
    window_data = df.iloc[start:end]
    
    print(f"\n📋 {description} (索引 {start}-{end}):")
    
    # 工况分布
    condition_counts = window_data['CW'].value_counts()
    dominant_condition = condition_counts.index[0]
    print(f"  主要工况: {dominant_condition}")
    
    # 参数统计
    params = ['WOB', 'RPM', 'HKLD', 'TOR', 'SPP']
    print(f"  参数统计:")
    
    for param in params:
        if param in window_data.columns:
            mean_val = window_data[param].mean()
            std_val = window_data[param].std()
            print(f"    {param}: 均值={mean_val:.1f}, 标准差={std_val:.1f}")
    
    # 异常检测（简化版）
    anomaly_score = 0
    if window_data['WOB'].mean() > 200:  # 钻压过高
        anomaly_score += 1
    if window_data['TOR'].mean() > 25:   # 扭矩过高
        anomaly_score += 1
    if window_data['RPM'].mean() < 90:   # 转速过低（排除起钻下钻）
        if dominant_condition not in ['起钻', '下钻']:
            anomaly_score += 1
    if window_data['HKLD'].mean() > 1050:  # 大钩载荷过高
        anomaly_score += 1
    if window_data['SPP'].mean() > 30:   # 立管压力过高
        anomaly_score += 1
    
    predicted_label = 1 if anomaly_score >= 2 else 0
    
    print(f"  异常评分: {anomaly_score}/5")
    print(f"  预测标签: {predicted_label} ({'卡钻前兆' if predicted_label == 1 else '正常'})")
    print(f"  期望标签: {expected_label} ({'卡钻前兆' if expected_label == 1 else '正常'})")
    print(f"  判断结果: {'✅ 正确' if predicted_label == expected_label else '❌ 错误'}")

def create_labeling_template():
    """
    创建实际的标注模板
    """
    print(f"\n🔧 创建实际标注模板...")
    
    # 加载数据
    df = pd.read_csv("drilling_sample.csv", encoding='utf-8-sig')
    
    # 创建滑动窗口
    window_size = 50
    step_size = 10
    windows = []
    
    for i in range(0, len(df) - window_size + 1, step_size):
        window_data = df.iloc[i:i + window_size]
        
        # 计算窗口统计信息
        window_info = {
            'window_id': len(windows),
            'start_index': i,
            'end_index': i + window_size - 1,
            'dominant_condition': window_data['CW'].mode().iloc[0],
            'condition_changes': (window_data['CW'] != window_data['CW'].shift()).sum(),
            'WOB_mean': round(window_data['WOB'].mean(), 2),
            'WOB_std': round(window_data['WOB'].std(), 2),
            'RPM_mean': round(window_data['RPM'].mean(), 2),
            'RPM_std': round(window_data['RPM'].std(), 2),
            'HKLD_mean': round(window_data['HKLD'].mean(), 2),
            'HKLD_std': round(window_data['HKLD'].std(), 2),
            'TOR_mean': round(window_data['TOR'].mean(), 2),
            'TOR_std': round(window_data['TOR'].std(), 2),
            'SPP_mean': round(window_data['SPP'].mean(), 2),
            'SPP_std': round(window_data['SPP'].std(), 2),
            'label': '',  # 待人工填写
            'confidence': '',  # 待人工填写
            'notes': ''  # 待人工填写
        }
        
        windows.append(window_info)
    
    # 保存标注模板
    template_df = pd.DataFrame(windows)
    template_file = "manual_labeling_template.csv"
    template_df.to_csv(template_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 标注模板已创建: {template_file}")
    print(f"📊 共 {len(windows)} 个窗口待标注")
    
    # 创建标注说明
    instructions = """# 钻井卡钻标注说明

## 标注任务
请根据钻井参数的异常情况，判断每个时间窗口是否包含卡钻前兆。

## 标注规则
- label列填写: 0=正常, 1=卡钻前兆
- confidence列填写: 1-5分 (5分最确定)
- notes列填写: 判断依据或特殊说明

## 判断依据
重点关注以下参数的异常组合:
1. WOB (钻压) > 200 或波动剧烈
2. TOR (扭矩) > 25 或异常变化
3. RPM (转速) < 90 (非起下钻工况)
4. HKLD (大钩载荷) > 1050
5. SPP (立管压力) > 30

## 注意事项
- 工况标签仅供参考，不是判断依据
- 重点关注参数异常组合，不是单一参数
- 考虑参数变化趋势，不只是绝对值
"""
    
    with open("标注说明.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✅ 标注说明已创建: 标注说明.md")
    
    return template_file

def main():
    """
    主演示函数
    """
    print("🎯 正确的卡钻预警数据处理方法")
    print("=" * 60)
    
    # 1. 解释正确方法
    explain_correct_approach()
    
    # 2. 演示窗口分析
    demonstrate_window_analysis()
    
    # 3. 创建实际标注模板
    template_file = create_labeling_template()
    
    print(f"\n" + "="*60)
    print("🎉 演示完成!")
    print("="*60)
    
    print(f"\n📁 生成的文件:")
    print(f"  - 示例数据: drilling_sample.csv")
    print(f"  - 标注模板: {template_file}")
    print(f"  - 标注说明: 标注说明.md")
    
    print(f"\n🚀 下一步操作:")
    print(f"1. 打开 {template_file} 进行人工标注")
    print(f"2. 参考 标注说明.md 中的判断依据")
    print(f"3. 完成标注后，使用标注结果训练模型")
    
    print(f"\n💡 关键要点:")
    print(f"• 工况标签 ≠ 卡钻事件")
    print(f"• 关注参数异常，不是工况类型")
    print(f"• 人工经验是标注的关键")
    print(f"• 多参数组合判断比单参数更可靠")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量验证器
用于验证钻进数据的质量，确保训练数据满足模型要求

作者: TSlib项目组
日期: 2024-06-29
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class DataQualityValidator:
    """数据质量验证器"""
    
    def __init__(self):
        # 钻进参数的合理范围
        self.parameter_ranges = {
            'DEP': (0, 10000),      # 井深范围 m
            'BITDEP': (0, 10000),   # 钻头深度范围 m
            'WOB': (0, 500),        # 钻压范围 kN
            'HKLD': (0, 2000),      # 大钩载荷范围 kN
            'RPM': (0, 300),        # 转速范围 rpm
            'TOR': (0, 100),        # 扭矩范围 kN·m
            'SPP': (0, 50),         # 立管压力范围 MPa
            'CSIP': (0, 50),        # 套管压力范围 MPa
            'HOKHEI': (0, 50),      # 大钩高度范围 m
            'DRITIME': (0, 24*3600) # 钻进时间范围 s
        }
        
        # 数据质量标准
        self.quality_standards = {
            'min_sequence_length': 50,          # 最小序列长度
            'max_sequence_length': 1000,        # 最大序列长度
            'missing_value_threshold': 0.1,     # 缺失值比例阈值
            'outlier_threshold': 0.05,          # 异常值比例阈值
            'required_features': ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP'],
            'sampling_frequency_tolerance': 0.2  # 采样频率容忍度
        }
        
        self.validation_results = {}
    
    def validate_dataset(self, data_path: str) -> Dict:
        """验证整个数据集的质量"""
        print("🔍 开始数据质量验证...")
        
        data_path = Path(data_path)
        if not data_path.exists():
            raise FileNotFoundError(f"数据路径不存在: {data_path}")
        
        # 收集所有CSV文件
        csv_files = list(data_path.rglob("*.csv"))
        print(f"📁 发现 {len(csv_files)} 个CSV文件")
        
        validation_summary = {
            'total_files': len(csv_files),
            'valid_files': 0,
            'invalid_files': 0,
            'file_results': {},
            'overall_stats': {},
            'quality_issues': []
        }
        
        valid_sequences = []
        invalid_files = []
        
        for i, file_path in enumerate(csv_files):
            if i % 100 == 0:
                print(f"  处理进度: {i}/{len(csv_files)}")
            
            try:
                # 验证单个文件
                file_result = self.validate_single_file(file_path)
                validation_summary['file_results'][str(file_path)] = file_result
                
                if file_result['is_valid']:
                    validation_summary['valid_files'] += 1
                    valid_sequences.append(file_result['sequence_stats'])
                else:
                    validation_summary['invalid_files'] += 1
                    invalid_files.append(str(file_path))
                    
            except Exception as e:
                validation_summary['invalid_files'] += 1
                invalid_files.append(str(file_path))
                validation_summary['file_results'][str(file_path)] = {
                    'is_valid': False,
                    'error': str(e)
                }
        
        # 计算整体统计信息
        if valid_sequences:
            validation_summary['overall_stats'] = self._calculate_overall_stats(valid_sequences)
        
        # 生成质量报告
        self._generate_quality_report(validation_summary)
        
        print(f"✅ 数据质量验证完成")
        print(f"  有效文件: {validation_summary['valid_files']}")
        print(f"  无效文件: {validation_summary['invalid_files']}")
        print(f"  有效率: {validation_summary['valid_files']/validation_summary['total_files']:.2%}")
        
        return validation_summary
    
    def validate_single_file(self, file_path: Path) -> Dict:
        """验证单个文件的数据质量"""
        try:
            # 尝试不同编码读取文件
            data = None
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    data = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if data is None:
                return {'is_valid': False, 'error': '无法读取文件'}
            
            # 基础检查
            basic_checks = self._perform_basic_checks(data)
            
            # 特征检查
            feature_checks = self._perform_feature_checks(data)
            
            # 时间序列检查
            temporal_checks = self._perform_temporal_checks(data)
            
            # 数据范围检查
            range_checks = self._perform_range_checks(data)
            
            # 综合评估
            all_checks = {**basic_checks, **feature_checks, **temporal_checks, **range_checks}
            is_valid = all(all_checks.values())
            
            # 计算序列统计信息
            sequence_stats = self._calculate_sequence_stats(data)
            
            return {
                'is_valid': is_valid,
                'checks': all_checks,
                'sequence_stats': sequence_stats,
                'data_shape': data.shape,
                'file_size_mb': file_path.stat().st_size / (1024*1024)
            }
            
        except Exception as e:
            return {'is_valid': False, 'error': str(e)}
    
    def _perform_basic_checks(self, data: pd.DataFrame) -> Dict[str, bool]:
        """执行基础数据检查"""
        checks = {}
        
        # 检查数据是否为空
        checks['not_empty'] = len(data) > 0
        
        # 检查序列长度
        checks['sequence_length_valid'] = (
            len(data) >= self.quality_standards['min_sequence_length'] and
            len(data) <= self.quality_standards['max_sequence_length']
        )
        
        # 检查是否有必需的特征
        required_features = self.quality_standards['required_features']
        missing_features = [f for f in required_features if f not in data.columns]
        checks['required_features_present'] = len(missing_features) == 0
        
        return checks
    
    def _perform_feature_checks(self, data: pd.DataFrame) -> Dict[str, bool]:
        """执行特征质量检查"""
        checks = {}
        
        # 检查缺失值比例
        missing_ratio = data.isnull().sum().sum() / (data.shape[0] * data.shape[1])
        checks['missing_values_acceptable'] = missing_ratio <= self.quality_standards['missing_value_threshold']
        
        # 检查数值特征的有效性
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        checks['numeric_features_valid'] = len(numeric_cols) >= 7  # 至少7个数值特征
        
        return checks
    
    def _perform_temporal_checks(self, data: pd.DataFrame) -> Dict[str, bool]:
        """执行时间序列检查"""
        checks = {}
        
        # 检查时间列（如果存在）
        if 'date' in data.columns:
            try:
                # 尝试解析时间
                time_series = pd.to_datetime(data['date'])
                
                # 检查时间序列是否单调递增
                checks['time_monotonic'] = time_series.is_monotonic_increasing
                
                # 检查采样频率的一致性
                if len(time_series) > 1:
                    time_diffs = time_series.diff().dropna()
                    median_diff = time_diffs.median()
                    
                    # 检查时间间隔的一致性
                    consistent_intervals = (
                        (time_diffs >= median_diff * (1 - self.quality_standards['sampling_frequency_tolerance'])) &
                        (time_diffs <= median_diff * (1 + self.quality_standards['sampling_frequency_tolerance']))
                    ).sum() / len(time_diffs)
                    
                    checks['sampling_frequency_consistent'] = consistent_intervals >= 0.8
                else:
                    checks['sampling_frequency_consistent'] = True
                    
            except:
                checks['time_monotonic'] = False
                checks['sampling_frequency_consistent'] = False
        else:
            # 如果没有时间列，假设数据是按时间顺序排列的
            checks['time_monotonic'] = True
            checks['sampling_frequency_consistent'] = True
        
        return checks
    
    def _perform_range_checks(self, data: pd.DataFrame) -> Dict[str, bool]:
        """执行数据范围检查"""
        checks = {}
        
        outlier_count = 0
        total_values = 0
        
        for param, (min_val, max_val) in self.parameter_ranges.items():
            if param in data.columns:
                param_data = data[param].dropna()
                if len(param_data) > 0:
                    # 计算超出范围的值
                    out_of_range = ((param_data < min_val) | (param_data > max_val)).sum()
                    outlier_count += out_of_range
                    total_values += len(param_data)
        
        # 检查异常值比例
        if total_values > 0:
            outlier_ratio = outlier_count / total_values
            checks['outliers_acceptable'] = outlier_ratio <= self.quality_standards['outlier_threshold']
        else:
            checks['outliers_acceptable'] = True
        
        return checks
    
    def _calculate_sequence_stats(self, data: pd.DataFrame) -> Dict:
        """计算序列统计信息"""
        stats = {
            'length': len(data),
            'feature_count': len(data.columns),
            'missing_ratio': data.isnull().sum().sum() / (data.shape[0] * data.shape[1]),
            'numeric_feature_count': len(data.select_dtypes(include=[np.number]).columns)
        }
        
        # 计算各参数的基本统计量
        for param in self.parameter_ranges.keys():
            if param in data.columns:
                param_data = data[param].dropna()
                if len(param_data) > 0:
                    stats[f'{param}_mean'] = param_data.mean()
                    stats[f'{param}_std'] = param_data.std()
                    stats[f'{param}_min'] = param_data.min()
                    stats[f'{param}_max'] = param_data.max()
        
        return stats
    
    def _calculate_overall_stats(self, valid_sequences: List[Dict]) -> Dict:
        """计算整体统计信息"""
        if not valid_sequences:
            return {}
        
        # 转换为DataFrame便于计算
        stats_df = pd.DataFrame(valid_sequences)
        
        overall_stats = {
            'total_valid_sequences': len(valid_sequences),
            'avg_sequence_length': stats_df['length'].mean(),
            'median_sequence_length': stats_df['length'].median(),
            'min_sequence_length': stats_df['length'].min(),
            'max_sequence_length': stats_df['length'].max(),
            'avg_missing_ratio': stats_df['missing_ratio'].mean(),
            'avg_feature_count': stats_df['feature_count'].mean()
        }
        
        return overall_stats
    
    def _generate_quality_report(self, validation_summary: Dict):
        """生成数据质量报告"""
        report_path = Path('./logs/data_quality_report.txt')
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("数据质量验证报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"总文件数: {validation_summary['total_files']}\n")
            f.write(f"有效文件数: {validation_summary['valid_files']}\n")
            f.write(f"无效文件数: {validation_summary['invalid_files']}\n")
            f.write(f"有效率: {validation_summary['valid_files']/validation_summary['total_files']:.2%}\n\n")
            
            if validation_summary['overall_stats']:
                f.write("整体统计信息:\n")
                for key, value in validation_summary['overall_stats'].items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")
            
            # 写入质量问题
            if validation_summary['invalid_files'] > 0:
                f.write("质量问题文件:\n")
                invalid_count = 0
                for file_path, result in validation_summary['file_results'].items():
                    if not result.get('is_valid', False):
                        f.write(f"  {file_path}: {result.get('error', '未知错误')}\n")
                        invalid_count += 1
                        if invalid_count >= 20:  # 只显示前20个
                            f.write(f"  ... 还有 {validation_summary['invalid_files'] - 20} 个文件\n")
                            break
        
        print(f"📄 数据质量报告已保存: {report_path}")

# 使用示例
if __name__ == "__main__":
    validator = DataQualityValidator()
    
    # 验证数据集
    results = validator.validate_dataset('./dataset/earlysignaldetection')
    
    print("\n📊 验证结果摘要:")
    print(f"  总文件数: {results['total_files']}")
    print(f"  有效文件数: {results['valid_files']}")
    print(f"  无效文件数: {results['invalid_files']}")
    print(f"  数据质量: {results['valid_files']/results['total_files']:.1%}")

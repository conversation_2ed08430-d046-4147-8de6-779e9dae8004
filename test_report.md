# 工况专用模型测试报告

测试时间: D:\PyCharm\kazuan\kazuan-6.29

## 测试结果总结

| 工况 | 测试准确率 | 精确率 | 召回率 | F1分数 | 训练最佳验证准确率 |
|------|------------|--------|--------|--------|-----------------|
| 正常钻进 | 0.9950 | 0.9950 | 0.9950 | 0.9944 | 0.9962 |
| 起钻 | 1.0000 | 1.0000 | 1.0000 | 1.0000 | 0.9963 |
| 下钻 | 1.0000 | 1.0000 | 1.0000 | 1.0000 | 1.0000 |
| 正划眼 | 0.9801 | 0.9805 | 0.9801 | 0.9764 | 0.9761 |

## 统计信息

- 测试的模型数量: 4
- 平均测试准确率: 0.9938
- 平均F1分数: 0.9927

## 详细结果

### 正常钻进

- 测试损失: 0.1659
- 测试准确率: 0.9950
- 精确率: 0.9950
- 召回率: 0.9950
- F1分数: 0.9944
- 真实标签分布: {0: 789, 1: 10}
- 预测标签分布: {0: 793, 1: 6}

### 起钻

- 测试损失: 0.0020
- 测试准确率: 1.0000
- 精确率: 1.0000
- 召回率: 1.0000
- F1分数: 1.0000
- 真实标签分布: {0: 266, 1: 2}
- 预测标签分布: {0: 266, 1: 2}

### 下钻

- 测试损失: 0.0279
- 测试准确率: 1.0000
- 精确率: 1.0000
- 召回率: 1.0000
- F1分数: 1.0000
- 真实标签分布: {0: 85}
- 预测标签分布: {0: 85}

### 正划眼

- 测试损失: 0.0848
- 测试准确率: 0.9801
- 精确率: 0.9805
- 召回率: 0.9801
- F1分数: 0.9764
- 真实标签分布: {0: 242, 1: 9}
- 预测标签分布: {0: 247, 1: 4}


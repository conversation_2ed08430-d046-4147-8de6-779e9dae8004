#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的钻井数据导出器
专门用于构建钻井卡钻预警模型的训练和测试数据集

基于钻井数据处理最佳实践：
1. 征兆数据：卡钻事件前15分钟（异常样本）
2. 测试数据：卡钻事件前24小时（模型验证）
3. 正常数据：卡钻事件前7-30天（正常样本）
4. 工况字段清洗和标准化
5. 数据质量控制和验证
"""

import sqlite3
import csv
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os
import warnings
warnings.filterwarnings('ignore')

class OptimizedDrillingDataExporter:
    """
    优化的钻井数据导出器
    """
    
    def __init__(self, base_path: str, output_base_dir: str):
        self.base_path = Path(base_path)
        self.output_base_dir = Path(output_base_dir)
        
        # 数据导出配置
        self.data_types = {
            'symptom': {
                'name': '征兆数据',
                'time_before_event': timedelta(minutes=15),  # 卡钻前15分钟
                'description': '用于异常样本训练'
            },
            'test': {
                'name': '测试数据', 
                'time_before_event': timedelta(hours=24),    # 卡钻前24小时
                'description': '用于模型性能验证'
            },
            'normal': {
                'name': '正常数据',
                'time_before_event': (timedelta(days=7), timedelta(days=30)),  # 卡钻前7-30天
                'description': '用于正常样本训练'
            }
        }
        
        # 必需的数据字段
        self.required_fields = [
            'DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 
            'RPM', 'TOR', 'SPP', 'CSIP', 'RIGSTA'
        ]
        
        # 可选字段
        self.optional_fields = ['FLOWIN', 'FLOWOUT']
        
        # 工况字段映射（用于标准化）
        self.rigsta_mapping = {
            # 标准工况映射 - 根据实际数据库情况调整
            '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT'],
            '下钻': ['下钻', '下放', '下钻具', 'TRIP_IN'], 
            '正常钻进': ['钻进', '正常钻进', '钻井', 'DRILLING'],
            '正划眼': ['正划眼', '划眼', '扩眼', 'REAMING'],
            '倒划眼': ['倒划眼', '反划眼', 'BACK_REAMING'],
            '坐卡': ['坐卡', '卡钻', 'STUCK'],
            '循环': ['循环', '洗井', 'CIRCULATION'],
            '接单根': ['接单根', '接立柱', 'CONNECTION'],
            '其他': ['其他', '未知', 'OTHER', '']
        }
        
        print(f"🔧 初始化钻井数据导出器")
        print(f"   - 数据库根路径: {self.base_path}")
        print(f"   - 输出根目录: {self.output_base_dir}")
        print(f"   - 支持数据类型: {list(self.data_types.keys())}")
    
    def standardize_rigsta(self, rigsta_value: str) -> str:
        """
        标准化工况字段
        """
        if pd.isna(rigsta_value) or rigsta_value == '':
            return '其他'
        
        rigsta_str = str(rigsta_value).strip()
        
        # 查找匹配的标准工况
        for standard_condition, variants in self.rigsta_mapping.items():
            if rigsta_str in variants:
                return standard_condition
        
        # 模糊匹配
        for standard_condition, variants in self.rigsta_mapping.items():
            for variant in variants:
                if variant in rigsta_str or rigsta_str in variant:
                    return standard_condition
        
        return '其他'
    
    def load_stuck_events_from_csv(self, csv_file: str) -> List[Dict]:
        """
        从CSV文件加载卡钻事件信息
        
        CSV格式要求：
        - 井名: 井的名称
        - 卡钻时间: 卡钻发生的时间 (YYYY-MM-DD HH:MM:SS)
        - 事件描述: 可选，事件的描述信息
        """
        events = []
        
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 检查必要列
            required_columns = ['井名', '卡钻时间']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ CSV文件缺少必要列: {missing_columns}")
                return events
            
            for _, row in df.iterrows():
                try:
                    event_time = pd.to_datetime(row['卡钻时间'])
                    events.append({
                        'well_name': str(row['井名']).strip(),
                        'stuck_time': event_time,
                        'description': str(row.get('事件描述', '')).strip()
                    })
                except Exception as e:
                    print(f"⚠️ 跳过无效行: {row.to_dict()}, 错误: {e}")
            
            print(f"✅ 从CSV加载了 {len(events)} 个卡钻事件")
            
        except Exception as e:
            print(f"❌ 加载卡钻事件CSV失败: {e}")
        
        return events
    
    def get_db_files_for_time_range(self, well_path: Path, start_time: datetime, end_time: datetime) -> List[Path]:
        """
        获取时间范围内需要查询的数据库文件
        """
        db_files = []
        
        # 生成需要查询的月份
        current = datetime(start_time.year, start_time.month, 1)
        end_month = datetime(end_time.year, end_time.month, 1)
        
        while current <= end_month:
            # 数据库文件名格式: 0YYMM.db
            db_filename = f"0{current.strftime('%y%m')}.db"
            db_path = well_path / db_filename
            
            if db_path.exists():
                db_files.append(db_path)
            else:
                print(f"⚠️ 数据库文件不存在: {db_path}")
            
            # 移动到下个月
            if current.month == 12:
                current = datetime(current.year + 1, 1, 1)
            else:
                current = datetime(current.year, current.month + 1, 1)
        
        return db_files
    
    def extract_data_from_db(self, db_files: List[Path], start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        从数据库文件中提取指定时间范围的数据
        """
        all_data = []
        
        for db_file in db_files:
            try:
                conn = sqlite3.connect(str(db_file))
                
                # 构建SQL查询
                fields = self.required_fields + self.optional_fields
                field_str = ', '.join(fields)
                
                sql = f"""
                SELECT {field_str}, (WELLDATE || ' ' || WELLTIME) AS date
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}' 
                    AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """
                
                df = pd.read_sql_query(sql, conn)
                
                if not df.empty:
                    all_data.append(df)
                    print(f"  📊 从 {db_file.name} 提取 {len(df)} 条记录")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 查询数据库失败 {db_file}: {e}")
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            # 按时间排序
            combined_df['date'] = pd.to_datetime(combined_df['date'])
            combined_df = combined_df.sort_values('date').reset_index(drop=True)
            
            # 标准化工况字段
            if 'RIGSTA' in combined_df.columns:
                combined_df['RIGSTA'] = combined_df['RIGSTA'].apply(self.standardize_rigsta)
            
            return combined_df
        
        return pd.DataFrame()
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """
        验证数据质量
        """
        quality_report = {
            'total_records': len(df),
            'missing_data': {},
            'time_gaps': [],
            'rigsta_distribution': {},
            'quality_score': 0.0
        }
        
        if df.empty:
            return quality_report
        
        # 检查缺失数据
        for field in self.required_fields:
            if field in df.columns:
                missing_count = df[field].isnull().sum()
                missing_ratio = missing_count / len(df)
                quality_report['missing_data'][field] = {
                    'count': int(missing_count),
                    'ratio': float(missing_ratio)
                }
        
        # 检查时间间隔
        if 'date' in df.columns:
            time_diffs = df['date'].diff().dt.total_seconds()
            large_gaps = time_diffs[time_diffs > 300]  # 超过5分钟的间隔
            quality_report['time_gaps'] = [float(gap) for gap in large_gaps.values if not pd.isna(gap)]
        
        # 工况分布
        if 'RIGSTA' in df.columns:
            rigsta_counts = df['RIGSTA'].value_counts()
            quality_report['rigsta_distribution'] = rigsta_counts.to_dict()
        
        # 计算质量分数
        missing_penalty = sum([info['ratio'] for info in quality_report['missing_data'].values()]) / len(self.required_fields)
        time_gap_penalty = min(len(quality_report['time_gaps']) / len(df), 0.5)
        quality_report['quality_score'] = max(0.0, 1.0 - missing_penalty - time_gap_penalty)
        
        return quality_report

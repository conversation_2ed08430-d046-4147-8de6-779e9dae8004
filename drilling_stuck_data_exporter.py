#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
钻井卡钻数据导出器
专门用于构建钻井卡钻预警模型的训练和测试数据集

基于钻井数据处理最佳实践：
1. 征兆数据：卡钻事件前15分钟（异常样本）
2. 测试数据：卡钻事件前24小时（模型验证）
3. 正常数据：卡钻事件前7-30天（正常样本）
4. 工况字段清洗和标准化
5. 数据质量控制和验证
"""

import sqlite3
import csv
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os
import warnings
warnings.filterwarnings('ignore')

class DrillingStuckDataExporter:
    """
    钻井卡钻数据导出器
    """
    
    def __init__(self, base_path: str, output_base_dir: str):
        self.base_path = Path(base_path)
        self.output_base_dir = Path(output_base_dir)
        
        # 数据导出配置
        self.export_configs = {
            'symptom': {
                'name': '征兆数据',
                'time_before_minutes': 15,  # 卡钻前15分钟
                'folder': 'symptom_data',
                'description': '用于异常样本训练'
            },
            'test': {
                'name': '测试数据', 
                'time_before_hours': 24,    # 卡钻前24小时
                'folder': 'test_data',
                'description': '用于模型性能验证'
            },
            'normal': {
                'name': '正常数据',
                'time_before_days_start': 7,   # 卡钻前7天开始
                'time_before_days_end': 30,    # 卡钻前30天结束
                'folder': 'normal_data',
                'description': '用于正常样本训练'
            }
        }
        
        # 必需的数据字段（基于您选中的代码）
        self.required_fields = [
            'DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 
            'RPM', 'TOR', 'SPP', 'CSIP', 'RIGSTA'
        ]
        
        # 可选字段
#        self.optional_fields = ['FLOWIN', 'FLOWOUT']
        
        # 工况字段标准化映射
        self.rigsta_mapping = {
            # 根据实际数据库情况调整这些映射
            '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT', '起', '上'],
            '下钻': ['下钻', '下放', '下钻具', 'TRIP_IN', '下', '入'],
            '正常钻进': ['钻进', '正常钻进', '钻井', 'DRILLING', '钻', '进'],
            '正划眼': ['正划眼', '划眼', '扩眼', 'REAMING', '划', '扩'],
            '倒划眼': ['倒划眼', '反划眼', 'BACK_REAMING', '倒', '反'],
            '坐卡': ['坐卡', '卡钻', 'STUCK', '卡', '坐'],
            '循环': ['循环', '洗井', 'CIRCULATION', '洗', '环'],
            '接单根': ['接单根', '接立柱', 'CONNECTION', '接', '连'],
            '其他': ['其他', '未知', 'OTHER', '', None]
        }
        
        # 创建输出目录
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔧 初始化钻井卡钻数据导出器")
        print(f"   - 数据库根路径: {self.base_path}")
        print(f"   - 输出根目录: {self.output_base_dir}")
    
    def standardize_rigsta(self, rigsta_value) -> str:
        """
        标准化工况字段
        """
        if pd.isna(rigsta_value) or rigsta_value == '' or rigsta_value is None:
            return '其他'
        
        rigsta_str = str(rigsta_value).strip()
        
        # 精确匹配
        for standard_condition, variants in self.rigsta_mapping.items():
            if rigsta_str in variants:
                return standard_condition
        
        # 模糊匹配（包含关系）
        for standard_condition, variants in self.rigsta_mapping.items():
            for variant in variants:
                if variant and (variant in rigsta_str or rigsta_str in variant):
                    return standard_condition
        
        # 如果都不匹配，记录原始值并归类为其他
        print(f"⚠️ 未识别的工况值: '{rigsta_str}' -> 归类为'其他'")
        return '其他'
    
    def load_stuck_events_from_csv(self, csv_file: str) -> List[Dict]:
        """
        从CSV文件加载卡钻事件信息
        
        CSV格式要求：
        - 井名: 井的名称
        - 卡钻时间: 卡钻发生的时间 (YYYY-MM-DD HH:MM:SS)
        - 事件描述: 可选，事件的描述信息
        """
        events = []
        
        if not os.path.exists(csv_file):
            print(f"❌ 卡钻事件CSV文件不存在: {csv_file}")
            return events
        
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    print(f"✅ 使用 {encoding} 编码成功读取CSV")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                print(f"❌ 无法读取CSV文件，尝试了所有编码")
                return events
            
            # 检查必要列
            required_columns = ['井名', '卡钻时间']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ CSV文件缺少必要列: {missing_columns}")
                print(f"   实际列名: {list(df.columns)}")
                return events
            
            for _, row in df.iterrows():
                try:
                    # 解析时间
                    stuck_time_str = str(row['卡钻时间']).strip()
                    
                    # 尝试多种时间格式
                    time_formats = [
                        '%Y-%m-%d %H:%M:%S',
                        '%Y/%m/%d %H:%M:%S',
                        '%Y-%m-%d %H:%M',
                        '%Y/%m/%d %H:%M',
                        '%Y-%m-%d',
                        '%Y/%m/%d'
                    ]
                    
                    stuck_time = None
                    for fmt in time_formats:
                        try:
                            stuck_time = datetime.strptime(stuck_time_str, fmt)
                            break
                        except ValueError:
                            continue
                    
                    if stuck_time is None:
                        print(f"⚠️ 无法解析时间格式: {stuck_time_str}")
                        continue
                    
                    events.append({
                        'well_name': str(row['井名']).strip(),
                        'stuck_time': stuck_time,
                        'description': str(row.get('事件描述', '')).strip()
                    })
                    
                except Exception as e:
                    print(f"⚠️ 跳过无效行: {row.to_dict()}, 错误: {e}")
            
            print(f"✅ 从CSV加载了 {len(events)} 个卡钻事件")
            
        except Exception as e:
            print(f"❌ 加载卡钻事件CSV失败: {e}")
        
        return events
    
    def get_db_files_for_time_range(self, well_path: Path, start_time: datetime, end_time: datetime) -> List[Path]:
        """
        获取时间范围内需要查询的数据库文件
        """
        db_files = []
        
        # 生成需要查询的月份
        current = datetime(start_time.year, start_time.month, 1)
        end_month = datetime(end_time.year, end_time.month, 1)
        
        while current <= end_month:
            # 数据库文件名格式: 0YYMM.db
            db_filename = f"0{current.strftime('%y%m')}.db"
            db_path = well_path / db_filename
            
            if db_path.exists():
                db_files.append(db_path)
            else:
                print(f"⚠️ 数据库文件不存在: {db_path}")
            
            # 移动到下个月
            if current.month == 12:
                current = datetime(current.year + 1, 1, 1)
            else:
                current = datetime(current.year, current.month + 1, 1)
        
        return db_files
    
    def extract_data_from_db(self, db_files: List[Path], start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        从数据库文件中提取指定时间范围的数据
        """
        all_data = []
        
        for db_file in db_files:
            try:
                conn = sqlite3.connect(str(db_file))
                
                # 构建SQL查询 - 使用您选中的字段
                fields = self.required_fields + self.optional_fields
                field_str = ', '.join(fields)
                
                sql = f"""
                SELECT {field_str}, (WELLDATE || ' ' || WELLTIME) AS date
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                    BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}' 
                    AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """
                
                df = pd.read_sql_query(sql, conn)
                
                if not df.empty:
                    all_data.append(df)
                    print(f"  📊 从 {db_file.name} 提取 {len(df)} 条记录")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 查询数据库失败 {db_file}: {e}")
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            # 按时间排序
            combined_df['date'] = pd.to_datetime(combined_df['date'])
            combined_df = combined_df.sort_values('date').reset_index(drop=True)
            
            # 标准化工况字段
            if 'RIGSTA' in combined_df.columns:
                combined_df['RIGSTA'] = combined_df['RIGSTA'].apply(self.standardize_rigsta)
            
            return combined_df
        
        return pd.DataFrame()
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """
        验证数据质量
        """
        quality_report = {
            'total_records': len(df),
            'missing_data': {},
            'time_gaps': [],
            'rigsta_distribution': {},
            'quality_score': 0.0,
            'issues': []
        }
        
        if df.empty:
            quality_report['issues'].append('数据为空')
            return quality_report
        
        # 检查缺失数据
        for field in self.required_fields:
            if field in df.columns:
                missing_count = df[field].isnull().sum()
                missing_ratio = missing_count / len(df)
                quality_report['missing_data'][field] = {
                    'count': int(missing_count),
                    'ratio': float(missing_ratio)
                }
                
                if missing_ratio > 0.1:  # 超过10%缺失
                    quality_report['issues'].append(f'{field}字段缺失率过高: {missing_ratio:.2%}')
        
        # 检查时间间隔
        if 'date' in df.columns and len(df) > 1:
            time_diffs = df['date'].diff().dt.total_seconds()
            large_gaps = time_diffs[time_diffs > 300]  # 超过5分钟的间隔
            quality_report['time_gaps'] = [float(gap) for gap in large_gaps.values if not pd.isna(gap)]
            
            if len(quality_report['time_gaps']) > len(df) * 0.1:
                quality_report['issues'].append(f'时间间隔异常过多: {len(quality_report["time_gaps"])} 个')
        
        # 工况分布
        if 'RIGSTA' in df.columns:
            rigsta_counts = df['RIGSTA'].value_counts()
            quality_report['rigsta_distribution'] = rigsta_counts.to_dict()
        
        # 计算质量分数
        missing_penalty = sum([info['ratio'] for info in quality_report['missing_data'].values()]) / len(self.required_fields)
        time_gap_penalty = min(len(quality_report['time_gaps']) / len(df), 0.5) if len(df) > 0 else 0
        quality_report['quality_score'] = max(0.0, 1.0 - missing_penalty - time_gap_penalty)
        
        return quality_report

    def export_symptom_data(self, event: Dict) -> Dict:
        """
        导出征兆数据（卡钻前15分钟）
        """
        well_name = event['well_name']
        stuck_time = event['stuck_time']

        # 计算时间范围：卡钻前15分钟
        end_time = stuck_time
        start_time = stuck_time - timedelta(minutes=self.export_configs['symptom']['time_before_minutes'])

        print(f"\n📊 导出征兆数据: {well_name}")
        print(f"   卡钻时间: {stuck_time}")
        print(f"   数据范围: {start_time} 至 {end_time}")

        # 获取井路径
        well_path = self.base_path / well_name
        if not well_path.exists():
            return {'success': False, 'error': f'井路径不存在: {well_path}'}

        # 获取数据库文件
        db_files = self.get_db_files_for_time_range(well_path, start_time, end_time)
        if not db_files:
            return {'success': False, 'error': '未找到相关数据库文件'}

        # 提取数据
        df = self.extract_data_from_db(db_files, start_time, end_time)
        if df.empty:
            return {'success': False, 'error': '未提取到数据'}

        # 数据质量验证
        quality_report = self.validate_data_quality(df)

        # 保存数据
        output_dir = self.output_base_dir / self.export_configs['symptom']['folder']
        output_dir.mkdir(parents=True, exist_ok=True)

        # 文件命名：井名_卡钻时间_征兆数据.csv
        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_征兆数据.csv"
        output_file = output_dir / filename

        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        result = {
            'success': True,
            'data_type': 'symptom',
            'well_name': well_name,
            'stuck_time': stuck_time,
            'time_range': f"{start_time} 至 {end_time}",
            'output_file': str(output_file),
            'record_count': len(df),
            'quality_report': quality_report
        }

        print(f"   ✅ 征兆数据导出成功: {filename} ({len(df)} 条记录)")
        print(f"   📈 数据质量分数: {quality_report['quality_score']:.3f}")

        return result

    def export_test_data(self, event: Dict) -> Dict:
        """
        导出测试数据（卡钻前24小时）
        """
        well_name = event['well_name']
        stuck_time = event['stuck_time']

        # 计算时间范围：卡钻前24小时
        end_time = stuck_time
        start_time = stuck_time - timedelta(hours=self.export_configs['test']['time_before_hours'])

        print(f"\n📊 导出测试数据: {well_name}")
        print(f"   卡钻时间: {stuck_time}")
        print(f"   数据范围: {start_time} 至 {end_time}")

        # 获取井路径
        well_path = self.base_path / well_name
        if not well_path.exists():
            return {'success': False, 'error': f'井路径不存在: {well_path}'}

        # 获取数据库文件
        db_files = self.get_db_files_for_time_range(well_path, start_time, end_time)
        if not db_files:
            return {'success': False, 'error': '未找到相关数据库文件'}

        # 提取数据
        df = self.extract_data_from_db(db_files, start_time, end_time)
        if df.empty:
            return {'success': False, 'error': '未提取到数据'}

        # 数据质量验证
        quality_report = self.validate_data_quality(df)

        # 保存数据
        output_dir = self.output_base_dir / self.export_configs['test']['folder']
        output_dir.mkdir(parents=True, exist_ok=True)

        # 文件命名：井名_卡钻时间_测试数据.csv
        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_测试数据.csv"
        output_file = output_dir / filename

        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        result = {
            'success': True,
            'data_type': 'test',
            'well_name': well_name,
            'stuck_time': stuck_time,
            'time_range': f"{start_time} 至 {end_time}",
            'output_file': str(output_file),
            'record_count': len(df),
            'quality_report': quality_report
        }

        print(f"   ✅ 测试数据导出成功: {filename} ({len(df)} 条记录)")
        print(f"   📈 数据质量分数: {quality_report['quality_score']:.3f}")

        return result

    def export_normal_data(self, event: Dict, sample_hours: int = 24) -> Dict:
        """
        导出正常数据（卡钻前7-30天的数据）

        参数:
            event: 卡钻事件信息
            sample_hours: 采样时长（小时），默认24小时
        """
        well_name = event['well_name']
        stuck_time = event['stuck_time']

        # 计算时间范围：卡钻前7-30天
        range_end = stuck_time - timedelta(days=self.export_configs['normal']['time_before_days_start'])
        range_start = stuck_time - timedelta(days=self.export_configs['normal']['time_before_days_end'])

        # 在这个范围内随机选择一个时间点，然后提取sample_hours小时的数据
        import random
        total_seconds = int((range_end - range_start).total_seconds())
        random_seconds = random.randint(0, max(0, total_seconds - sample_hours * 3600))

        start_time = range_start + timedelta(seconds=random_seconds)
        end_time = start_time + timedelta(hours=sample_hours)

        print(f"\n📊 导出正常数据: {well_name}")
        print(f"   卡钻时间: {stuck_time}")
        print(f"   正常数据范围: {start_time} 至 {end_time}")
        print(f"   (从卡钻前{self.export_configs['normal']['time_before_days_end']}-{self.export_configs['normal']['time_before_days_start']}天随机选择)")

        # 获取井路径
        well_path = self.base_path / well_name
        if not well_path.exists():
            return {'success': False, 'error': f'井路径不存在: {well_path}'}

        # 获取数据库文件
        db_files = self.get_db_files_for_time_range(well_path, start_time, end_time)
        if not db_files:
            return {'success': False, 'error': '未找到相关数据库文件'}

        # 提取数据
        df = self.extract_data_from_db(db_files, start_time, end_time)
        if df.empty:
            return {'success': False, 'error': '未提取到数据'}

        # 数据质量验证
        quality_report = self.validate_data_quality(df)

        # 保存数据
        output_dir = self.output_base_dir / self.export_configs['normal']['folder']
        output_dir.mkdir(parents=True, exist_ok=True)

        # 文件命名：井名_卡钻时间_正常数据_随机时间.csv
        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_正常数据_{start_time.strftime('%Y%m%d_%H%M%S')}.csv"
        output_file = output_dir / filename

        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        result = {
            'success': True,
            'data_type': 'normal',
            'well_name': well_name,
            'stuck_time': stuck_time,
            'normal_time_range': f"{start_time} 至 {end_time}",
            'output_file': str(output_file),
            'record_count': len(df),
            'quality_report': quality_report
        }

        print(f"   ✅ 正常数据导出成功: {filename} ({len(df)} 条记录)")
        print(f"   📈 数据质量分数: {quality_report['quality_score']:.3f}")

        return result

    def batch_export_all_data(self, stuck_events: List[Dict], export_types: List[str] = None) -> Dict:
        """
        批量导出所有类型的数据

        参数:
            stuck_events: 卡钻事件列表
            export_types: 要导出的数据类型列表，默认导出所有类型
        """
        if export_types is None:
            export_types = ['symptom', 'test', 'normal']

        print(f"\n🚀 开始批量导出钻井卡钻数据")
        print(f"   卡钻事件数量: {len(stuck_events)}")
        print(f"   导出数据类型: {export_types}")
        print("=" * 60)

        results = {
            'total_events': len(stuck_events),
            'export_types': export_types,
            'results': [],
            'summary': {
                'successful': 0,
                'failed': 0,
                'by_type': {data_type: {'success': 0, 'failed': 0} for data_type in export_types}
            }
        }

        for i, event in enumerate(stuck_events, 1):
            print(f"\n{'='*20} 处理事件 {i}/{len(stuck_events)} {'='*20}")
            print(f"井名: {event['well_name']}")
            print(f"卡钻时间: {event['stuck_time']}")

            event_results = {
                'event': event,
                'exports': {}
            }

            # 导出征兆数据
            if 'symptom' in export_types:
                try:
                    symptom_result = self.export_symptom_data(event)
                    event_results['exports']['symptom'] = symptom_result

                    if symptom_result['success']:
                        results['summary']['by_type']['symptom']['success'] += 1
                    else:
                        results['summary']['by_type']['symptom']['failed'] += 1
                        print(f"   ❌ 征兆数据导出失败: {symptom_result.get('error', '未知错误')}")

                except Exception as e:
                    print(f"   ❌ 征兆数据导出异常: {e}")
                    event_results['exports']['symptom'] = {'success': False, 'error': str(e)}
                    results['summary']['by_type']['symptom']['failed'] += 1

            # 导出测试数据
            if 'test' in export_types:
                try:
                    test_result = self.export_test_data(event)
                    event_results['exports']['test'] = test_result

                    if test_result['success']:
                        results['summary']['by_type']['test']['success'] += 1
                    else:
                        results['summary']['by_type']['test']['failed'] += 1
                        print(f"   ❌ 测试数据导出失败: {test_result.get('error', '未知错误')}")

                except Exception as e:
                    print(f"   ❌ 测试数据导出异常: {e}")
                    event_results['exports']['test'] = {'success': False, 'error': str(e)}
                    results['summary']['by_type']['test']['failed'] += 1

            # 导出正常数据
            if 'normal' in export_types:
                try:
                    normal_result = self.export_normal_data(event)
                    event_results['exports']['normal'] = normal_result

                    if normal_result['success']:
                        results['summary']['by_type']['normal']['success'] += 1
                    else:
                        results['summary']['by_type']['normal']['failed'] += 1
                        print(f"   ❌ 正常数据导出失败: {normal_result.get('error', '未知错误')}")

                except Exception as e:
                    print(f"   ❌ 正常数据导出异常: {e}")
                    event_results['exports']['normal'] = {'success': False, 'error': str(e)}
                    results['summary']['by_type']['normal']['failed'] += 1

            # 统计事件级别的成功/失败
            event_success = any(result.get('success', False) for result in event_results['exports'].values())
            if event_success:
                results['summary']['successful'] += 1
            else:
                results['summary']['failed'] += 1

            results['results'].append(event_results)

        return results

    def generate_export_report(self, results: Dict) -> str:
        """
        生成导出报告
        """
        report_file = self.output_base_dir / "导出报告.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 钻井卡钻数据导出报告\n\n")
            f.write(f"**导出时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 总体统计
            f.write("## 总体统计\n\n")
            f.write(f"- **总事件数**: {results['total_events']}\n")
            f.write(f"- **成功事件数**: {results['summary']['successful']}\n")
            f.write(f"- **失败事件数**: {results['summary']['failed']}\n")
            f.write(f"- **成功率**: {results['summary']['successful']/results['total_events']:.2%}\n\n")

            # 按数据类型统计
            f.write("## 按数据类型统计\n\n")
            f.write("| 数据类型 | 成功 | 失败 | 成功率 |\n")
            f.write("|----------|------|------|--------|\n")

            for data_type, stats in results['summary']['by_type'].items():
                total = stats['success'] + stats['failed']
                success_rate = stats['success'] / total if total > 0 else 0
                type_name = self.export_configs[data_type]['name']
                f.write(f"| {type_name} | {stats['success']} | {stats['failed']} | {success_rate:.2%} |\n")

            f.write("\n## 详细结果\n\n")

            # 详细结果
            for i, event_result in enumerate(results['results'], 1):
                event = event_result['event']
                f.write(f"### {i}. {event['well_name']} - {event['stuck_time']}\n\n")

                for data_type, export_result in event_result['exports'].items():
                    type_name = self.export_configs[data_type]['name']
                    if export_result['success']:
                        f.write(f"- ✅ **{type_name}**: {export_result['record_count']} 条记录\n")
                        f.write(f"  - 文件: `{Path(export_result['output_file']).name}`\n")
                        f.write(f"  - 质量分数: {export_result['quality_report']['quality_score']:.3f}\n")
                    else:
                        f.write(f"- ❌ **{type_name}**: {export_result.get('error', '未知错误')}\n")

                f.write("\n")

            # 数据使用说明
            f.write("## 数据使用说明\n\n")
            f.write("### 征兆数据（异常样本）\n")
            f.write("- **用途**: 模型训练的异常样本\n")
            f.write("- **时间范围**: 卡钻事件前15分钟\n")
            f.write("- **后续处理**: 按3分钟窗口切分成5个训练样本\n\n")

            f.write("### 测试数据（模型验证）\n")
            f.write("- **用途**: 模型性能测试和预警效果验证\n")
            f.write("- **时间范围**: 卡钻事件前24小时\n")
            f.write("- **特点**: 包含卡钻征兆期，用于测试模型预警能力\n\n")

            f.write("### 正常数据（正常样本）\n")
            f.write("- **用途**: 模型训练的正常样本\n")
            f.write("- **时间范围**: 卡钻事件前7-30天随机选择24小时\n")
            f.write("- **特点**: 排除异常时间段，确保数据正常性\n\n")

            f.write("### 工况字段标准化\n")
            f.write("数据库中的RIGSTA字段已标准化为以下工况：\n")
            for standard_condition in self.rigsta_mapping.keys():
                f.write(f"- {standard_condition}\n")

        print(f"\n📋 导出报告已生成: {report_file}")
        return str(report_file)


def main():
    """
    主函数 - 钻井卡钻数据导出
    """
    # 配置参数
    BASE_PATH = r"D:\采数据\PROCESS"  # 数据库文件的根目录
    OUTPUT_DIR = r"D:\采数据\PROCESS\stuck_prediction_data"  # 输出目录
    STUCK_EVENTS_CSV = "卡钻事件表.csv"  # 卡钻事件CSV文件

    print("🚀 钻井卡钻数据导出器")
    print("=" * 50)

    # 初始化导出器
    exporter = DrillingStuckDataExporter(BASE_PATH, OUTPUT_DIR)

    # 加载卡钻事件
    stuck_events = exporter.load_stuck_events_from_csv(STUCK_EVENTS_CSV)

    if not stuck_events:
        print("❌ 未找到卡钻事件数据，请检查CSV文件")
        print(f"   期望文件: {STUCK_EVENTS_CSV}")
        print("   CSV格式要求:")
        print("   - 井名: 井的名称")
        print("   - 卡钻时间: 卡钻发生的时间 (YYYY-MM-DD HH:MM:SS)")
        print("   - 事件描述: 可选，事件的描述信息")
        return

    # 批量导出数据
    try:
        results = exporter.batch_export_all_data(stuck_events)

        # 生成报告
        report_file = exporter.generate_export_report(results)

        # 输出总结
        print(f"\n🎉 数据导出完成!")
        print(f"📊 总体统计:")
        print(f"   - 处理事件: {results['total_events']} 个")
        print(f"   - 成功事件: {results['summary']['successful']} 个")
        print(f"   - 失败事件: {results['summary']['failed']} 个")
        print(f"   - 成功率: {results['summary']['successful']/results['total_events']:.2%}")

        print(f"\n📁 输出目录: {OUTPUT_DIR}")
        print(f"📋 详细报告: {report_file}")

        # 按数据类型统计
        print(f"\n📈 按数据类型统计:")
        for data_type, stats in results['summary']['by_type'].items():
            type_name = exporter.export_configs[data_type]['name']
            total = stats['success'] + stats['failed']
            success_rate = stats['success'] / total if total > 0 else 0
            print(f"   - {type_name}: {stats['success']}/{total} ({success_rate:.2%})")

    except Exception as e:
        print(f"❌ 导出过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

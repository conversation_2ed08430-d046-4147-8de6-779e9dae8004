#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工况专用PatchTST模型训练器
为每种工况训练独立的PatchTST模型
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from condition_specific_data_loader import create_condition_dataloaders

class ConditionSpecificTrainer:
    """工况专用模型训练器"""
    
    def __init__(self, 
                 condition_name: str,
                 model_config: dict = None,
                 training_config: dict = None,
                 save_dir: str = "checkpoints"):
        """
        初始化训练器
        
        Args:
            condition_name: 工况名称
            model_config: 模型配置
            training_config: 训练配置
            save_dir: 模型保存目录
        """
        self.condition_name = condition_name
        self.save_dir = Path(save_dir) / condition_name
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认模型配置
        self.model_config = {
            'seq_len': 180,
            'd_model': 128,
            'd_ff': 256,
            'n_heads': 8,
            'e_layers': 3,
            'dropout': 0.1,
            'patch_len': 16,
            'stride': 8,
            'n_classes': 2,  # 二分类：正常/异常
            'enc_in': 10,    # 输入特征数
            'c_out': 2,      # 输出类别数
        }
        
        if model_config:
            self.model_config.update(model_config)
            
        # 默认训练配置
        self.training_config = {
            'batch_size': 16,
            'learning_rate': 0.001,
            'train_epochs': 100,
            'patience': 10,
            'weight_decay': 1e-4,
            'warmup_epochs': 5,
        }
        
        if training_config:
            self.training_config.update(training_config)
            
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': [],
            'best_val_acc': 0.0,
            'best_epoch': 0
        }
        
    def create_model(self, force_simple=False):
        """创建PatchTST分类模型"""
        if not force_simple:
            try:
                # 尝试导入现有的PatchTST模型
                from models.PatchTST import Model as PatchTST

                # 创建模型参数对象
                class Args:
                    def __init__(self, config):
                        for key, value in config.items():
                            setattr(self, key, value)
                        # 添加必要的参数
                        self.task_name = 'earlysignaldet'
                        self.pred_len = 0
                        self.label_len = 0
                        self.factor = 3
                        self.embed = 'timeF'
                        self.freq = 's'
                        self.activation = 'gelu'
                        self.output_attention = False
                        self.distil = True
                        self.mix = True
                        self.top_k = 3
                        self.num_kernels = 6
                        # 修复关键问题：PatchTST需要num_class而不是n_classes
                        self.num_class = getattr(self, 'n_classes', 2)

                args = Args(self.model_config)
                model = PatchTST(args)
                print(f"成功创建PatchTST模型")
                return model.to(self.device)

            except ImportError:
                print("无法导入PatchTST模型，使用简化版本")
            except Exception as e:
                print(f"PatchTST模型创建失败: {e}，使用简化版本")

        # 使用简化版本
        print("使用简化版分类器")
        model = self._create_simple_classifier()
        return model.to(self.device)
    
    def _create_simple_classifier(self):
        """创建简化的分类器（备用方案）"""
        class SimpleClassifier(nn.Module):
            def __init__(self, config):
                super().__init__()
                self.seq_len = config['seq_len']
                self.enc_in = config['enc_in']
                self.d_model = config['d_model']
                self.n_classes = config['n_classes']
                
                # 简单的特征提取器
                self.feature_extractor = nn.Sequential(
                    nn.Linear(self.enc_in, self.d_model),
                    nn.ReLU(),
                    nn.Dropout(config['dropout']),
                    nn.Linear(self.d_model, self.d_model // 2),
                    nn.ReLU(),
                    nn.Dropout(config['dropout'])
                )
                
                # 全局平均池化
                self.global_pool = nn.AdaptiveAvgPool1d(1)
                
                # 分类头
                self.classifier = nn.Sequential(
                    nn.Linear(self.d_model // 2, self.d_model // 4),
                    nn.ReLU(),
                    nn.Dropout(config['dropout']),
                    nn.Linear(self.d_model // 4, self.n_classes)
                )
                
            def forward(self, x):
                # x shape: (batch_size, seq_len, enc_in)
                batch_size, seq_len, enc_in = x.shape
                
                # 重塑为 (batch_size * seq_len, enc_in)
                x = x.view(-1, enc_in)
                
                # 特征提取
                x = self.feature_extractor(x)  # (batch_size * seq_len, d_model//2)
                
                # 重塑回 (batch_size, seq_len, d_model//2)
                x = x.view(batch_size, seq_len, -1)
                
                # 转置用于池化 (batch_size, d_model//2, seq_len)
                x = x.transpose(1, 2)
                
                # 全局平均池化 (batch_size, d_model//2, 1)
                x = self.global_pool(x)
                
                # 展平 (batch_size, d_model//2)
                x = x.squeeze(-1)
                
                # 分类
                output = self.classifier(x)
                
                return output
                
        return SimpleClassifier(self.model_config)
    
    def train_model(self):
        """训练模型"""
        print(f"\n开始训练工况 '{self.condition_name}' 的专用模型")
        print("=" * 60)

        # 创建数据加载器
        print("创建数据加载器...")
        dataloaders = create_condition_dataloaders(
            condition_name=self.condition_name,
            batch_size=self.training_config['batch_size'],
            seq_len=self.model_config['seq_len']
        )
        
        train_loader = dataloaders['train']
        val_loader = dataloaders['val']
        
        print(f"   训练批次: {len(train_loader)}")
        print(f"   验证批次: {len(val_loader)}")
        
        # 创建模型
        print("创建模型...")
        model = self.create_model()
        print(f"   模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=self.training_config['learning_rate'],
            weight_decay=self.training_config['weight_decay']
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        # 训练循环
        best_val_acc = 0.0
        patience_counter = 0
        
        for epoch in range(self.training_config['train_epochs']):
            # 训练阶段
            train_loss, train_acc = self._train_epoch(model, train_loader, criterion, optimizer)
            
            # 验证阶段
            val_loss, val_acc = self._validate_epoch(model, val_loader, criterion)
            
            # 更新学习率
            scheduler.step(val_acc)
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_acc'].append(val_acc)
            
            # 打印进度
            print(f"Epoch {epoch+1:3d}/{self.training_config['train_epochs']:3d} | "
                  f"Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.4f} | "
                  f"Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.4f}")
            
            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                self.train_history['best_val_acc'] = best_val_acc
                self.train_history['best_epoch'] = epoch + 1
                
                # 保存模型
                self._save_model(model, optimizer, epoch, val_acc)
                print(f"   新的最佳模型已保存 (Val Acc: {val_acc:.4f})")
            else:
                patience_counter += 1

            # 早停检查
            if patience_counter >= self.training_config['patience']:
                print(f"   早停触发 (patience={self.training_config['patience']})")
                break

        print(f"\n训练完成!")
        print(f"   最佳验证准确率: {best_val_acc:.4f} (Epoch {self.train_history['best_epoch']})")
        
        # 保存训练历史
        self._save_training_history()
        
        return best_val_acc
    
    def _train_epoch(self, model, dataloader, criterion, optimizer):
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_x, batch_y in dataloader:
            batch_x = batch_x.float().to(self.device)
            batch_y = batch_y.long().to(self.device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()
            
        avg_loss = total_loss / len(dataloader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def _validate_epoch(self, model, dataloader, criterion):
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.long().to(self.device)
                
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()
                
        avg_loss = total_loss / len(dataloader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def _save_model(self, model, optimizer, epoch, val_acc):
        """保存模型"""
        checkpoint = {
            'condition_name': self.condition_name,
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'val_acc': val_acc,
            'model_config': self.model_config,
            'training_config': self.training_config,
            'train_history': self.train_history
        }
        
        checkpoint_path = self.save_dir / f'best_model.pth'
        torch.save(checkpoint, checkpoint_path)
        
    def _save_training_history(self):
        """保存训练历史"""
        history_path = self.save_dir / 'training_history.json'
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.train_history, f, indent=2, ensure_ascii=False)

def train_all_conditions():
    """训练所有工况的模型"""
    conditions = ['正常钻进', '起钻', '下钻', '正划眼', '倒划眼']
    
    results = {}
    
    for condition in conditions:
        print(f"\n{'='*80}")
        print(f"开始训练工况: {condition}")
        print(f"{'='*80}")
        
        try:
            trainer = ConditionSpecificTrainer(condition_name=condition)
            best_acc = trainer.train_model()
            results[condition] = {
                'status': 'success',
                'best_val_acc': best_acc
            }
            
        except Exception as e:
            print(f"工况 '{condition}' 训练失败: {e}")
            results[condition] = {
                'status': 'failed',
                'error': str(e)
            }
            import traceback
            traceback.print_exc()
            
    # 打印总结
    print(f"\n{'='*80}")
    print("训练总结")
    print(f"{'='*80}")
    
    for condition, result in results.items():
        if result['status'] == 'success':
            print(f"成功 {condition}: 最佳准确率 {result['best_val_acc']:.4f}")
        else:
            print(f"失败 {condition}: 训练失败")
            
    return results

if __name__ == "__main__":
    print("工况专用PatchTST模型训练系统")
    print("=" * 50)
    
    # 训练所有工况模型
    results = train_all_conditions()

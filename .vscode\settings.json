{
    // Python解释器配置
    "python.defaultInterpreterPath": "python",
    "python.terminal.activateEnvironment": true,
    
    // 代码格式化
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "editor.formatOnSave": true,
    
    // 代码检查
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
        "--max-line-length=88",
        "--ignore=E203,W503,F401"
    ],
    
    // 自动补全和智能提示
    "python.analysis.autoImportCompletions": true,
    "python.analysis.completeFunctionParens": true,
    "python.analysis.typeCheckingMode": "basic",
    
    // 文件关联
    "files.associations": {
        "*.py": "python",
        "*.pyi": "python",
        "*.ipynb": "jupyter"
    },
    
    // 排除文件和文件夹
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/checkpoints/**": false,
        "**/results/**": false,
        "**/logs/**": false
    },
    
    // 搜索排除
    "search.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/checkpoints/**": true,
        "**/results/**": false
    },
    
    // 终端配置
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.cwd": "${workspaceFolder}",
    
    // 编辑器配置
    "editor.rulers": [88],
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    
    // 文件监视
    "files.watcherExclude": {
        "**/__pycache__/**": true,
        "**/checkpoints/**": true,
        "**/results/**": true
    },
    
    // Jupyter配置
    "jupyter.askForKernelRestart": false,
    "jupyter.interactiveWindow.textEditor.executeSelection": true,
    
    // Git配置
    "git.ignoreLimitWarning": true,
    
    // 工作区特定设置
    "python.analysis.extraPaths": [
        "${workspaceFolder}",
        "${workspaceFolder}/models",
        "${workspaceFolder}/data_provider",
        "${workspaceFolder}/exp",
        "${workspaceFolder}/utils"
    ],
    
    // 调试配置
    "debug.console.fontSize": 12,
    "debug.console.wordWrap": true,
    "debug.inlineValues": true,
    
    // 任务配置
    "tasks.version": "2.0.0"
}

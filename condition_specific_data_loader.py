#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工况专用数据加载器
为每种工况创建独立的数据加载器，支持PatchTST模型训练
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
from torch.utils.data import Dataset
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import torch
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

class ConditionSpecificDataLoader(Dataset):
    """
    工况专用数据加载器
    为特定工况加载征兆数据和正常数据，用于训练工况专用模型
    """
    
    def __init__(self, 
                 condition_name: str,
                 processed_data_path: str = "dataset/processed_data",
                 seq_len: int = 180,  # 3分钟数据，约180个时间点
                 flag: str = 'train',
                 features: str = 'M',  # M: multivariate, S: univariate
                 target_col: str = 'label',
                 scale: bool = True):
        """
        初始化工况专用数据加载器
        
        Args:
            condition_name: 工况名称 ('正常钻进', '起钻', '下钻', '正划眼', '倒划眼')
            processed_data_path: 处理后数据路径
            seq_len: 序列长度
            flag: 数据集标志 ('train', 'val', 'test')
            features: 特征类型
            target_col: 目标列名
            scale: 是否标准化
        """
        self.condition_name = condition_name
        self.processed_data_path = Path(processed_data_path)
        self.seq_len = seq_len
        self.flag = flag
        self.features = features
        self.target_col = target_col
        self.scale = scale
        
        # 数据路径
        self.symptom_path = self.processed_data_path / 'symptom_data' / condition_name
        self.normal_path = self.processed_data_path / 'normal_data' / condition_name
        
        # 特征列（排除非数值列）
        self.feature_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
        
        # 初始化数据
        self._load_and_prepare_data()
        
    def _load_and_prepare_data(self):
        """加载和准备数据"""
        print(f"加载工况 '{self.condition_name}' 的数据...")

        # 加载征兆数据（标签=1）
        symptom_data, symptom_labels = self._load_condition_data(self.symptom_path, label=1)
        print(f"   征兆数据: {len(symptom_data)} 个样本")

        # 加载正常数据（标签=0）
        normal_data, normal_labels = self._load_condition_data(self.normal_path, label=0)
        print(f"   正常数据: {len(normal_data)} 个样本")
        
        # 合并数据
        all_data = symptom_data + normal_data
        all_labels = symptom_labels + normal_labels
        
        if len(all_data) == 0:
            raise ValueError(f"工况 '{self.condition_name}' 没有找到数据文件")
        
        print(f"   总数据: {len(all_data)} 个样本 (征兆: {len(symptom_data)}, 正常: {len(normal_data)})")
        
        # 数据分割
        self._split_data(all_data, all_labels)

        # 数据标准化（需要在分割后进行）
        if self.scale:
            self._scale_data()
            
    def _load_condition_data(self, data_path: Path, label: int) -> Tuple[List[np.ndarray], List[int]]:
        """加载特定路径下的数据"""
        data_list = []
        labels = []
        
        if not data_path.exists():
            print(f"   路径不存在: {data_path}")
            return data_list, labels

        csv_files = list(data_path.glob("*.csv"))
        if not csv_files:
            print(f"   没有找到CSV文件: {data_path}")
            return data_list, labels
            
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                
                # 检查必要的列
                missing_cols = [col for col in self.feature_cols if col not in df.columns]
                if missing_cols:
                    print(f"   文件 {csv_file.name} 缺少列: {missing_cols}")
                    continue
                
                # 提取特征数据
                feature_data = df[self.feature_cols].values.astype(np.float32)
                
                # 检查数据质量
                if len(feature_data) < 10:  # 至少10个时间点
                    continue
                    
                # 处理NaN值
                if np.isnan(feature_data).any():
                    feature_data = pd.DataFrame(feature_data).fillna(method='ffill').fillna(method='bfill').values
                
                # 调整序列长度
                if len(feature_data) >= self.seq_len:
                    # 如果数据长度大于等于seq_len，截取前seq_len个点
                    feature_data = feature_data[:self.seq_len]
                else:
                    # 如果数据长度小于seq_len，进行填充
                    pad_length = self.seq_len - len(feature_data)
                    # 使用最后一个值进行填充
                    last_values = feature_data[-1:].repeat(pad_length, axis=0)
                    feature_data = np.vstack([feature_data, last_values])
                
                data_list.append(feature_data)
                labels.append(label)
                
            except Exception as e:
                print(f"   加载文件失败 {csv_file.name}: {e}")
                continue
                
        return data_list, labels
    
    def _split_data(self, all_data: List[np.ndarray], all_labels: List[int]):
        """分割训练、验证、测试数据"""
        # 转换为numpy数组
        X = np.stack(all_data)  # shape: (n_samples, seq_len, n_features)
        y = np.array(all_labels)

        # 检查每个类别的样本数量
        unique_labels, counts = np.unique(y, return_counts=True)
        min_count = np.min(counts)

        if min_count < 2:
            print(f"   警告: 某些类别样本数量过少 (最少: {min_count})，使用简单分割")
            # 简单分割，不使用分层抽样
            n_samples = len(X)
            n_train = int(0.7 * n_samples)
            n_val = int(0.15 * n_samples)

            # 随机打乱
            indices = np.random.permutation(n_samples)

            train_indices = indices[:n_train]
            val_indices = indices[n_train:n_train+n_val]
            test_indices = indices[n_train+n_val:]

            X_train, y_train = X[train_indices], y[train_indices]
            X_val, y_val = X[val_indices], y[val_indices]
            X_test, y_test = X[test_indices], y[test_indices]

        else:
            # 分割数据 (70% train, 15% val, 15% test)
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=0.15, random_state=42, stratify=y
            )

            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp  # 0.176 ≈ 0.15/0.85
            )
        
        # 存储数据
        if self.flag == 'train':
            self.data_x = X_train
            self.data_y = y_train
        elif self.flag == 'val':
            self.data_x = X_val
            self.data_y = y_val
        elif self.flag == 'test':
            self.data_x = X_test
            self.data_y = y_test
        else:
            raise ValueError(f"不支持的flag: {self.flag}")
            
        print(f"   数据分割完成:")
        print(f"      训练集: {len(X_train)} 样本")
        print(f"      验证集: {len(X_val)} 样本")
        print(f"      测试集: {len(X_test)} 样本")
        print(f"   当前使用: {self.flag} ({len(self.data_x)} 样本)")
        
    def _scale_data(self):
        """数据标准化"""
        # 重塑数据进行标准化 (n_samples * seq_len, n_features)
        original_shape = self.data_x.shape
        reshaped_data = self.data_x.reshape(-1, self.data_x.shape[-1])

        # 创建并拟合标准化器（每个数据集都独立标准化）
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(reshaped_data)

        # 保存标准化器供后续使用
        self.scaler = scaler

        # 恢复原始形状
        self.data_x = scaled_data.reshape(original_shape)
        
    def __len__(self):
        return len(self.data_x)
    
    def __getitem__(self, index):
        """获取单个样本"""
        seq_x = self.data_x[index]  # shape: (seq_len, n_features)
        seq_y = self.data_y[index]  # shape: scalar
        
        return seq_x, seq_y
    
    def get_data_info(self) -> Dict:
        """获取数据信息"""
        return {
            'condition_name': self.condition_name,
            'n_samples': len(self.data_x),
            'seq_len': self.seq_len,
            'n_features': len(self.feature_cols),
            'feature_cols': self.feature_cols,
            'class_distribution': {
                'normal': int(np.sum(self.data_y == 0)),
                'anomaly': int(np.sum(self.data_y == 1))
            }
        }

def create_condition_dataloaders(condition_name: str, 
                               processed_data_path: str = "dataset/processed_data",
                               batch_size: int = 16,
                               seq_len: int = 180,
                               num_workers: int = 0) -> Dict:
    """
    为指定工况创建数据加载器
    
    Returns:
        Dict: 包含train, val, test数据加载器的字典
    """
    from torch.utils.data import DataLoader
    
    dataloaders = {}
    
    for flag in ['train', 'val', 'test']:
        dataset = ConditionSpecificDataLoader(
            condition_name=condition_name,
            processed_data_path=processed_data_path,
            seq_len=seq_len,
            flag=flag
        )
        
        dataloader = DataLoader(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=(flag == 'train'),
            num_workers=num_workers,
            drop_last=False
        )
        
        dataloaders[flag] = dataloader
        
    return dataloaders

if __name__ == "__main__":
    # 测试数据加载器
    print("测试工况专用数据加载器")
    print("=" * 50)
    
    condition_name = "正常钻进"
    
    try:
        # 创建数据加载器
        dataloaders = create_condition_dataloaders(
            condition_name=condition_name,
            batch_size=8,
            seq_len=180
        )
        
        # 测试数据加载
        for flag, dataloader in dataloaders.items():
            print(f"\n{flag.upper()} 数据加载器:")
            print(f"   批次数量: {len(dataloader)}")

            # 获取一个批次的数据
            for batch_x, batch_y in dataloader:
                print(f"   批次形状: X={batch_x.shape}, Y={batch_y.shape}")
                print(f"   标签分布: {torch.bincount(batch_y)}")
                break

        print(f"\n工况 '{condition_name}' 数据加载器测试成功!")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

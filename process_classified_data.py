#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理已分类的钻井数据
用户已经人工挑选分类好了正常数据和征兆数据，现在需要按工况进一步处理

数据结构：
- 正常数据文件夹/
  ├── file1.csv
  ├── file2.csv
  └── ...
- 征兆数据文件夹/
  ├── file1.csv
  ├── file2.csv
  └── ...

处理目标：
1. 对正常数据和征兆数据分别按工况分类
2. 创建训练数据集，每个工况都有正常样本和征兆样本
3. 生成可用于PatchTST训练的数据格式
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
from typing import Dict, List, Tuple

class ClassifiedDataProcessor:
    """
    处理已分类的钻井数据
    """
    
    def __init__(self, config=None):
        self.config = config or {
            'window_size': 100,          # 滑动窗口大小
            'step_size': 20,             # 滑动步长
            'min_quality_score': 0.6,    # 最小质量分数
            'min_segment_length': 50,    # 最小段长度
        }
        
        # 钻井参数列名映射
        self.column_mapping = {
            'DEP': 'DEP',           # 井深
            'BITDEP': 'BITDEP',     # 钻头深度
            'HOOKLD': 'HKLD',       # 大钩载荷
            'HKLD': 'HKLD',         # 大钩载荷
            'WOB': 'WOB',           # 钻压
            'RPM': 'RPM',           # 转速
            'TOR': 'TOR',           # 扭矩
            'SPP': 'SPP',           # 立管压力
            'CW': 'condition',       # 工况
        }
    
    def load_data_with_encoding(self, file_path: str) -> pd.DataFrame:
        """
        使用正确编码加载数据
        """
        encodings = ['gbk', 'gb2312', 'utf-8', 'utf-8-sig', 'cp936']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return df
            except UnicodeDecodeError:
                continue
        
        raise ValueError(f"无法读取文件: {file_path}")
    
    def standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        """
        df_std = df.rename(columns=self.column_mapping)
        
        # 确保必要的钻井参数列存在
        required_params = ['DEP', 'BITDEP', 'WOB', 'RPM', 'HKLD', 'TOR', 'SPP']
        missing_params = [p for p in required_params if p not in df_std.columns]
        
        if missing_params:
            print(f"⚠️ 缺少钻井参数: {missing_params}")
        
        return df_std
    
    def extract_condition_segments(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """
        按工况提取数据段
        """
        if 'condition' not in df.columns:
            print("⚠️ 未找到工况列，将整个文件作为一个段处理")
            return {'未知工况': [{'data': df, 'start_idx': 0, 'end_idx': len(df)-1}]}
        
        segments = {}
        current_condition = None
        segment_start = 0
        
        for i, condition in enumerate(df['condition']):
            # 处理空值和异常值
            if pd.isna(condition) or condition == '':
                continue
                
            condition = str(condition).strip()
            
            # 工况发生变化
            if condition != current_condition:
                # 保存前一个段（如果存在且足够长）
                if current_condition is not None and i - segment_start >= self.config['min_segment_length']:
                    segment_data = df.iloc[segment_start:i].copy()
                    
                    if current_condition not in segments:
                        segments[current_condition] = []
                    
                    segments[current_condition].append({
                        'data': segment_data,
                        'start_idx': segment_start,
                        'end_idx': i-1,
                        'length': i - segment_start,
                        'quality_score': self._calculate_segment_quality(segment_data)
                    })
                
                # 开始新段
                current_condition = condition
                segment_start = i
        
        # 处理最后一个段
        if current_condition is not None and len(df) - segment_start >= self.config['min_segment_length']:
            segment_data = df.iloc[segment_start:].copy()
            
            if current_condition not in segments:
                segments[current_condition] = []
            
            segments[current_condition].append({
                'data': segment_data,
                'start_idx': segment_start,
                'end_idx': len(df)-1,
                'length': len(df) - segment_start,
                'quality_score': self._calculate_segment_quality(segment_data)
            })
        
        return segments
    
    def _calculate_segment_quality(self, segment_data: pd.DataFrame) -> float:
        """
        计算段质量分数
        """
        param_columns = ['WOB', 'RPM', 'HKLD', 'TOR', 'SPP']
        available_params = [col for col in param_columns if col in segment_data.columns]
        
        if not available_params:
            return 0.0
        
        scores = []
        for col in available_params:
            # 数据完整性
            completeness = 1 - segment_data[col].isna().sum() / len(segment_data)
            
            # 数据变异性（避免全是相同值）
            if segment_data[col].std() > 0:
                variability = min(segment_data[col].std() / segment_data[col].mean(), 1.0) if segment_data[col].mean() != 0 else 0
            else:
                variability = 0
            
            # 综合分数
            param_score = 0.7 * completeness + 0.3 * variability
            scores.append(param_score)
        
        return np.mean(scores) if scores else 0
    
    def create_sliding_windows(self, segment_data: pd.DataFrame) -> List[pd.DataFrame]:
        """
        从段数据创建滑动窗口
        """
        windows = []
        window_size = self.config['window_size']
        step_size = self.config['step_size']
        
        for i in range(0, len(segment_data) - window_size + 1, step_size):
            window = segment_data.iloc[i:i + window_size].copy()
            windows.append(window)
        
        return windows
    
    def process_data_folder(self, folder_path: str, data_type: str) -> Dict[str, List[pd.DataFrame]]:
        """
        处理数据文件夹（正常数据或征兆数据）
        """
        print(f"🔍 处理{data_type}数据文件夹: {folder_path}")
        
        folder = Path(folder_path)
        if not folder.exists():
            print(f"❌ 文件夹不存在: {folder_path}")
            return {}
        
        csv_files = list(folder.glob("*.csv"))
        if not csv_files:
            print(f"❌ 文件夹中没有CSV文件: {folder_path}")
            return {}
        
        print(f"📁 发现 {len(csv_files)} 个CSV文件")
        
        # 按工况收集所有窗口
        condition_windows = {}
        
        for file_path in csv_files:
            try:
                print(f"  处理文件: {file_path.name}")
                
                # 加载数据
                df = self.load_data_with_encoding(str(file_path))
                df_std = self.standardize_columns(df)
                
                # 按工况提取段
                segments = self.extract_condition_segments(df_std)
                
                # 为每个工况的每个段创建滑动窗口
                for condition, condition_segments in segments.items():
                    if condition not in condition_windows:
                        condition_windows[condition] = []
                    
                    for segment in condition_segments:
                        # 只处理高质量段
                        if segment['quality_score'] >= self.config['min_quality_score']:
                            windows = self.create_sliding_windows(segment['data'])
                            condition_windows[condition].extend(windows)
                
            except Exception as e:
                print(f"  ❌ 处理文件失败 {file_path.name}: {e}")
                continue
        
        # 统计结果
        total_windows = sum(len(windows) for windows in condition_windows.values())
        print(f"✅ {data_type}数据处理完成，共生成 {total_windows} 个窗口")
        
        for condition, windows in condition_windows.items():
            print(f"  - {condition}: {len(windows)} 个窗口")
        
        return condition_windows
    
    def create_training_dataset(self, normal_windows: Dict[str, List], 
                              anomaly_windows: Dict[str, List], 
                              output_dir: str):
        """
        创建训练数据集
        """
        print(f"🎯 创建训练数据集...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有工况
        all_conditions = set(normal_windows.keys()) | set(anomaly_windows.keys())
        
        dataset_info = {
            'creation_time': datetime.now().isoformat(),
            'conditions': {},
            'total_samples': 0
        }
        
        for condition in all_conditions:
            print(f"\n📊 处理工况: {condition}")
            
            # 获取该工况的正常和异常窗口
            normal_condition_windows = normal_windows.get(condition, [])
            anomaly_condition_windows = anomaly_windows.get(condition, [])
            
            if not normal_condition_windows and not anomaly_condition_windows:
                print(f"  ⚠️ 工况 {condition} 没有数据，跳过")
                continue
            
            # 创建工况目录
            condition_dir = output_path / condition
            condition_dir.mkdir(exist_ok=True)
            
            normal_dir = condition_dir / "normal"
            anomaly_dir = condition_dir / "anomaly"
            normal_dir.mkdir(exist_ok=True)
            anomaly_dir.mkdir(exist_ok=True)
            
            # 保存正常样本
            for i, window in enumerate(normal_condition_windows):
                file_path = normal_dir / f"normal_{i:04d}.csv"
                window.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            # 保存异常样本
            for i, window in enumerate(anomaly_condition_windows):
                file_path = anomaly_dir / f"anomaly_{i:04d}.csv"
                window.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            # 记录统计信息
            condition_info = {
                'normal_samples': len(normal_condition_windows),
                'anomaly_samples': len(anomaly_condition_windows),
                'total_samples': len(normal_condition_windows) + len(anomaly_condition_windows),
                'anomaly_ratio': len(anomaly_condition_windows) / (len(normal_condition_windows) + len(anomaly_condition_windows)) if (len(normal_condition_windows) + len(anomaly_condition_windows)) > 0 else 0
            }
            
            dataset_info['conditions'][condition] = condition_info
            dataset_info['total_samples'] += condition_info['total_samples']
            
            print(f"  ✅ 正常样本: {condition_info['normal_samples']}")
            print(f"  ✅ 异常样本: {condition_info['anomaly_samples']}")
            print(f"  ✅ 异常比例: {condition_info['anomaly_ratio']:.3f}")
        
        # 保存数据集信息
        info_file = output_path / "dataset_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 训练数据集创建完成!")
        print(f"📁 保存位置: {output_path}")
        print(f"📊 总样本数: {dataset_info['total_samples']}")
        print(f"📋 数据集信息: {info_file}")
        
        return dataset_info

def main():
    """
    主处理函数
    """
    print("🎯 处理已分类的钻井数据")
    print("=" * 60)
    
    # 配置参数
    config = {
        'window_size': 100,          # 滑动窗口大小
        'step_size': 20,             # 滑动步长
        'min_quality_score': 0.6,    # 最小质量分数
        'min_segment_length': 50,    # 最小段长度
    }
    
    # 数据路径（请根据实际情况修改）
    normal_data_folder = "正常数据"      # 正常数据文件夹路径
    anomaly_data_folder = "征兆数据"     # 征兆数据文件夹路径
    output_folder = "training_dataset"   # 输出文件夹路径
    
    processor = ClassifiedDataProcessor(config)
    
    try:
        # 1. 处理正常数据
        print("\n🔵 处理正常数据...")
        normal_windows = processor.process_data_folder(normal_data_folder, "正常")
        
        # 2. 处理征兆数据
        print("\n🔴 处理征兆数据...")
        anomaly_windows = processor.process_data_folder(anomaly_data_folder, "征兆")
        
        # 3. 创建训练数据集
        print("\n📊 创建训练数据集...")
        dataset_info = processor.create_training_dataset(
            normal_windows, anomaly_windows, output_folder
        )
        
        # 4. 生成使用说明
        create_usage_instructions(dataset_info, output_folder)
        
        print(f"\n" + "="*60)
        print("🎉 数据处理完成!")
        print("="*60)
        
        print(f"\n📁 生成的文件结构:")
        print(f"{output_folder}/")
        for condition in dataset_info['conditions'].keys():
            print(f"├── {condition}/")
            print(f"│   ├── normal/     # 正常样本")
            print(f"│   └── anomaly/    # 异常样本")
        print(f"├── dataset_info.json")
        print(f"└── usage_instructions.md")
        
        return dataset_info
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_usage_instructions(dataset_info: Dict, output_folder: str):
    """
    创建使用说明
    """
    instructions = f"""# 训练数据集使用说明

## 数据集概览
- 创建时间: {dataset_info['creation_time']}
- 总样本数: {dataset_info['total_samples']}
- 工况数量: {len(dataset_info['conditions'])}

## 工况统计
"""
    
    for condition, info in dataset_info['conditions'].items():
        instructions += f"""
### {condition}
- 正常样本: {info['normal_samples']} 个
- 异常样本: {info['anomaly_samples']} 个
- 异常比例: {info['anomaly_ratio']:.3f}
"""
    
    instructions += f"""
## 使用方法

### 1. 训练PatchTST模型
```python
# 为特定工况训练模型
python run_earlysignaldet.py \\
    --task_name earlysignaldet \\
    --model PatchTST \\
    --data Earlysignaldet \\
    --data_path {output_folder}/起钻/ \\
    --batch_size 16 \\
    --d_model 128 \\
    --learning_rate 0.001 \\
    --train_epochs 100 \\
    --do_predict
```

### 2. 多工况联合训练
可以将多个工况的数据合并训练，或者为每个工况单独训练模型。

### 3. 数据格式
- 每个CSV文件包含一个时间窗口的数据
- 列名已标准化：DEP, BITDEP, WOB, RPM, HKLD, TOR, SPP
- 正常样本在 normal/ 子目录
- 异常样本在 anomaly/ 子目录

## 注意事项
1. 数据已经按工况分类，可以针对特定工况训练专门的模型
2. 正负样本比例可能不平衡，训练时注意处理
3. 可以根据需要调整窗口大小和步长重新生成数据
"""
    
    instructions_file = Path(output_folder) / "usage_instructions.md"
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"📖 使用说明已保存: {instructions_file}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编码修复功能
创建包含中文标签的测试数据，验证编码处理是否正确
"""

import pandas as pd
import numpy as np
from pathlib import Path
from fix_encoding_and_process import load_with_correct_encoding, analyze_chinese_conditions

def create_chinese_test_data():
    """
    创建包含中文工况标签的测试数据
    """
    print("🔧 创建包含中文标签的测试数据...")
    
    # 模拟200个数据点
    n_points = 200
    
    # 创建基础数据
    data = {
        'DEP': np.linspace(2972, 2973, n_points),
        'BITDEP': np.linspace(2972, 2973, n_points),
        'HOOKLD': np.random.normal(975, 25, n_points),
        'DRTIME': pd.date_range('2022-06-23 18:24:00', periods=n_points, freq='5S'),
        'WOB': np.random.normal(160, 20, n_points),
        'HKLD': np.random.normal(975, 25, n_points),
        'RPM': np.random.normal(106, 5, n_points),
        'TOR': np.random.normal(18, 3, n_points),
        'SPP': np.random.normal(25.8, 2, n_points),
        'date': pd.date_range('2022-06-23 18:24:00', periods=n_points, freq='5S').strftime('%Y/%m/%d %H:%M'),
        'RIGSTA': ['继续钻进作业'] * n_points
    }
    
    # 创建中文工况标签序列
    conditions = []
    current_pos = 0
    
    # 定义工况序列：起钻 -> 坐卡 -> 起钻 -> 下钻 -> 正常钻进
    condition_sequence = [
        ('起钻', 50),      # 起钻50个点
        ('坐卡', 30),      # 坐卡30个点  
        ('起钻', 40),      # 继续起钻40个点
        ('下钻', 35),      # 下钻35个点
        ('正常钻进', 45)   # 正常钻进45个点
    ]
    
    for condition, length in condition_sequence:
        conditions.extend([condition] * length)
    
    # 确保长度匹配
    while len(conditions) < n_points:
        conditions.append('正常钻进')
    conditions = conditions[:n_points]
    
    data['CW'] = conditions
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    print(f"✅ 测试数据创建完成")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 工况分布: {df['CW'].value_counts().to_dict()}")
    
    return df

def test_encoding_methods():
    """
    测试不同编码方式保存和读取中文数据
    """
    print("\n🧪 测试不同编码方式...")
    
    # 创建测试数据
    df = create_chinese_test_data()
    
    # 测试不同编码方式
    encodings_to_test = ['utf-8', 'gbk', 'utf-8-sig']
    test_results = {}
    
    for encoding in encodings_to_test:
        try:
            # 保存文件
            test_file = f"test_chinese_data_{encoding.replace('-', '_')}.csv"
            df.to_csv(test_file, index=False, encoding=encoding)
            print(f"✅ 使用 {encoding} 编码保存成功: {test_file}")
            
            # 尝试读取文件
            df_loaded = load_with_correct_encoding(test_file)
            
            # 检查中文字符是否正确
            original_conditions = set(df['CW'].unique())
            loaded_conditions = set(df_loaded['CW'].unique())
            
            if original_conditions == loaded_conditions:
                print(f"✅ {encoding} 编码读取成功，中文字符正确")
                test_results[encoding] = 'success'
            else:
                print(f"❌ {encoding} 编码读取失败，中文字符损坏")
                print(f"   原始: {original_conditions}")
                print(f"   读取: {loaded_conditions}")
                test_results[encoding] = 'failed'
                
        except Exception as e:
            print(f"❌ {encoding} 编码测试失败: {str(e)}")
            test_results[encoding] = 'error'
    
    return test_results

def test_condition_analysis():
    """
    测试中文工况标签分析功能
    """
    print("\n🔍 测试中文工况标签分析...")
    
    # 创建测试数据
    df = create_chinese_test_data()
    
    # 保存为UTF-8-SIG格式（最兼容）
    test_file = "test_chinese_analysis.csv"
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    
    # 加载并分析
    df_loaded = load_with_correct_encoding(test_file)
    analysis_result = analyze_chinese_conditions(df_loaded)
    
    print(f"\n📊 分析结果:")
    print(f"  - 工况种类: {len(analysis_result['unique_conditions'])}")
    print(f"  - 总记录数: {analysis_result['total_records']}")
    print(f"  - 工况切换次数: {analysis_result['condition_changes']}")
    print(f"  - 平均持续时长: {analysis_result['avg_duration']:.1f} 个点")
    
    return analysis_result

def demonstrate_positive_negative_samples():
    """
    演示正负样本的实际例子
    """
    print("\n" + "="*60)
    print("📚 正负样本实际演示")
    print("="*60)
    
    # 创建测试数据
    df = create_chinese_test_data()
    
    print("🎯 假设我们要预测 '坐卡' 事件:")
    print()
    
    # 统计各工况数量
    condition_counts = df['CW'].value_counts()
    
    print("📊 原始数据中的工况分布:")
    for condition, count in condition_counts.items():
        percentage = (count / len(df)) * 100
        sample_type = "🔴 正样本" if condition == "坐卡" else "🔵 负样本"
        print(f"  {sample_type} - '{condition}': {count} 个数据点 ({percentage:.1f}%)")
    
    print(f"\n⚖️ 样本平衡分析:")
    positive_count = condition_counts.get('坐卡', 0)
    negative_count = len(df) - positive_count
    
    if positive_count > 0:
        ratio = negative_count / positive_count
        print(f"  - 当前正负比例: 1:{ratio:.1f}")
        print(f"  - 建议正负比例: 1:3 到 1:4")
        
        if ratio > 4:
            print(f"  ⚠️ 负样本过多，建议减少负样本数量")
        elif ratio < 3:
            print(f"  ⚠️ 正样本相对较多，可能需要更多负样本")
        else:
            print(f"  ✅ 样本比例较为合理")
    else:
        print(f"  ❌ 没有找到目标工况 '坐卡'，无法创建正样本")
    
    print(f"\n🔧 预处理系统会自动:")
    print(f"  1. 识别 '坐卡' 段作为正样本")
    print(f"  2. 其他工况段作为负样本")
    print(f"  3. 自动平衡正负样本比例")
    print(f"  4. 确保训练数据质量")

def main():
    """
    主测试函数
    """
    print("🎯 开始编码修复和中文数据处理测试")
    print("=" * 60)
    
    try:
        # 1. 测试编码方式
        encoding_results = test_encoding_methods()
        
        # 2. 测试工况分析
        analysis_result = test_condition_analysis()
        
        # 3. 演示正负样本
        demonstrate_positive_negative_samples()
        
        print(f"\n" + "="*60)
        print("🎉 测试完成!")
        print("="*60)
        
        print(f"\n📋 测试总结:")
        print(f"  - 编码测试结果: {encoding_results}")
        print(f"  - 推荐使用编码: utf-8-sig (最兼容)")
        print(f"  - 工况分析: 成功识别 {len(analysis_result['unique_conditions'])} 种中文工况")
        print(f"  - 正负样本: 演示完成")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 使用 fix_encoding_and_process.py 处理您的实际数据")
        print(f"  2. 确保文件使用正确的中文编码")
        print(f"  3. 根据您的目标工况调整正负样本比例")
        
        # 清理测试文件
        test_files = [
            "test_chinese_data_utf_8.csv",
            "test_chinese_data_gbk.csv", 
            "test_chinese_data_utf_8_sig.csv",
            "test_chinese_analysis.csv"
        ]
        
        for file in test_files:
            if Path(file).exists():
                Path(file).unlink()
                print(f"🗑️ 清理测试文件: {file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

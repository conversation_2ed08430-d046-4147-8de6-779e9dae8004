#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试PatchTST模型
验证模型架构和预测功能
"""

import sys
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from final_enhanced_predictor import FinalEnhancedPredictor

def quick_test():
    """快速测试PatchTST模型"""
    print("开始快速测试PatchTST模型")
    print("=" * 60)
    
    # 创建预测器
    predictor = FinalEnhancedPredictor()
    
    # 加载模型
    predictor.load_trained_models()
    
    if not predictor.models:
        print("没有可用的模型，请先训练模型")
        return
    
    print(f"成功加载 {len(predictor.models)} 个模型:")
    for condition, model in predictor.models.items():
        param_count = sum(p.numel() for p in model.parameters())
        print(f"  - {condition}: {param_count:,} 参数")
    
    # 测试文件列表（只测试前3个）
    test_data_dir = Path("dataset/processed_data/test_data/")
    if test_data_dir.exists():
        test_files = list(test_data_dir.glob("*.csv"))[:3]  # 只测试前3个文件
        print(f"\n找到 {len(test_files)} 个测试文件进行快速测试:")
        for i, file in enumerate(test_files, 1):
            print(f"  {i}. {file.name}")
    else:
        print(f"测试数据目录不存在: {test_data_dir}")
        return
    
    # 测试每个文件
    results_summary = []
    
    for i, test_file in enumerate(test_files, 1):
        print(f"\n{'='*60}")
        print(f"测试文件 {i}/{len(test_files)}: {test_file.name}")
        print(f"{'='*60}")
        
        try:
            # 分析文件
            results = predictor.analyze_file_final(str(test_file), create_visualization=False)
            
            if results:
                summary = {
                    'file': test_file.name,
                    'model_used': results['model_used'],
                    'anomaly_windows': results['anomaly_windows'],
                    'total_windows': results['total_windows'],
                    'anomaly_ratio': results['anomaly_ratio'],
                    'max_anomaly_prob': results.get('max_anomaly_prob', 0),
                    'has_real_timestamps': results['has_real_timestamps']
                }
                results_summary.append(summary)
                
                print(f"\n测试结果:")
                print(f"  文件: {summary['file']}")
                print(f"  使用模型: {summary['model_used']}")
                print(f"  异常窗口数: {summary['anomaly_windows']}")
                print(f"  总窗口数: {summary['total_windows']}")
                print(f"  异常比例: {summary['anomaly_ratio']*100:.2f}%")
                print(f"  最大异常概率: {summary['max_anomaly_prob']:.4f}")
                print(f"  真实时间戳: {'是' if summary['has_real_timestamps'] else '否'}")
            else:
                print("分析失败")
                
        except Exception as e:
            print(f"测试失败: {e}")
            continue
    
    # 总结
    print(f"\n{'='*60}")
    print("快速测试总结")
    print(f"{'='*60}")
    
    if results_summary:
        print(f"成功测试 {len(results_summary)} 个文件:")
        print(f"{'序号':<4} {'文件名':<30} {'模型':<10} {'异常率':<8} {'最大概率':<10}")
        print("-" * 70)
        
        for i, result in enumerate(results_summary, 1):
            file_short = result['file'][:25] + "..." if len(result['file']) > 28 else result['file']
            print(f"{i:<4} {file_short:<30} {result['model_used']:<10} {result['anomaly_ratio']*100:>6.2f}% {result['max_anomaly_prob']:>8.4f}")
        
        # 统计
        avg_anomaly_ratio = np.mean([r['anomaly_ratio'] for r in results_summary])
        avg_max_prob = np.mean([r['max_anomaly_prob'] for r in results_summary])
        model_usage = {}
        for r in results_summary:
            model_usage[r['model_used']] = model_usage.get(r['model_used'], 0) + 1
        
        print(f"\n统计信息:")
        print(f"  平均异常比例: {avg_anomaly_ratio*100:.2f}%")
        print(f"  平均最大概率: {avg_max_prob:.4f}")
        print(f"  模型使用统计:")
        for model, count in model_usage.items():
            print(f"    {model}: {count} 次")
    else:
        print("没有成功测试的文件")
    
    print(f"\nPatchTST模型测试完成！")

if __name__ == "__main__":
    quick_test()

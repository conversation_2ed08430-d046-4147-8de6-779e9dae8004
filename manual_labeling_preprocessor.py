#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人工标注的卡钻数据预处理器
专门处理需要人工标注卡钻事件的钻井数据

核心思想：
- 工况标签（起钻、下钻、坐卡等）只是钻井操作类型
- 真正的卡钻事件需要人工识别和标注
- 正负样本基于人工标注的卡钻事件，而非工况标签
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime

class ManualLabelingPreprocessor:
    """
    支持人工标注的钻井数据预处理器
    """
    
    def __init__(self, config=None):
        self.config = config or {
            'window_size': 100,          # 滑动窗口大小
            'step_size': 20,             # 滑动步长
            'min_quality_score': 0.6,    # 最小质量分数
            'prediction_horizon': 10,    # 预测提前量（提前多少个点预测）
        }
        
        # 钻井参数列名映射
        self.column_mapping = {
            'DEP': 'DEP',           # 井深
            'BITDEP': 'BITDEP',     # 钻头深度
            'HOOKLD': 'HKLD',       # 大钩载荷
            'HKLD': 'HKLD',         # 大钩载荷
            'WOB': 'WOB',           # 钻压
            'RPM': 'RPM',           # 转速
            'TOR': 'TOR',           # 扭矩
            'SPP': 'SPP',           # 立管压力
            'CW': 'condition',       # 工况（仅作参考）
        }
    
    def load_data_with_encoding(self, file_path: str) -> pd.DataFrame:
        """
        使用正确编码加载数据
        """
        print(f"🔍 加载数据文件: {file_path}")
        
        encodings = ['gbk', 'gb2312', 'utf-8', 'utf-8-sig', 'cp936']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"✅ 成功使用 {encoding} 编码读取数据")
                print(f"📊 数据形状: {df.shape}")
                return df
            except UnicodeDecodeError:
                continue
        
        raise ValueError("❌ 无法读取文件，请检查文件编码")
    
    def standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        """
        df_std = df.rename(columns=self.column_mapping)
        
        # 确保必要的钻井参数列存在
        required_params = ['DEP', 'BITDEP', 'WOB', 'RPM', 'HKLD', 'TOR', 'SPP']
        missing_params = [p for p in required_params if p not in df_std.columns]
        
        if missing_params:
            print(f"⚠️ 缺少钻井参数: {missing_params}")
        
        # 添加时间索引（如果没有）
        if 'timestamp' not in df_std.columns:
            df_std['timestamp'] = pd.date_range(
                start='2022-01-01', periods=len(df_std), freq='5S'
            )
        
        return df_std
    
    def create_sliding_windows(self, df: pd.DataFrame) -> list:
        """
        创建滑动窗口数据段
        这些窗口将用于人工标注
        """
        print(f"🔧 创建滑动窗口 (窗口大小: {self.config['window_size']}, 步长: {self.config['step_size']})")
        
        windows = []
        window_size = self.config['window_size']
        step_size = self.config['step_size']
        
        # 选择用于分析的参数列
        param_columns = ['DEP', 'BITDEP', 'WOB', 'RPM', 'HKLD', 'TOR', 'SPP']
        available_params = [col for col in param_columns if col in df.columns]
        
        if not available_params:
            raise ValueError("❌ 没有找到可用的钻井参数列")
        
        for i in range(0, len(df) - window_size + 1, step_size):
            window_data = df.iloc[i:i + window_size].copy()
            
            # 计算窗口质量分数
            quality_score = self._calculate_window_quality(window_data, available_params)
            
            if quality_score >= self.config['min_quality_score']:
                window_info = {
                    'window_id': len(windows),
                    'start_index': i,
                    'end_index': i + window_size - 1,
                    'data': window_data,
                    'quality_score': quality_score,
                    'condition_summary': self._summarize_conditions(window_data),
                    'param_stats': self._calculate_param_stats(window_data, available_params),
                    'is_labeled': False,  # 是否已标注
                    'label': None,        # 人工标注的标签（0=正常，1=卡钻前兆）
                    'confidence': None,   # 标注置信度
                    'notes': ''          # 标注备注
                }
                windows.append(window_info)
        
        print(f"✅ 创建了 {len(windows)} 个高质量窗口")
        return windows
    
    def _calculate_window_quality(self, window_data: pd.DataFrame, param_columns: list) -> float:
        """
        计算窗口数据质量分数
        """
        scores = []
        
        for col in param_columns:
            if col in window_data.columns:
                # 数据完整性
                completeness = 1 - window_data[col].isna().sum() / len(window_data)
                
                # 数据变异性（避免全是相同值）
                if window_data[col].std() > 0:
                    variability = min(window_data[col].std() / window_data[col].mean(), 1.0) if window_data[col].mean() != 0 else 0
                else:
                    variability = 0
                
                # 综合分数
                param_score = 0.7 * completeness + 0.3 * variability
                scores.append(param_score)
        
        return np.mean(scores) if scores else 0
    
    def _summarize_conditions(self, window_data: pd.DataFrame) -> dict:
        """
        总结窗口内的工况分布
        """
        if 'condition' in window_data.columns:
            condition_counts = window_data['condition'].value_counts()
            dominant_condition = condition_counts.index[0] if len(condition_counts) > 0 else 'Unknown'
            condition_changes = (window_data['condition'] != window_data['condition'].shift()).sum()
            
            return {
                'dominant_condition': dominant_condition,
                'condition_changes': condition_changes,
                'condition_distribution': condition_counts.to_dict()
            }
        else:
            return {'dominant_condition': 'Unknown', 'condition_changes': 0}
    
    def _calculate_param_stats(self, window_data: pd.DataFrame, param_columns: list) -> dict:
        """
        计算窗口内参数统计信息
        """
        stats = {}
        for col in param_columns:
            if col in window_data.columns:
                stats[col] = {
                    'mean': float(window_data[col].mean()),
                    'std': float(window_data[col].std()),
                    'min': float(window_data[col].min()),
                    'max': float(window_data[col].max()),
                    'trend': self._calculate_trend(window_data[col])
                }
        return stats
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """
        计算参数趋势
        """
        if len(series) < 2:
            return 'stable'
        
        # 简单线性趋势
        x = np.arange(len(series))
        slope = np.polyfit(x, series.dropna(), 1)[0]
        
        if abs(slope) < 0.01:
            return 'stable'
        elif slope > 0:
            return 'increasing'
        else:
            return 'decreasing'
    
    def export_for_labeling(self, windows: list, output_dir: str = "labeling_data"):
        """
        导出数据供人工标注
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print(f"📤 导出标注数据到: {output_path}")
        
        # 1. 创建标注模板
        labeling_template = []
        for window in windows:
            template_row = {
                'window_id': window['window_id'],
                'start_index': window['start_index'],
                'end_index': window['end_index'],
                'quality_score': round(window['quality_score'], 3),
                'dominant_condition': window['condition_summary']['dominant_condition'],
                'condition_changes': window['condition_summary']['condition_changes'],
                'label': '',  # 待填写：0=正常，1=卡钻前兆
                'confidence': '',  # 待填写：1-5分
                'notes': ''  # 待填写：标注说明
            }
            
            # 添加关键参数统计
            for param, stats in window['param_stats'].items():
                template_row[f'{param}_mean'] = round(stats['mean'], 2)
                template_row[f'{param}_trend'] = stats['trend']
            
            labeling_template.append(template_row)
        
        # 保存标注模板
        template_file = output_path / "labeling_template.csv"
        pd.DataFrame(labeling_template).to_csv(template_file, index=False, encoding='utf-8-sig')
        print(f"✅ 标注模板已保存: {template_file}")
        
        # 2. 保存原始窗口数据
        windows_dir = output_path / "window_data"
        windows_dir.mkdir(exist_ok=True)
        
        for window in windows:
            window_file = windows_dir / f"window_{window['window_id']:04d}.csv"
            window['data'].to_csv(window_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 窗口数据已保存到: {windows_dir}")
        
        # 3. 创建标注说明文档
        instructions = self._create_labeling_instructions()
        instructions_file = output_path / "标注说明.md"
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print(f"✅ 标注说明已保存: {instructions_file}")
        
        return template_file
    
    def _create_labeling_instructions(self) -> str:
        """
        创建人工标注说明文档
        """
        return """# 钻井卡钻数据标注说明

## 标注目标
识别钻井过程中可能导致卡钻的早期信号，为预警模型提供训练数据。

## 标注规则

### 标签定义
- **0 = 正常**: 该时间窗口内钻井参数正常，无卡钻风险
- **1 = 卡钻前兆**: 该时间窗口内出现可能导致卡钻的异常信号

### 判断依据
请根据以下钻井参数的异常变化来判断：

1. **钻压 (WOB)**: 
   - 异常增高或波动剧烈
   - 突然下降到零

2. **大钩载荷 (HKLD)**:
   - 异常增高（可能表示阻力增大）
   - 与钻压变化不协调

3. **转速 (RPM)**:
   - 异常下降或停转
   - 波动剧烈

4. **扭矩 (TOR)**:
   - 异常增高
   - 突然下降

5. **立管压力 (SPP)**:
   - 异常增高或下降
   - 与其他参数变化不协调

### 置信度评分 (1-5分)
- **5分**: 非常确定的判断
- **4分**: 比较确定的判断  
- **3分**: 一般确定的判断
- **2分**: 不太确定的判断
- **1分**: 很不确定的判断

### 标注流程
1. 打开 `labeling_template.csv` 文件
2. 查看每个窗口的参数统计信息
3. 如需详细分析，查看对应的 `window_xxxx.csv` 文件
4. 在 `label` 列填写 0 或 1
5. 在 `confidence` 列填写 1-5 的置信度
6. 在 `notes` 列填写判断依据或特殊说明
7. 保存文件

### 注意事项
- 重点关注参数的**异常变化趋势**，而不是绝对数值
- 考虑多个参数的**组合异常**
- 工况标签仅供参考，不是判断依据
- 如有疑问，可在notes列详细说明
"""
    
    def load_labeled_data(self, labeled_file: str) -> list:
        """
        加载人工标注后的数据
        """
        print(f"📥 加载标注数据: {labeled_file}")
        
        labeled_df = pd.read_csv(labeled_file, encoding='utf-8-sig')
        
        # 统计标注情况
        total_windows = len(labeled_df)
        labeled_windows = labeled_df['label'].notna().sum()
        positive_samples = (labeled_df['label'] == 1).sum()
        negative_samples = (labeled_df['label'] == 0).sum()
        
        print(f"📊 标注统计:")
        print(f"  - 总窗口数: {total_windows}")
        print(f"  - 已标注: {labeled_windows}")
        print(f"  - 正样本 (卡钻前兆): {positive_samples}")
        print(f"  - 负样本 (正常): {negative_samples}")
        
        if positive_samples > 0:
            ratio = negative_samples / positive_samples
            print(f"  - 正负比例: 1:{ratio:.1f}")
        
        return labeled_df
    
    def create_training_dataset(self, labeled_file: str, windows_dir: str, 
                              output_dir: str = "training_dataset"):
        """
        基于人工标注创建训练数据集
        """
        print(f"🎯 创建训练数据集...")
        
        # 加载标注数据
        labeled_df = self.load_labeled_data(labeled_file)
        
        # 过滤已标注的数据
        valid_labels = labeled_df['label'].notna()
        labeled_df = labeled_df[valid_labels]
        
        if len(labeled_df) == 0:
            print("❌ 没有找到有效的标注数据")
            return None
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        positive_dir = output_path / "positive"
        negative_dir = output_path / "negative"
        positive_dir.mkdir(exist_ok=True)
        negative_dir.mkdir(exist_ok=True)
        
        # 分离正负样本
        positive_count = 0
        negative_count = 0
        
        for _, row in labeled_df.iterrows():
            window_id = row['window_id']
            label = int(row['label'])
            confidence = row.get('confidence', 3)
            
            # 加载对应的窗口数据
            window_file = Path(windows_dir) / f"window_{window_id:04d}.csv"
            
            if not window_file.exists():
                print(f"⚠️ 窗口数据文件不存在: {window_file}")
                continue
            
            window_data = pd.read_csv(window_file, encoding='utf-8-sig')
            
            # 保存到对应目录
            if label == 1:  # 正样本
                output_file = positive_dir / f"positive_{positive_count:04d}_conf_{confidence}.csv"
                window_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                positive_count += 1
            else:  # 负样本
                output_file = negative_dir / f"negative_{negative_count:04d}_conf_{confidence}.csv"
                window_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                negative_count += 1
        
        # 生成数据集报告
        dataset_info = {
            'creation_time': datetime.now().isoformat(),
            'source_file': labeled_file,
            'total_samples': positive_count + negative_count,
            'positive_samples': positive_count,
            'negative_samples': negative_count,
            'positive_ratio': positive_count / (positive_count + negative_count) if (positive_count + negative_count) > 0 else 0,
            'window_size': self.config['window_size'],
            'step_size': self.config['step_size']
        }
        
        report_file = output_path / "dataset_info.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 训练数据集创建完成:")
        print(f"  - 正样本: {positive_count} 个")
        print(f"  - 负样本: {negative_count} 个")
        print(f"  - 数据集信息: {report_file}")
        
        return dataset_info

def process_drilling_data_for_labeling(input_file: str):
    """
    处理钻井数据，生成标注模板
    """
    config = {
        'window_size': 100,          # 窗口大小：100个数据点
        'step_size': 20,             # 步长：每20个点创建一个窗口
        'min_quality_score': 0.6,    # 最小质量分数
        'prediction_horizon': 10,    # 预测提前量
    }

    preprocessor = ManualLabelingPreprocessor(config)

    try:
        # 1. 加载数据
        df = preprocessor.load_data_with_encoding(input_file)

        # 2. 标准化列名
        df_std = preprocessor.standardize_columns(df)

        # 3. 创建滑动窗口
        windows = preprocessor.create_sliding_windows(df_std)

        # 4. 导出标注模板
        template_file = preprocessor.export_for_labeling(windows)

        print(f"\n🎉 标注模板生成完成!")
        print(f"📁 请打开文件进行人工标注: {template_file}")
        print(f"\n📋 标注完成后，请运行:")
        print(f"python manual_labeling_preprocessor.py --create-dataset")

        return template_file

    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_training_dataset_from_labels():
    """
    从标注结果创建训练数据集
    """
    labeled_file = "labeling_data/labeling_template.csv"
    windows_dir = "labeling_data/window_data"

    if not Path(labeled_file).exists():
        print(f"❌ 标注文件不存在: {labeled_file}")
        print(f"请先运行数据预处理生成标注模板")
        return None

    config = {
        'window_size': 100,
        'step_size': 20,
        'min_quality_score': 0.6,
        'prediction_horizon': 10,
    }

    preprocessor = ManualLabelingPreprocessor(config)

    try:
        dataset_info = preprocessor.create_training_dataset(
            labeled_file, windows_dir
        )

        if dataset_info:
            print(f"\n🎉 训练数据集创建完成!")
            print(f"📊 数据集统计:")
            print(f"  - 总样本数: {dataset_info['total_samples']}")
            print(f"  - 正样本数: {dataset_info['positive_samples']}")
            print(f"  - 负样本数: {dataset_info['negative_samples']}")
            print(f"  - 正样本比例: {dataset_info['positive_ratio']:.3f}")

            print(f"\n📁 训练数据保存在:")
            print(f"  - 正样本: training_dataset/positive/")
            print(f"  - 负样本: training_dataset/negative/")

        return dataset_info

    except Exception as e:
        print(f"❌ 创建数据集失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    主处理流程
    """
    import sys

    print("🎯 人工标注的卡钻数据预处理系统")
    print("=" * 60)

    if len(sys.argv) > 1 and sys.argv[1] == "--create-dataset":
        # 从标注结果创建训练数据集
        create_training_dataset_from_labels()
    else:
        # 处理原始数据，生成标注模板
        input_file = "your_drilling_data.csv"  # 请替换为您的实际文件路径

        print(f"📁 输入文件: {input_file}")

        if Path(input_file).exists():
            process_drilling_data_for_labeling(input_file)
        else:
            print(f"\n⚠️ 文件不存在: {input_file}")
            print(f"请将您的钻井数据文件路径替换到 input_file 变量中")

            print(f"\n💡 完整使用流程:")
            print(f"1. 修改 input_file 为您的数据文件路径")
            print(f"2. 运行: python manual_labeling_preprocessor.py")
            print(f"3. 人工标注生成的 labeling_template.csv 文件")
            print(f"4. 运行: python manual_labeling_preprocessor.py --create-dataset")
            print(f"5. 使用生成的训练数据集训练模型")

if __name__ == "__main__":
    main()

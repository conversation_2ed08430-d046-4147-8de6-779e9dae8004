#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
早期信号检测实验管理器
支持多种预设实验配置和批量实验运行

作者: TSlib项目组
日期: 2024-06-29
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 导入自定义配置类
from run_earlysignaldet import EarlySignalDetectionConfig, run_early_signal_detection


class ExperimentManager:
    """实验管理器"""
    
    def __init__(self):
        self.experiments = {}
        self.results_dir = Path('./experiment_results')
        self.results_dir.mkdir(exist_ok=True)
        
        # 定义预设实验配置
        self._define_preset_experiments()
    
    def _define_preset_experiments(self):
        """定义预设实验配置"""
        
        # 基础实验
        self.experiments['baseline'] = {
            'name': '基础实验 - PatchTST默认配置',
            'description': '使用默认参数的基础实验',
            'config': {}
        }
        
        # 快速测试实验
        self.experiments['quick_test'] = {
            'name': '快速测试',
            'description': '用于快速验证代码的小规模实验',
            'config': {
                'train_epochs': 5,
                'batch_size': 8,
                'patience': 3,
                'des': 'QuickTest'
            }
        }
        
        # 大模型实验
        self.experiments['large_model'] = {
            'name': '大模型实验',
            'description': '使用更大的模型容量',
            'config': {
                'd_model': 256,
                'd_ff': 512,
                'e_layers': 4,
                'n_heads': 16,
                'batch_size': 8,
                'des': 'LargeModel'
            }
        }
        
        # 小学习率实验
        self.experiments['low_lr'] = {
            'name': '小学习率实验',
            'description': '使用较小的学习率进行精细调优',
            'config': {
                'learning_rate': 0.0005,
                'train_epochs': 150,
                'patience': 15,
                'des': 'LowLR'
            }
        }
        
        # 大批次实验
        self.experiments['large_batch'] = {
            'name': '大批次实验',
            'description': '使用更大的批次大小',
            'config': {
                'batch_size': 32,
                'learning_rate': 0.002,
                'des': 'LargeBatch'
            }
        }
        
        # 深层网络实验
        self.experiments['deep_network'] = {
            'name': '深层网络实验',
            'description': '使用更深的网络结构',
            'config': {
                'e_layers': 6,
                'd_model': 192,
                'd_ff': 384,
                'dropout': 0.2,
                'des': 'DeepNetwork'
            }
        }
        
        # 仅预测实验
        self.experiments['predict_only'] = {
            'name': '仅预测实验',
            'description': '使用已训练模型进行预测',
            'config': {
                'is_training': 0,
                'do_predict': True,
                'des': 'PredictOnly'
            }
        }
    
    def list_experiments(self):
        """列出所有可用的实验配置"""
        print("\n" + "="*70)
        print("🧪 可用实验配置")
        print("="*70)
        
        for exp_id, exp_info in self.experiments.items():
            print(f"\n📋 {exp_id}:")
            print(f"   名称: {exp_info['name']}")
            print(f"   描述: {exp_info['description']}")
            print(f"   参数: {len(exp_info['config'])} 个自定义参数")
            
            if exp_info['config']:
                print("   主要配置:")
                for key, value in list(exp_info['config'].items())[:5]:
                    print(f"     - {key}: {value}")
                if len(exp_info['config']) > 5:
                    print(f"     - ... 还有 {len(exp_info['config']) - 5} 个参数")
        
        print("\n" + "="*70)
    
    def run_experiment(self, experiment_id, save_results=True):
        """运行指定的实验"""
        if experiment_id not in self.experiments:
            print(f"❌ 错误: 未找到实验配置 '{experiment_id}'")
            self.list_experiments()
            return False
        
        exp_info = self.experiments[experiment_id]
        print(f"\n🚀 开始运行实验: {exp_info['name']}")
        print(f"📝 描述: {exp_info['description']}")
        
        # 创建配置对象
        config = EarlySignalDetectionConfig()
        
        # 应用实验特定配置
        if exp_info['config']:
            config.update_config(**exp_info['config'])
        
        # 记录开始时间
        start_time = time.time()
        start_datetime = datetime.now()
        
        # 运行实验
        success = run_early_signal_detection(config)
        
        # 记录结束时间
        end_time = time.time()
        end_datetime = datetime.now()
        duration = end_time - start_time
        
        # 保存实验结果
        if save_results:
            self._save_experiment_results(
                experiment_id, exp_info, config, 
                success, start_datetime, end_datetime, duration
            )
        
        if success:
            print(f"\n✅ 实验 '{experiment_id}' 完成成功!")
            print(f"⏱️  耗时: {duration:.2f} 秒 ({duration/60:.1f} 分钟)")
        else:
            print(f"\n❌ 实验 '{experiment_id}' 执行失败!")
        
        return success
    
    def _save_experiment_results(self, exp_id, exp_info, config, success, start_time, end_time, duration):
        """保存实验结果"""
        result_data = {
            'experiment_id': exp_id,
            'experiment_name': exp_info['name'],
            'description': exp_info['description'],
            'success': success,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'config': exp_info['config'],
            'full_config': vars(config)
        }
        
        # 保存到JSON文件
        timestamp = start_time.strftime("%Y%m%d_%H%M%S")
        result_file = self.results_dir / f"{exp_id}_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📊 实验结果已保存: {result_file}")
    
    def run_batch_experiments(self, experiment_ids):
        """批量运行多个实验"""
        print(f"\n🔄 开始批量运行 {len(experiment_ids)} 个实验...")
        
        results = {}
        total_start_time = time.time()
        
        for i, exp_id in enumerate(experiment_ids, 1):
            print(f"\n{'='*50}")
            print(f"📊 批量实验进度: {i}/{len(experiment_ids)}")
            print(f"🧪 当前实验: {exp_id}")
            print(f"{'='*50}")
            
            success = self.run_experiment(exp_id)
            results[exp_id] = success
            
            if i < len(experiment_ids):
                print(f"\n⏳ 等待 5 秒后开始下一个实验...")
                time.sleep(5)
        
        total_duration = time.time() - total_start_time
        
        # 打印批量实验总结
        print(f"\n{'='*70}")
        print("📈 批量实验总结")
        print(f"{'='*70}")
        print(f"⏱️  总耗时: {total_duration:.2f} 秒 ({total_duration/60:.1f} 分钟)")
        print(f"✅ 成功: {sum(results.values())} 个")
        print(f"❌ 失败: {len(results) - sum(results.values())} 个")
        
        print("\n📋 详细结果:")
        for exp_id, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {exp_id}: {status}")
        
        return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='早期信号检测实验管理器')
    parser.add_argument('--list', action='store_true', help='列出所有可用实验')
    parser.add_argument('--run', type=str, help='运行指定实验')
    parser.add_argument('--batch', nargs='+', help='批量运行多个实验')
    
    args = parser.parse_args()
    
    manager = ExperimentManager()
    
    if args.list:
        manager.list_experiments()
    elif args.run:
        manager.run_experiment(args.run)
    elif args.batch:
        manager.run_batch_experiments(args.batch)
    else:
        # 交互式模式
        print("🧪 早期信号检测实验管理器")
        print("请选择操作:")
        print("1. 列出所有实验配置")
        print("2. 运行单个实验")
        print("3. 批量运行实验")
        print("4. 退出")
        
        while True:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                manager.list_experiments()
            elif choice == '2':
                manager.list_experiments()
                exp_id = input("\n请输入实验ID: ").strip()
                if exp_id:
                    manager.run_experiment(exp_id)
            elif choice == '3':
                manager.list_experiments()
                exp_ids = input("\n请输入实验ID列表 (用空格分隔): ").strip().split()
                if exp_ids:
                    manager.run_batch_experiments(exp_ids)
            elif choice == '4':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()

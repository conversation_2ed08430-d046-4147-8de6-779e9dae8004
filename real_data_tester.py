#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据测试器
使用已训练的工况专用模型对真实钻井数据进行预测测试
"""

import sys
import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import json
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from condition_specific_trainer import ConditionSpecificTrainer

class RealDataTester:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.feature_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
        self.condition_mapping = {
            '钻进': '正常钻进',
            '起钻': '起钻', 
            '下钻': '下钻',
            '正划眼': '正划眼',
            '倒划眼': '倒划眼'
        }
        self.models = {}
        self.scalers = {}
        
    def load_trained_models(self):
        """加载所有已训练的工况模型"""
        print("加载已训练的工况模型...")
        
        checkpoints_dir = Path("checkpoints")
        conditions = ['正常钻进', '起钻', '下钻', '正划眼']
        
        for condition in conditions:
            model_path = checkpoints_dir / condition / "best_model.pth"
            
            if model_path.exists():
                try:
                    # 加载检查点
                    checkpoint = torch.load(model_path, map_location='cpu')
                    
                    # 创建训练器来重建模型
                    trainer = ConditionSpecificTrainer(
                        condition_name=condition,
                        model_config=checkpoint['model_config'],
                        training_config=checkpoint['training_config']
                    )
                    
                    # 创建模型并加载权重
                    model = trainer.create_model()
                    model.load_state_dict(checkpoint['model_state_dict'])
                    model.eval()
                    model = model.to(self.device)
                    
                    self.models[condition] = model
                    
                    # 加载标准化器（如果有的话）
                    if 'scaler_mean' in checkpoint and 'scaler_scale' in checkpoint:
                        scaler = StandardScaler()
                        scaler.mean_ = checkpoint['scaler_mean']
                        scaler.scale_ = checkpoint['scaler_scale']
                        self.scalers[condition] = scaler
                    
                    print(f"  成功加载: {condition}")
                    
                except Exception as e:
                    print(f"  加载失败 {condition}: {e}")
            else:
                print(f"  模型不存在: {condition}")
        
        print(f"共加载 {len(self.models)} 个模型")
        
    def preprocess_data(self, data, condition):
        """预处理数据"""
        # 选择特征列
        if not all(col in data.columns for col in self.feature_cols):
            missing_cols = [col for col in self.feature_cols if col not in data.columns]
            raise ValueError(f"数据缺少必要列: {missing_cols}")
        
        features = data[self.feature_cols].copy()
        
        # 处理缺失值
        features = features.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        # 标准化（如果有对应的scaler）
        if condition in self.scalers:
            features = pd.DataFrame(
                self.scalers[condition].transform(features),
                columns=self.feature_cols,
                index=features.index
            )
        else:
            # 简单标准化
            features = (features - features.mean()) / (features.std() + 1e-8)
        
        return features
    
    def create_time_windows(self, data, window_size=180, stride=60):
        """创建时间窗口"""
        windows = []
        
        for i in range(0, len(data) - window_size + 1, stride):
            window = data.iloc[i:i+window_size].values
            if window.shape[0] == window_size:
                windows.append(window)
        
        return np.array(windows) if windows else np.empty((0, window_size, len(self.feature_cols)))
    
    def predict_condition(self, data, condition):
        """使用指定工况模型进行预测"""
        if condition not in self.models:
            return None, f"工况 '{condition}' 的模型不存在"
        
        try:
            # 预处理数据
            processed_data = self.preprocess_data(data, condition)
            
            # 创建时间窗口
            windows = self.create_time_windows(processed_data)
            
            if len(windows) == 0:
                return None, "数据长度不足，无法创建时间窗口"
            
            # 转换为tensor
            X = torch.FloatTensor(windows).to(self.device)
            
            # 预测
            model = self.models[condition]
            predictions = []
            probabilities = []
            
            with torch.no_grad():
                for i in range(0, len(X), 32):  # 批处理
                    batch = X[i:i+32]
                    outputs = model(batch)
                    
                    # 获取概率
                    probs = torch.softmax(outputs, dim=1)
                    probabilities.extend(probs.cpu().numpy())
                    
                    # 获取预测类别
                    _, predicted = torch.max(outputs, 1)
                    predictions.extend(predicted.cpu().numpy())
            
            return {
                'predictions': predictions,
                'probabilities': probabilities,
                'windows_count': len(windows),
                'anomaly_ratio': np.mean(predictions),
                'max_anomaly_prob': max([prob[1] for prob in probabilities]) if len(probabilities[0]) > 1 else 0
            }, None
            
        except Exception as e:
            return None, f"预测失败: {e}"
    
    def test_single_file(self, file_path, expected_condition=None):
        """测试单个文件"""
        print(f"\n测试文件: {file_path}")
        print("-" * 50)
        
        try:
            # 读取数据
            data = pd.read_csv(file_path, encoding='utf-8')
            print(f"数据形状: {data.shape}")
            
            # 检查RIGSTA列以确定工况
            if 'RIGSTA' in data.columns:
                conditions_in_data = data['RIGSTA'].value_counts()
                print(f"数据中的工况分布:")
                for condition, count in conditions_in_data.items():
                    print(f"  {condition}: {count} 条记录")
                
                # 确定主要工况
                main_condition = conditions_in_data.index[0]
                if main_condition in self.condition_mapping:
                    test_condition = self.condition_mapping[main_condition]
                else:
                    test_condition = expected_condition or '正常钻进'
            else:
                test_condition = expected_condition or '正常钻进'
                print(f"未找到RIGSTA列，使用默认工况: {test_condition}")
            
            print(f"使用模型: {test_condition}")
            
            # 进行预测
            result, error = self.predict_condition(data, test_condition)
            
            if error:
                print(f"预测失败: {error}")
                return None
            
            # 显示结果
            print(f"预测结果:")
            print(f"  时间窗口数量: {result['windows_count']}")
            print(f"  异常窗口比例: {result['anomaly_ratio']:.4f}")
            print(f"  最高异常概率: {result['max_anomaly_prob']:.4f}")
            
            # 判断整体风险等级
            if result['max_anomaly_prob'] > 0.8:
                risk_level = "高风险"
            elif result['max_anomaly_prob'] > 0.5:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            print(f"  风险等级: {risk_level}")
            
            return {
                'file': str(file_path),
                'condition': test_condition,
                'windows_count': int(result['windows_count']),
                'anomaly_ratio': float(result['anomaly_ratio']),
                'max_anomaly_prob': float(result['max_anomaly_prob']),
                'risk_level': risk_level,
                'predictions': result['predictions']
            }
            
        except Exception as e:
            print(f"测试失败: {e}")
            return None
    
    def test_directory(self, directory_path, max_files=10):
        """测试目录中的多个文件"""
        print(f"测试目录: {directory_path}")
        print("=" * 60)
        
        directory = Path(directory_path)
        if not directory.exists():
            print(f"目录不存在: {directory_path}")
            return []
        
        # 获取CSV文件
        csv_files = list(directory.glob("*.csv"))[:max_files]
        
        if not csv_files:
            print("目录中没有找到CSV文件")
            return []
        
        print(f"找到 {len(csv_files)} 个CSV文件，测试前 {min(len(csv_files), max_files)} 个")
        
        results = []
        for file_path in csv_files:
            result = self.test_single_file(file_path)
            if result:
                results.append(result)
        
        # 生成总结
        if results:
            print(f"\n{'='*60}")
            print("测试总结")
            print(f"{'='*60}")
            
            high_risk_count = sum(1 for r in results if r['risk_level'] == '高风险')
            medium_risk_count = sum(1 for r in results if r['risk_level'] == '中风险')
            low_risk_count = sum(1 for r in results if r['risk_level'] == '低风险')
            
            print(f"测试文件数量: {len(results)}")
            print(f"高风险文件: {high_risk_count}")
            print(f"中风险文件: {medium_risk_count}")
            print(f"低风险文件: {low_risk_count}")
            
            avg_anomaly_ratio = np.mean([r['anomaly_ratio'] for r in results])
            avg_max_prob = np.mean([r['max_anomaly_prob'] for r in results])
            
            print(f"平均异常比例: {avg_anomaly_ratio:.4f}")
            print(f"平均最高异常概率: {avg_max_prob:.4f}")
        
        return results
    
    def save_results(self, results, output_file="real_data_test_report.md"):
        """保存测试结果"""
        if results:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# 真实数据测试报告\n\n")
                f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("## 测试总结\n\n")
                high_risk_count = sum(1 for r in results if r['risk_level'] == '高风险')
                medium_risk_count = sum(1 for r in results if r['risk_level'] == '中风险')
                low_risk_count = sum(1 for r in results if r['risk_level'] == '低风险')

                f.write(f"- 测试文件数量: {len(results)}\n")
                f.write(f"- 高风险文件: {high_risk_count}\n")
                f.write(f"- 中风险文件: {medium_risk_count}\n")
                f.write(f"- 低风险文件: {low_risk_count}\n")

                avg_anomaly_ratio = sum(r['anomaly_ratio'] for r in results) / len(results)
                avg_max_prob = sum(r['max_anomaly_prob'] for r in results) / len(results)

                f.write(f"- 平均异常比例: {avg_anomaly_ratio:.4f}\n")
                f.write(f"- 平均最高异常概率: {avg_max_prob:.4f}\n\n")

                f.write("## 详细结果\n\n")
                f.write("| 文件名 | 工况 | 时间窗口数 | 异常比例 | 最高异常概率 | 风险等级 |\n")
                f.write("|--------|------|------------|----------|--------------|----------|\n")

                for result in results:
                    filename = Path(result['file']).name
                    f.write(f"| {filename} | {result['condition']} | {result['windows_count']} | "
                           f"{result['anomaly_ratio']:.4f} | {result['max_anomaly_prob']:.4f} | {result['risk_level']} |\n")

            print(f"\n测试结果已保存到: {output_file}")

def main():
    """主函数"""
    tester = RealDataTester()
    
    # 加载模型
    tester.load_trained_models()
    
    if not tester.models:
        print("没有可用的模型，请先训练模型")
        return
    
    # 测试519测试井数据
    test_dir = "519测试井"
    results = tester.test_directory(test_dir, max_files=5)
    
    # 保存结果
    tester.save_results(results)

if __name__ == "__main__":
    main()

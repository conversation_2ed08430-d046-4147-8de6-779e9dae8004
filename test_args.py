#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数配置脚本
用于验证所有参数是否正确定义

作者: TSlib项目组
日期: 2024-06-29
"""

import sys
import argparse
from pathlib import Path

def create_args_for_earlysignaldet():
    """创建早期信号检测的参数配置"""
    
    # 创建参数解析器（模拟run.py的参数）
    parser = argparse.ArgumentParser(description='Early Signal Detection')
    
    # 基础配置
    parser.add_argument('--task_name', type=str, default='earlysignaldet')
    parser.add_argument('--is_training', type=int, default=1)
    parser.add_argument('--model_id', type=str, default='earlysignaldetection')
    parser.add_argument('--model', type=str, default='PatchTST')
    
    # 数据配置
    parser.add_argument('--data', type=str, default='Earlysignaldet')
    parser.add_argument('--root_path', type=str, default='./dataset/earlysignaldetection')
    parser.add_argument('--data_path', type=str, default='')
    parser.add_argument('--features', type=str, default='M')
    parser.add_argument('--target', type=str, default='label')
    parser.add_argument('--freq', type=str, default='h')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/')
    
    # 序列配置
    parser.add_argument('--seq_len', type=int, default=96)
    parser.add_argument('--label_len', type=int, default=48)
    parser.add_argument('--pred_len', type=int, default=24)
    parser.add_argument('--seasonal_patterns', type=str, default='Monthly')
    parser.add_argument('--inverse', action='store_true', default=False)
    
    # 模型配置
    parser.add_argument('--e_layers', type=int, default=3)
    parser.add_argument('--d_layers', type=int, default=1)
    parser.add_argument('--d_model', type=int, default=128)
    parser.add_argument('--d_ff', type=int, default=256)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--factor', type=int, default=1)
    parser.add_argument('--enc_in', type=int, default=10)
    parser.add_argument('--dec_in', type=int, default=10)
    parser.add_argument('--c_out', type=int, default=2)
    parser.add_argument('--num_class', type=int, default=2)
    
    # 训练配置
    parser.add_argument('--batch_size', type=int, default=16)
    parser.add_argument('--learning_rate', type=float, default=0.001)
    parser.add_argument('--train_epochs', type=int, default=100)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--dropout', type=float, default=0.1)
    
    # PatchTST特有参数
    parser.add_argument('--patch_len', type=int, default=16)
    parser.add_argument('--stride', type=int, default=8)
    parser.add_argument('--fc_dropout', type=float, default=0.1)
    parser.add_argument('--head_dropout', type=float, default=0.0)
    parser.add_argument('--padding_patch', type=str, default='end')
    parser.add_argument('--revin', type=int, default=1)
    parser.add_argument('--affine', type=int, default=0)
    parser.add_argument('--subtract_last', type=int, default=0)
    parser.add_argument('--decomposition', type=int, default=0)
    parser.add_argument('--kernel_size', type=int, default=25)
    parser.add_argument('--individual', type=int, default=0)
    
    # 其他配置
    parser.add_argument('--embed', type=str, default='timeF')
    parser.add_argument('--distil', action='store_true', default=True)
    parser.add_argument('--mix', action='store_true', default=True)
    parser.add_argument('--activation', type=str, default='gelu')
    parser.add_argument('--output_attention', action='store_true', default=False)
    parser.add_argument('--use_amp', action='store_true', default=False)
    parser.add_argument('--lradj', type=str, default='type1')
    
    # print_args.py需要的参数
    parser.add_argument('--num_kernels', type=int, default=6)
    parser.add_argument('--moving_avg', type=int, default=25)
    parser.add_argument('--num_workers', type=int, default=10)
    parser.add_argument('--loss', type=str, default='MSE')
    
    # 异常检测任务参数
    parser.add_argument('--anomaly_ratio', type=float, default=0.25)
    
    # 填充任务参数
    parser.add_argument('--mask_rate', type=float, default=0.25)
    
    # 设备配置
    parser.add_argument('--use_gpu', type=bool, default=True)
    parser.add_argument('--gpu', type=int, default=0)
    parser.add_argument('--use_multi_gpu', action='store_true', default=False)
    parser.add_argument('--devices', type=str, default='0,1,2,3')
    
    # 实验配置
    parser.add_argument('--des', type=str, default='Exp')
    parser.add_argument('--itr', type=int, default=1)
    parser.add_argument('--top_k', type=int, default=3)
    
    # de-stationary projector 参数
    parser.add_argument('--p_hidden_dims', type=int, nargs='+', default=[128, 128])
    parser.add_argument('--p_hidden_layers', type=int, default=2)
    
    # 预测配置
    parser.add_argument('--do_predict', action='store_true', default=False)
    
    return parser

def main():
    """主函数"""
    print("🧪 参数配置测试脚本")
    print("="*60)
    
    # 创建参数解析器
    parser = create_args_for_earlysignaldet()
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 设置GPU
    import torch
    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False
    
    if args.use_gpu and args.use_multi_gpu:
        args.devices = args.devices.replace(' ', '')
        device_ids = args.devices.split(',')
        args.device_ids = [int(id_) for id_ in device_ids]
        args.gpu = args.device_ids[0]
    
    try:
        print("📦 导入print_args模块...")
        from utils.print_args import print_args
        print("✅ 模块导入成功")
        
        print('\n📊 实验参数:')
        print_args(args)
        
        print("\n✅ 所有参数配置正确!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

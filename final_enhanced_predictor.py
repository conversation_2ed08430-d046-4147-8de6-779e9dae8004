#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版增强钻井卡钻预测系统
完美解决时间戳显示、未知工况处理、结果格式优化问题
"""

import sys
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime, timedelta
import glob
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from real_data_tester import RealDataTester

class FinalEnhancedPredictor(RealDataTester):
    def __init__(self):
        super().__init__()
        
        # 扩展的工况映射规则
        self.condition_mapping = {
            # 标准工况
            '钻进': '正常钻进',
            '正常钻进': '正常钻进',
            '起钻': '起钻', 
            '下钻': '下钻',
            '正划眼': '正划眼',
            '倒划眼': '倒划眼',
            
            # 其他工况映射规则
            '其他': '正常钻进',  # 默认映射到正常钻进
            '循环': '正常钻进',  # 循环作业映射到正常钻进
            '接单根': '下钻',    # 接单根作业映射到下钻
            '卸单根': '起钻',    # 卸单根作业映射到起钻
            '短起': '起钻',      # 短起映射到起钻
            '短下': '下钻',      # 短下映射到下钻
            '划眼': '正划眼',    # 划眼映射到正划眼
            '扩眼': '正划眼',    # 扩眼映射到正划眼
            '通井': '正常钻进',  # 通井映射到正常钻进
            '洗井': '正常钻进',  # 洗井映射到正常钻进
            '测试': '正常钻进',  # 测试映射到正常钻进
            '停钻': '正常钻进',  # 停钻映射到正常钻进
        }
        
        # 未知工况处理策略
        self.unknown_condition_strategy = 'skip'  # 'default_model' 或 'skip'
        self.default_model = '正常钻进'
        
    def get_condition_for_prediction(self, rigsta_value):
        """根据RIGSTA值确定预测使用的工况模型"""
        if rigsta_value in self.condition_mapping:
            return self.condition_mapping[rigsta_value], False
        else:
            # 处理未知工况
            if self.unknown_condition_strategy == 'default_model':
                print(f"警告: 未知工况 '{rigsta_value}'，使用默认模型 '{self.default_model}'")
                return self.default_model, True
            else:
                print(f"警告: 未知工况 '{rigsta_value}'，跳过预测")
                return None, True
    
    def create_windows_with_real_timestamps(self, data, window_size=180, stride=60):
        """创建时间窗口并生成真实时间戳"""
        timestamps = []

        # 检查是否有date列
        has_date_column = 'date' in data.columns

        if has_date_column:
            # 确保date列为datetime类型
            if not pd.api.types.is_datetime64_any_dtype(data['date']):
                data['date'] = pd.to_datetime(data['date'])
            print(f"使用真实时间戳，时间范围: {data['date'].min()} 到 {data['date'].max()}")
        else:
            print("注意: 数据中没有date列，将使用窗口索引作为时间戳")

        # 只返回时间戳，窗口数据将在后面单独处理
        for i in range(0, len(data) - window_size + 1, stride):
            if has_date_column:
                # 使用窗口结束时间作为时间戳
                end_time = data.iloc[i+window_size-1]['date']
                timestamps.append(end_time)
            else:
                # 使用窗口索引
                timestamps.append(f"Window_{len(timestamps)+1}")

        return timestamps
    
    def analyze_file_final(self, file_path, create_visualization=False):
        """最终版文件分析，包含真实时间戳和完整工况处理"""
        print(f"\n{'='*80}")
        print(f"最终版增强分析: {Path(file_path).name}")
        print(f"{'='*80}")
        
        try:
            # 读取数据
            data = pd.read_csv(file_path, encoding='utf-8')
            print(f"数据形状: {data.shape}")
            print(f"数据列: {list(data.columns)}")
            
            # 处理时间列
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                print(f"时间范围: {data['date'].min()} 到 {data['date'].max()}")
                time_span = data['date'].max() - data['date'].min()
                print(f"时间跨度: {time_span}")
                
                # 计算数据频率
                time_diff = data['date'].diff().dropna()
                avg_interval = time_diff.mean()
                print(f"平均采样间隔: {avg_interval}")
            
            # 分析工况分布
            condition_stats = {}
            unknown_conditions = []
            
            if 'RIGSTA' in data.columns:
                conditions_in_data = data['RIGSTA'].value_counts()
                print(f"\n工况分布详细分析:")
                print(f"{'原始工况':<15} {'记录数':<10} {'占比':<8} {'映射模型':<12} {'状态'}")
                print("-" * 65)
                
                for condition, count in conditions_in_data.items():
                    mapped_condition, is_unknown = self.get_condition_for_prediction(condition)
                    percentage = count/len(data)*100
                    status = "未知工况" if is_unknown else "已知工况"
                    
                    print(f"{condition:<15} {count:<10} {percentage:<7.1f}% {mapped_condition or 'N/A':<12} {status}")
                    
                    if mapped_condition:
                        if mapped_condition not in condition_stats:
                            condition_stats[mapped_condition] = 0
                        condition_stats[mapped_condition] += count
                    
                    if is_unknown:
                        unknown_conditions.append(condition)
                
                # 确定主要使用的模型
                if condition_stats:
                    main_model = max(condition_stats.items(), key=lambda x: x[1])[0]
                    print(f"\n模型使用统计:")
                    for model, count in condition_stats.items():
                        print(f"  {model}: {count} 条记录 ({count/len(data)*100:.1f}%)")
                else:
                    main_model = self.default_model
            else:
                main_model = self.default_model
                print(f"未找到RIGSTA列，使用默认模型: {main_model}")
            
            print(f"\n最终使用模型: {main_model}")
            if unknown_conditions:
                print(f"发现未知工况: {unknown_conditions}")
            
            # 创建时间戳（使用原始数据保留时间戳）
            timestamps = self.create_windows_with_real_timestamps(data)

            # 预处理特征数据用于预测
            processed_data = self.preprocess_data(data, main_model)

            # 创建预处理后的时间窗口
            windows, _ = self.create_time_windows(processed_data)

            if len(windows) == 0:
                print("错误: 数据长度不足，无法创建时间窗口")
                return None

            print(f"\n时间窗口创建完成:")
            print(f"  窗口大小: {windows.shape[1]} 个时间点 (3分钟)")
            print(f"  特征数量: {windows.shape[2]} 个特征")
            print(f"  总窗口数: {len(windows)} 个")
            print(f"  滑动步长: 60 个时间点 (1分钟)")
            
            # 进行预测
            model = self.models.get(main_model)
            if not model:
                print(f"错误: 没有找到模型 {main_model}")
                return None
            
            # 预测
            predictions = []
            probabilities = []
            
            print(f"\n开始预测...")
            
            with torch.no_grad():
                for i in range(0, len(windows), 32):  # 批处理
                    batch = windows[i:i+32]
                    batch_tensor = torch.FloatTensor(batch).to(self.device)
                    
                    outputs = model(batch_tensor)
                    probs = torch.softmax(outputs, dim=1)
                    preds = torch.argmax(probs, dim=1)
                    
                    predictions.extend(preds.cpu().numpy())
                    probabilities.extend(probs.cpu().numpy())
            
            # 分析结果
            anomaly_count = sum(predictions)
            anomaly_ratio = np.mean(predictions)
            
            print(f"\n预测结果概览:")
            print(f"  总时间窗口数: {len(predictions)}")
            print(f"  异常窗口数: {anomaly_count}")
            print(f"  正常窗口数: {len(predictions) - anomaly_count}")
            print(f"  异常比例: {anomaly_ratio*100:.2f}%")
            
            # 创建详细结果
            results_data = []
            
            for i, (timestamp, pred, prob) in enumerate(zip(timestamps, predictions, probabilities)):
                anomaly_prob = prob[1] if len(prob) > 1 else 0
                normal_prob = prob[0] if len(prob) > 0 else 0
                
                # 确定风险等级
                if anomaly_prob > 0.9:
                    risk_level = "极高风险"
                elif anomaly_prob > 0.8:
                    risk_level = "高风险"
                elif anomaly_prob > 0.6:
                    risk_level = "中风险"
                else:
                    risk_level = "低风险"
                
                # 只保存异常的时间窗口
                if pred == 1:
                    results_data.append({
                        '真实时间戳': timestamp,
                        '异常概率': anomaly_prob,
                        '正常概率': normal_prob,
                        '风险等级': risk_level,
                        '使用模型': main_model,
                        '窗口索引': i,
                        '对应数据行开始': i * 60,
                        '对应数据行结束': i * 60 + 180,
                        '是否异常': '是',
                        '文件名': Path(file_path).name,
                        '未知工况': ', '.join(unknown_conditions) if unknown_conditions else '无'
                    })
            
            results_df = pd.DataFrame(results_data)
            
            # 保存结果
            if not results_df.empty:
                filename = Path(file_path).stem
                output_file = f"{filename}_最终版预测结果.csv"
                results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                print(f"\n最终版预测结果已保存到: {output_file}")
                
                # 显示前10个异常时间点
                print(f"\n前10个异常时间点详情:")
                print(f"{'序号':<4} {'真实时间戳':<20} {'异常概率':<10} {'风险等级':<10} {'数据行范围'}")
                print("-" * 70)
                
                for i, row in results_df.head(10).iterrows():
                    timestamp_str = str(row['真实时间戳'])
                    if len(timestamp_str) > 19:
                        timestamp_str = timestamp_str[:19]
                    data_range = f"{row['对应数据行开始']}-{row['对应数据行结束']}"
                    print(f"{i+1:<4} {timestamp_str:<20} {row['异常概率']:<10.4f} {row['风险等级']:<10} {data_range}")
                
                if len(results_df) > 10:
                    print(f"... 还有 {len(results_df) - 10} 个异常点")
                
                # 显示最高风险时间点
                max_risk_idx = results_df['异常概率'].idxmax()
                max_risk_row = results_df.loc[max_risk_idx]
                print(f"\n最高风险时间点详情:")
                print(f"  时间戳: {max_risk_row['真实时间戳']}")
                print(f"  异常概率: {max_risk_row['异常概率']:.4f}")
                print(f"  风险等级: {max_risk_row['风险等级']}")
                print(f"  对应数据行: {max_risk_row['对应数据行开始']} - {max_risk_row['对应数据行结束']}")
                
                # 风险等级统计
                risk_stats = results_df['风险等级'].value_counts()
                print(f"\n风险等级分布:")
                for risk_level, count in risk_stats.items():
                    percentage = count / len(results_df) * 100
                    print(f"  {risk_level}: {count} 个时间窗口 ({percentage:.1f}%)")
                
                # 创建简单可视化
                if create_visualization:
                    self.create_simple_visualization(results_df, filename)
                
            else:
                print("没有检测到异常时间窗口")
            
            return {
                'file': str(file_path),
                'model_used': main_model,
                'total_windows': len(predictions),
                'anomaly_windows': anomaly_count,
                'anomaly_ratio': anomaly_ratio,
                'unknown_conditions': unknown_conditions,
                'results_df': results_df,
                'has_real_timestamps': 'date' in data.columns
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_simple_visualization(self, results_df, filename):
        """创建简单的可视化图表"""
        if results_df.empty:
            return
        
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            fig.suptitle(f'钻井卡钻预测分析 - {filename}', fontsize=14, fontweight='bold')
            
            # 1. 异常概率时间序列图
            if pd.api.types.is_datetime64_any_dtype(results_df['真实时间戳']):
                x_data = results_df['真实时间戳']
                ax1.set_xlabel('时间')
            else:
                x_data = range(len(results_df))
                ax1.set_xlabel('时间窗口索引')
            
            # 根据风险等级着色
            colors = []
            for level in results_df['风险等级']:
                if level == '极高风险':
                    colors.append('red')
                elif level == '高风险':
                    colors.append('orange')
                elif level == '中风险':
                    colors.append('yellow')
                else:
                    colors.append('green')
            
            ax1.scatter(x_data, results_df['异常概率'], c=colors, alpha=0.7, s=50)
            ax1.plot(x_data, results_df['异常概率'], 'b-', alpha=0.3, linewidth=1)
            ax1.set_title('异常概率时间序列')
            ax1.set_ylabel('异常概率')
            ax1.grid(True, alpha=0.3)
            
            # 添加风险阈值线
            ax1.axhline(y=0.9, color='red', linestyle='--', alpha=0.5, label='极高风险阈值')
            ax1.axhline(y=0.8, color='orange', linestyle='--', alpha=0.5, label='高风险阈值')
            ax1.axhline(y=0.6, color='yellow', linestyle='--', alpha=0.5, label='中风险阈值')
            ax1.legend()
            
            # 2. 风险等级分布饼图
            risk_counts = results_df['风险等级'].value_counts()
            colors_pie = ['red', 'orange', 'yellow', 'green']
            ax2.pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%', 
                    colors=colors_pie[:len(risk_counts)])
            ax2.set_title('风险等级分布')
            
            plt.tight_layout()
            
            # 保存图表
            chart_file = f"{filename}_最终版可视化.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            print(f"可视化图表已保存到: {chart_file}")
            
            plt.show()
            
        except Exception as e:
            print(f"可视化创建失败: {e}")

def main():
    """主函数"""
    predictor = FinalEnhancedPredictor()
    
    # 加载模型
    predictor.load_trained_models()
    
    if not predictor.models:
        print("没有可用的模型，请先训练模型")
        return
    
    # 动态获取测试文件列表
    test_data_dir = Path("dataset/processed_data/test_data/")
    if test_data_dir.exists():
        test_files = list(test_data_dir.glob("*.csv"))
        test_files = [str(f) for f in test_files]  # 转换为字符串路径
        print(f"找到 {len(test_files)} 个测试文件:")
        for i, file in enumerate(test_files, 1):
            print(f"  {i}. {Path(file).name}")
    else:
        print(f"测试数据目录不存在: {test_data_dir}")
        test_files = []
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n开始最终版增强分析: {Path(test_file).name}")
            results = predictor.analyze_file_final(test_file, create_visualization=True)
            
            if results:
                print(f"\n分析完成总结:")
                print(f"  文件: {Path(results['file']).name}")
                print(f"  使用模型: {results['model_used']}")
                print(f"  异常窗口数: {results['anomaly_windows']}")
                print(f"  异常比例: {results['anomaly_ratio']*100:.2f}%")
                print(f"  真实时间戳: {'是' if results['has_real_timestamps'] else '否'}")
                if results['unknown_conditions']:
                    print(f"  未知工况: {results['unknown_conditions']}")
            
            print(f"\n{'='*80}")
            input("按回车键继续分析下一个文件...")
        else:
            print(f"文件不存在: {test_file}")

if __name__ == "__main__":
    main()

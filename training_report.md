# 工况专用PatchTST模型训练报告

生成时间: 2025-06-30 05:45:36

## 训练结果总结

| 工况 | 状态 | 最佳验证准确率 | 最佳轮次 | 总训练轮次 |
|------|------|----------------|----------|------------|
| 正常钻进 | 成功 | 0.9962 | 9 | 10 |
| 起钻 | 成功 | 0.9963 | 5 | 10 |
| 下钻 | 成功 | 1.0000 | 1 | 10 |
| 正划眼 | 成功 | 0.9761 | 9 | 10 |
| 倒划眼 | 未训练 | - | - | - |

## 统计信息

- 成功训练的工况数量: 4/5
- 平均验证准确率: 0.9921
- 最佳模型: 下钻 (准确率: 1.0000)

## 模型文件

- 正常钻进: `checkpoints/正常钻进/best_model.pth`
- 起钻: `checkpoints/起钻/best_model.pth`
- 下钻: `checkpoints/下钻/best_model.pth`
- 正划眼: `checkpoints/正划眼/best_model.pth`

## 使用说明

每个工况模型都是独立训练的，可以用于对应工况的钻井卡钻预测。
模型输入为180个时间点的10维特征数据（3分钟窗口），输出为二分类结果（正常/异常）。

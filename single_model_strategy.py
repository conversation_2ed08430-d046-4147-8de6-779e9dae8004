# 单模型多工况预测策略实现方案
# 在PatchTST模型中集成工况特征的统一预测系统

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler, LabelEncoder

class EnhancedWorkConditionClassifier:
    """增强的工况分类器"""
    
    def __init__(self):
        self.condition_mapping = {
            0: '起钻',      # Tripping up
            1: '下钻',      # Tripping down  
            2: '正常钻进',   # Normal drilling
            3: '正划眼',    # Forward reaming
            4: '倒划眼'     # Reverse reaming
        }
        
        # 工况特征编码器
        self.condition_encoder = LabelEncoder()
        self.condition_encoder.fit(list(range(5)))
        
    def extract_condition_features(self, data: pd.DataFrame) -> np.ndarray:
        """
        提取工况相关的统计特征
        返回形状: (seq_len, condition_features)
        """
        condition_features = []
        
        # 滑动窗口统计特征
        window_size = 10
        
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            window_data = data.iloc[start_idx:i+1]
            
            features = []
            
            # WOB相关特征
            features.extend([
                window_data['WOB'].mean(),
                window_data['WOB'].std(),
                (window_data['WOB'] > 50).sum() / len(window_data),  # 高WOB比例
                (window_data['WOB'] < 10).sum() / len(window_data),  # 低WOB比例
            ])
            
            # RPM相关特征  
            features.extend([
                window_data['RPM'].mean(),
                window_data['RPM'].std(),
                (window_data['RPM'] > 60).sum() / len(window_data),  # 高RPM比例
            ])
            
            # 深度变化特征
            dep_diff = window_data['DEP'].diff().fillna(0)
            features.extend([
                dep_diff.mean(),
                dep_diff.std(),
                (dep_diff > 0.1).sum() / len(window_data),   # 下钻比例
                (dep_diff < -0.1).sum() / len(window_data),  # 起钻比例
            ])
            
            # HKLD相关特征
            features.extend([
                window_data['HKLD'].mean(),
                window_data['HKLD'].std(),
            ])
            
            # TOR相关特征
            features.extend([
                window_data['TOR'].mean(),
                window_data['TOR'].std(),
            ])
            
            # 钻进效率特征
            if len(window_data) > 1:
                time_diff = (pd.to_datetime(window_data['date'].iloc[-1]) - 
                           pd.to_datetime(window_data['date'].iloc[0])).total_seconds()
                if time_diff > 0:
                    drilling_rate = abs(dep_diff.sum()) / time_diff * 3600  # m/h
                else:
                    drilling_rate = 0
            else:
                drilling_rate = 0
            features.append(drilling_rate)
            
            condition_features.append(features)
        
        return np.array(condition_features)
    
    def classify_conditions(self, data: pd.DataFrame) -> np.ndarray:
        """基于规则的工况分类"""
        conditions = np.zeros(len(data))
        
        # 计算关键指标的滑动平均
        window = 5
        wob_ma = data['WOB'].rolling(window=window, center=True).mean()
        rpm_ma = data['RPM'].rolling(window=window, center=True).mean()
        dep_diff = data['DEP'].diff().rolling(window=window, center=True).mean()
        hkld_ma = data['HKLD'].rolling(window=window, center=True).mean()
        tor_ma = data['TOR'].rolling(window=window, center=True).mean()
        
        for i in range(len(data)):
            wob = wob_ma.iloc[i] if not pd.isna(wob_ma.iloc[i]) else data['WOB'].iloc[i]
            rpm = rpm_ma.iloc[i] if not pd.isna(rpm_ma.iloc[i]) else data['RPM'].iloc[i]
            dep_change = dep_diff.iloc[i] if not pd.isna(dep_diff.iloc[i]) else 0
            hkld = hkld_ma.iloc[i] if not pd.isna(hkld_ma.iloc[i]) else data['HKLD'].iloc[i]
            tor = tor_ma.iloc[i] if not pd.isna(tor_ma.iloc[i]) else data['TOR'].iloc[i]
            
            # 改进的工况判断逻辑
            if wob < 20 and dep_change < -0.05 and hkld > 800:  # 起钻
                conditions[i] = 0
            elif wob < 20 and dep_change > 0.05 and hkld < 800:  # 下钻
                conditions[i] = 1
            elif wob > 40 and rpm > 50 and abs(dep_change) > 0.02:  # 正常钻进
                conditions[i] = 2
            elif 20 <= wob <= 60 and rpm > 30 and abs(dep_change) < 0.02:  # 正划眼
                conditions[i] = 3
            else:  # 倒划眼或其他特殊工况
                conditions[i] = 4
                
        return conditions.astype(int)

class UnifiedConditionDataLoader:
    """统一的工况数据加载器"""
    
    def __init__(self, data_path: str, seq_len: int = 152):
        self.data_path = data_path
        self.seq_len = seq_len
        self.condition_classifier = EnhancedWorkConditionClassifier()
        self.scaler = StandardScaler()
        
    def load_unified_dataset(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        加载统一数据集
        返回: (时间序列特征, 工况特征, 工况标签, 卡钻标签)
        """
        all_sequences = []
        all_condition_features = []
        all_condition_labels = []
        all_drilling_labels = []
        
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    
                    # 确定卡钻标签
                    if 'earlysignal' in root:
                        drilling_label = 1  # 卡钻征兆
                    else:
                        drilling_label = 0  # 正常
                    
                    try:
                        # 读取数据
                        df = pd.read_csv(file_path, encoding='utf-8')
                        if len(df) < 20:  # 过滤太短的序列
                            continue
                        
                        # 确保有date列
                        if 'date' not in df.columns:
                            df['date'] = pd.date_range(start='2023-01-01', periods=len(df), freq='5S')
                        
                        # 提取基础时间序列特征
                        feature_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 
                                      'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
                        
                        # 检查列是否存在
                        available_cols = [col for col in feature_cols if col in df.columns]
                        if len(available_cols) < 8:  # 至少需要8个特征
                            continue
                            
                        # 填充缺失的列
                        for col in feature_cols:
                            if col not in df.columns:
                                df[col] = 0.0
                        
                        # 工况分类
                        condition_labels = self.condition_classifier.classify_conditions(df)
                        
                        # 提取工况特征
                        condition_features = self.condition_classifier.extract_condition_features(df)
                        
                        # 序列分段处理
                        for start_idx in range(0, len(df) - self.seq_len + 1, self.seq_len // 2):
                            end_idx = start_idx + self.seq_len
                            
                            # 提取序列片段
                            seq_data = df[feature_cols].iloc[start_idx:end_idx].values
                            seq_condition_features = condition_features[start_idx:end_idx]
                            seq_condition_labels = condition_labels[start_idx:end_idx]
                            
                            # 填充到固定长度
                            if seq_data.shape[0] < self.seq_len:
                                pad_len = self.seq_len - seq_data.shape[0]
                                seq_data = np.pad(seq_data, ((0, pad_len), (0, 0)), mode='constant')
                                seq_condition_features = np.pad(seq_condition_features, 
                                                              ((0, pad_len), (0, 0)), mode='constant')
                                seq_condition_labels = np.pad(seq_condition_labels, 
                                                            (0, pad_len), mode='constant')
                            
                            all_sequences.append(seq_data)
                            all_condition_features.append(seq_condition_features)
                            all_condition_labels.append(seq_condition_labels)
                            all_drilling_labels.append(drilling_label)
                            
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {e}")
                        continue
        
        # 转换为numpy数组
        sequences = np.array(all_sequences)
        condition_features = np.array(all_condition_features)
        condition_labels = np.array(all_condition_labels)
        drilling_labels = np.array(all_drilling_labels)
        
        print(f"加载完成: {len(sequences)} 个序列")
        print(f"序列形状: {sequences.shape}")
        print(f"工况特征形状: {condition_features.shape}")
        
        return sequences, condition_features, condition_labels, drilling_labels

class UnifiedPatchTSTModel(nn.Module):
    """集成工况特征的统一PatchTST模型"""
    
    def __init__(self, seq_len: int = 152, d_model: int = 128, 
                 n_heads: int = 8, num_layers: int = 3, 
                 condition_feature_dim: int = 16):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.patch_len = 16
        self.stride = 8
        self.condition_feature_dim = condition_feature_dim
        
        # 计算patch数量
        self.patch_num = (seq_len - self.patch_len) // self.stride + 1
        
        # 时间序列patch embedding
        self.ts_patch_embedding = nn.Linear(self.patch_len * 10, d_model)  # 10个基础特征
        
        # 工况特征embedding
        self.condition_embedding = nn.Linear(condition_feature_dim, d_model)
        
        # 工况标签embedding
        self.condition_label_embedding = nn.Embedding(5, d_model // 4)  # 5种工况
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(self.patch_num, d_model))
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(d_model + d_model + d_model // 4, d_model),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=n_heads, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 多任务输出头
        self.drilling_classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 2)  # 卡钻预测
        )
        
        self.condition_classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 5)  # 工况分类
        )
        
    def forward(self, ts_data, condition_features, condition_labels):
        """
        前向传播
        ts_data: (batch_size, seq_len, 10) - 时间序列数据
        condition_features: (batch_size, seq_len, condition_feature_dim) - 工况特征
        condition_labels: (batch_size, seq_len) - 工况标签
        """
        batch_size = ts_data.size(0)
        
        # 创建时间序列patches
        ts_patches = []
        condition_patches = []
        label_patches = []
        
        for i in range(0, self.seq_len - self.patch_len + 1, self.stride):
            # 时间序列patch
            ts_patch = ts_data[:, i:i+self.patch_len, :].reshape(batch_size, -1)
            ts_patches.append(ts_patch)
            
            # 工况特征patch (取平均)
            cond_patch = condition_features[:, i:i+self.patch_len, :].mean(dim=1)
            condition_patches.append(cond_patch)
            
            # 工况标签patch (取众数)
            label_patch = torch.mode(condition_labels[:, i:i+self.patch_len], dim=1)[0]
            label_patches.append(label_patch)
        
        if len(ts_patches) == 0:
            ts_patches = [ts_data[:, :self.patch_len, :].reshape(batch_size, -1)]
            condition_patches = [condition_features[:, :self.patch_len, :].mean(dim=1)]
            label_patches = [torch.mode(condition_labels[:, :self.patch_len], dim=1)[0]]
        
        # Stack patches
        ts_patches = torch.stack(ts_patches, dim=1)  # (batch_size, patch_num, patch_len*10)
        condition_patches = torch.stack(condition_patches, dim=1)  # (batch_size, patch_num, cond_dim)
        label_patches = torch.stack(label_patches, dim=1)  # (batch_size, patch_num)
        
        # Embeddings
        ts_embedded = self.ts_patch_embedding(ts_patches)  # (batch_size, patch_num, d_model)
        condition_embedded = self.condition_embedding(condition_patches)  # (batch_size, patch_num, d_model)
        label_embedded = self.condition_label_embedding(label_patches)  # (batch_size, patch_num, d_model//4)
        
        # 特征融合
        fused_features = torch.cat([ts_embedded, condition_embedded, label_embedded], dim=-1)
        fused_embedded = self.feature_fusion(fused_features)  # (batch_size, patch_num, d_model)
        
        # Add positional encoding
        fused_embedded = fused_embedded + self.pos_encoding.unsqueeze(0)
        
        # Transformer encoding
        encoded = self.transformer(fused_embedded)  # (batch_size, patch_num, d_model)
        
        # Global average pooling
        pooled = encoded.mean(dim=1)  # (batch_size, d_model)
        
        # 多任务输出
        drilling_output = self.drilling_classifier(pooled)  # 卡钻预测
        condition_output = self.condition_classifier(pooled)  # 工况分类
        
        return {
            'drilling_risk': drilling_output,
            'condition_prediction': condition_output
        }

# 使用示例
if __name__ == "__main__":
    # 1. 数据加载
    data_loader = UnifiedConditionDataLoader('./dataset/earlysignaldetection')
    sequences, condition_features, condition_labels, drilling_labels = data_loader.load_unified_dataset()
    
    # 2. 创建模型
    model = UnifiedPatchTSTModel(
        seq_len=152,
        d_model=128,
        condition_feature_dim=condition_features.shape[-1]
    )
    
    # 3. 示例前向传播
    if len(sequences) > 0:
        sample_ts = torch.FloatTensor(sequences[:4])
        sample_cond_feat = torch.FloatTensor(condition_features[:4])
        sample_cond_label = torch.LongTensor(condition_labels[:4])
        
        with torch.no_grad():
            output = model(sample_ts, sample_cond_feat, sample_cond_label)
            print(f"卡钻预测输出形状: {output['drilling_risk'].shape}")
            print(f"工况预测输出形状: {output['condition_prediction'].shape}")

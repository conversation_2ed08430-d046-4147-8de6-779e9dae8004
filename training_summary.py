#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工况专用模型训练总结
检查已训练的模型并生成总结报告
"""

import os
import json
import torch
from pathlib import Path
from datetime import datetime

def check_trained_models():
    """检查已训练的模型"""
    checkpoints_dir = Path("checkpoints")
    
    if not checkpoints_dir.exists():
        print("没有找到checkpoints目录")
        return
    
    print("工况专用PatchTST模型训练总结")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    conditions = ['正常钻进', '起钻', '下钻', '正划眼', '倒划眼']
    trained_models = []
    
    for condition in conditions:
        condition_dir = checkpoints_dir / condition
        model_file = condition_dir / "best_model.pth"
        history_file = condition_dir / "training_history.json"
        
        if model_file.exists() and history_file.exists():
            try:
                # 加载训练历史
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                
                # 加载模型检查点
                checkpoint = torch.load(model_file, map_location='cpu')
                
                print(f"\n成功 {condition}")
                print(f"   最佳验证准确率: {history['best_val_acc']:.4f}")
                print(f"   最佳轮次: {history['best_epoch']}")
                print(f"   总训练轮次: {len(history['train_loss'])}")
                print(f"   最终训练准确率: {history['train_acc'][-1]:.4f}")
                print(f"   最终验证准确率: {history['val_acc'][-1]:.4f}")
                print(f"   模型文件大小: {model_file.stat().st_size / 1024:.1f} KB")
                
                trained_models.append({
                    'condition': condition,
                    'best_val_acc': history['best_val_acc'],
                    'best_epoch': history['best_epoch'],
                    'total_epochs': len(history['train_loss']),
                    'final_train_acc': history['train_acc'][-1],
                    'final_val_acc': history['val_acc'][-1],
                    'model_size_kb': model_file.stat().st_size / 1024
                })
                
            except Exception as e:
                print(f"\n失败 {condition} (加载失败: {e})")
        else:
            print(f"\n未训练 {condition}")
    
    # 总结统计
    if trained_models:
        print(f"\n{'='*60}")
        print("训练统计总结")
        print(f"{'='*60}")
        print(f"成功训练的工况数量: {len(trained_models)}/5")
        
        avg_acc = sum(model['best_val_acc'] for model in trained_models) / len(trained_models)
        print(f"平均验证准确率: {avg_acc:.4f}")
        
        best_model = max(trained_models, key=lambda x: x['best_val_acc'])
        print(f"最佳模型: {best_model['condition']} (准确率: {best_model['best_val_acc']:.4f})")
        
        total_size = sum(model['model_size_kb'] for model in trained_models)
        print(f"模型总大小: {total_size:.1f} KB")
        
        print(f"\n模型文件位置:")
        for model in trained_models:
            print(f"  {model['condition']}: checkpoints/{model['condition']}/best_model.pth")
    
    return trained_models

def check_data_availability():
    """检查数据可用性"""
    print(f"\n{'='*60}")
    print("数据可用性检查")
    print(f"{'='*60}")
    
    processed_data_dir = Path("dataset/processed_data")
    conditions = ['正常钻进', '起钻', '下钻', '正划眼', '倒划眼']
    
    for condition in conditions:
        symptom_dir = processed_data_dir / "symptom_data" / condition
        normal_dir = processed_data_dir / "normal_data" / condition
        
        symptom_files = list(symptom_dir.glob("*.csv")) if symptom_dir.exists() else []
        normal_files = list(normal_dir.glob("*.csv")) if normal_dir.exists() else []
        
        if symptom_files or normal_files:
            print(f"有数据 {condition}: 征兆数据 {len(symptom_files)} 个, 正常数据 {len(normal_files)} 个")
        else:
            print(f"无数据 {condition}")

def generate_training_report():
    """生成训练报告"""
    trained_models = check_trained_models()
    check_data_availability()
    
    # 生成Markdown报告
    report_file = Path("training_report.md")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 工况专用PatchTST模型训练报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 训练结果总结\n\n")
        f.write("| 工况 | 状态 | 最佳验证准确率 | 最佳轮次 | 总训练轮次 |\n")
        f.write("|------|------|----------------|----------|------------|\n")
        
        conditions = ['正常钻进', '起钻', '下钻', '正划眼', '倒划眼']
        for condition in conditions:
            model = next((m for m in trained_models if m['condition'] == condition), None)
            if model:
                f.write(f"| {condition} | 成功 | {model['best_val_acc']:.4f} | {model['best_epoch']} | {model['total_epochs']} |\n")
            else:
                f.write(f"| {condition} | 未训练 | - | - | - |\n")
        
        if trained_models:
            f.write(f"\n## 统计信息\n\n")
            f.write(f"- 成功训练的工况数量: {len(trained_models)}/5\n")
            avg_acc = sum(model['best_val_acc'] for model in trained_models) / len(trained_models)
            f.write(f"- 平均验证准确率: {avg_acc:.4f}\n")
            best_model = max(trained_models, key=lambda x: x['best_val_acc'])
            f.write(f"- 最佳模型: {best_model['condition']} (准确率: {best_model['best_val_acc']:.4f})\n")
        
        f.write(f"\n## 模型文件\n\n")
        for model in trained_models:
            f.write(f"- {model['condition']}: `checkpoints/{model['condition']}/best_model.pth`\n")
        
        f.write(f"\n## 使用说明\n\n")
        f.write("每个工况模型都是独立训练的，可以用于对应工况的钻井卡钻预测。\n")
        f.write("模型输入为180个时间点的10维特征数据（3分钟窗口），输出为二分类结果（正常/异常）。\n")
    
    print(f"\n训练报告已保存到: {report_file}")

if __name__ == "__main__":
    generate_training_report()

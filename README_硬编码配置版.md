# 早期信号检测模型 - 硬编码配置版使用说明

## 📋 概述

`run_earlysignaldet_simple.py` 是早期信号检测模型的硬编码配置版本。所有参数都直接在代码中定义，无需命令行参数，使用更加简单直观。

## 🚀 快速开始

### 1. 直接运行（使用默认配置）
```bash
python run_earlysignaldet_simple.py
```

### 2. 修改配置参数
打开 `run_earlysignaldet_simple.py` 文件，在顶部找到配置区域：

```python
# ============================================================================
# 🔧 主要配置参数 - 在这里修改您的实验设置
# ============================================================================

# 🎯 核心训练参数
TRAIN_EPOCHS = 100          # 训练轮数 (建议: 5-200)
BATCH_SIZE = 16             # 批次大小 (建议: 8-32)
LEARNING_RATE = 0.001       # 学习率 (建议: 0.0001-0.01)
D_MODEL = 128               # 模型维度 (建议: 64-512)
E_LAYERS = 3                # 编码器层数 (建议: 1-6)
PATIENCE = 10               # 早停耐心值 (建议: 5-20)

# 🚀 运行模式配置
IS_TRAINING = 1             # 是否训练模式 (1=训练, 0=仅测试)
DO_PREDICT = True           # 是否进行预测 (True/False)
```

## 🎯 常用配置组合

### 快速测试配置
```python
TRAIN_EPOCHS = 5            # 快速测试，只训练5轮
BATCH_SIZE = 8              # 小批次，减少内存使用
PATIENCE = 3                # 快速早停
DO_PREDICT = False          # 跳过预测阶段
```

### 完整训练配置
```python
TRAIN_EPOCHS = 100          # 完整训练
BATCH_SIZE = 16             # 标准批次大小
PATIENCE = 10               # 标准早停耐心值
DO_PREDICT = True           # 包含预测阶段
```

### 仅测试配置
```python
IS_TRAINING = 0             # 不进行训练
DO_PREDICT = True           # 仅进行预测
```

### 高性能配置
```python
TRAIN_EPOCHS = 200          # 更多训练轮数
BATCH_SIZE = 32             # 更大批次（需要更多GPU内存）
D_MODEL = 256               # 更大模型维度
E_LAYERS = 6                # 更多编码器层
```

## 📊 参数详细说明

### 核心训练参数
- **TRAIN_EPOCHS**: 训练轮数，控制模型训练的总轮数
- **BATCH_SIZE**: 批次大小，影响训练速度和内存使用
- **LEARNING_RATE**: 学习率，控制模型参数更新的步长
- **D_MODEL**: 模型维度，影响模型容量和性能
- **E_LAYERS**: 编码器层数，影响模型深度和表达能力
- **PATIENCE**: 早停耐心值，验证损失不改善时等待的轮数

### 运行模式配置
- **IS_TRAINING**: 是否训练模式
  - `1`: 进行训练
  - `0`: 跳过训练，直接测试
- **DO_PREDICT**: 是否进行预测
  - `True`: 训练/测试后进行预测
  - `False`: 跳过预测阶段

### 模型配置
- **MODEL_TYPE**: 模型类型，默认为 'PatchTST'
- **TASK_NAME**: 任务名称，固定为 'earlysignaldet'
- **DATA_NAME**: 数据集名称，固定为 'Earlysignaldet'

## 🔧 高级参数调优

### 性能优化
```python
# 提高训练速度
BATCH_SIZE = 32             # 增大批次大小
N_HEADS = 16                # 增加注意力头数

# 提高模型性能
D_MODEL = 256               # 增大模型维度
E_LAYERS = 6                # 增加编码器层数
D_FF = 512                  # 增大前馈网络维度
```

### 内存优化
```python
# 减少内存使用
BATCH_SIZE = 8              # 减小批次大小
D_MODEL = 64                # 减小模型维度
E_LAYERS = 2                # 减少编码器层数
```

### PatchTST特有参数
```python
PATCH_LEN = 16              # 补丁长度，影响时间序列分割
STRIDE = 8                  # 步长，影响补丁重叠程度
DROPOUT = 0.1               # Dropout率，防止过拟合
```

## 📈 实验结果示例

运行脚本后，您将看到类似以下的输出：

```
🚀 早期信号检测模型 - 硬编码配置版启动器
============================================================
📋 当前配置参数:
  训练轮数: 100
  批次大小: 16
  学习率: 0.001
  模型维度: 128
  编码器层数: 3
  是否训练: 是
  是否预测: 是
  模型类型: PatchTST

🚀 开始运行早期信号检测模型...
------------------------------------------------------------
Use GPU: cuda:0
>>>>>>>start training : earlysignaldet_...
Epoch: 1, Steps: 46 | Train Loss: 4.656 Vali Loss: 3.148 Vali Acc: 0.780
Test Acc: 0.783 Test precision: 0.635 Test recall: 0.788 Test f1: 0.651
```

## 🛠️ 故障排除

### 常见问题

1. **内存不足错误**
   - 解决方案：减小 `BATCH_SIZE` 或 `D_MODEL`

2. **训练时间过长**
   - 解决方案：减少 `TRAIN_EPOCHS` 或增大 `BATCH_SIZE`

3. **模型性能不佳**
   - 解决方案：增加 `TRAIN_EPOCHS`、`D_MODEL` 或 `E_LAYERS`

4. **预测阶段路径错误**
   - 这是正常现象，预测需要特定的数据路径配置

## 📝 注意事项

1. **参数修改后需要保存文件**：修改配置参数后，记得保存文件再运行
2. **GPU内存限制**：根据您的GPU内存调整 `BATCH_SIZE` 和 `D_MODEL`
3. **训练时间**：完整训练可能需要较长时间，建议先用小参数测试
4. **模型保存**：训练完成的模型会自动保存到 `./checkpoints/` 目录

## 🔄 与命令行版本的对比

| 特性 | 硬编码配置版 | 命令行参数版 |
|------|-------------|-------------|
| 使用难度 | 简单 | 中等 |
| 参数修改 | 编辑代码 | 命令行参数 |
| 配置管理 | 代码内管理 | 外部参数 |
| 批量实验 | 需要修改代码 | 脚本化容易 |
| 推荐场景 | 单次实验、学习使用 | 批量实验、生产环境 |

选择适合您需求的版本进行使用！

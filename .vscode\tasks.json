{"version": "2.0.0", "tasks": [{"label": "🚀 运行早期信号检测 - 完整流程", "type": "shell", "command": "python", "args": ["run_earlysignaldet.py"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🎯 运行早期信号检测 - 仅训练", "type": "shell", "command": "python", "args": ["run_earlysignaldet.py", "--no_predict"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🔮 运行早期信号检测 - 仅预测", "type": "shell", "command": "python", "args": ["run_earlysignaldet.py", "--no_training"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "⚡ 快速测试 - 早期信号检测", "type": "shell", "command": "python", "args": ["run_earlysignaldet.py", "--train_epochs", "5", "--batch_size", "8", "--patience", "3"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📊 检查数据集", "type": "shell", "command": "python", "args": ["-c", "import os; print('数据集文件:'); [print(f'  {root}/{file}') for root, dirs, files in os.walk('./dataset/earlysignaldetection') for file in files[:3] if file.endswith('.csv')]"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🧹 清理缓存文件", "type": "shell", "command": "python", "args": ["-c", "import shutil, os; [shutil.rmtree(root) for root, dirs, files in os.walk('.') for d in dirs if d == '__pycache__' and shutil.rmtree(os.path.join(root, d)) is None]; print('缓存清理完成')"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📦 检查依赖", "type": "shell", "command": "python", "args": ["-c", "import torch, numpy, pandas, sklearn; print(f'✅ PyTorch: {torch.__version__}'); print(f'✅ NumPy: {numpy.__version__}'); print(f'✅ Pandas: {pandas.__version__}'); print(f'✅ Scikit-learn: {sklearn.__version__}'); print(f'✅ CUDA可用: {torch.cuda.is_available()}')"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📈 查看训练结果", "type": "shell", "command": "python", "args": ["-c", "import os; results_dir = './results'; print('训练结果:') if os.path.exists(results_dir) else print('暂无训练结果'); [print(f'  📁 {d}') for d in os.listdir(results_dir) if os.path.isdir(os.path.join(results_dir, d))] if os.path.exists(results_dir) else None"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🔧 安装依赖", "type": "shell", "command": "pip", "args": ["install", "torch", "numpy", "pandas", "scikit-learn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "tqdm"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}
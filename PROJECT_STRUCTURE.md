# 钻井卡钻预测项目结构

## 核心文件

### 数据处理
- `drilling_stuck_data_exporter.py` - 钻井卡钻数据导出器
- `drilling_data_postprocessor.py` - 钻井数据后处理器
- `卡钻事件表.csv` - 卡钻事件配置文件

### 模型训练
- `condition_specific_data_loader.py` - 工况专用数据加载器
- `condition_specific_trainer.py` - 工况专用训练系统
- `train_single_condition.py` - 单工况训练脚本

### 模型文件
- `checkpoints/正常钻进/` - 正常钻进工况模型
- `checkpoints/起钻/` - 起钻工况模型
- `checkpoints/下钻/` - 下钻工况模型
- `checkpoints/正划眼/` - 正划眼工况模型

### 数据集
- `dataset/processed_data/` - 处理后的训练数据
  - `symptom_data/` - 征兆数据（按工况分类）
  - `normal_data/` - 正常数据（按工况分类）
  - `test_data/` - 测试数据（混合工况）
- `519测试井/` - 原始测试数据

### 报告文件
- `training_report.md` - 模型训练报告
- `test_report.md` - 模型测试报告
- `test_results.json` - 详细测试结果

### TSlib框架
- `models/` - 时间序列模型库
- `data_provider/` - 数据提供器
- `exp/` - 实验框架
- `layers/` - 神经网络层
- `utils/` - 工具函数
- `scripts/` - 各种任务脚本

### 配置文件
- `run.py` - 主运行脚本
- `run_earlysignaldet.py` - 早期信号检测运行脚本
- `requirements.txt` - Python依赖

## 归档文件 (archive/)
- `documentation/` - 项目文档
- `training_scripts/` - 训练相关脚本
- `deprecated_scripts/` - 已废弃的脚本
- `old_checkpoints/` - 旧的模型检查点

## 使用流程
1. 使用 `drilling_stuck_data_exporter.py` 导出原始数据
2. 使用 `drilling_data_postprocessor.py` 处理数据
3. 使用 `train_single_condition.py` 训练各工况模型
4. 使用训练好的模型进行预测

## 模型性能
- 正常钻进: 99.50% 测试准确率
- 起钻: 100.00% 测试准确率  
- 下钻: 100.00% 测试准确率
- 正划眼: 98.01% 测试准确率

# 多工况预测策略实现方案
# 基于TSlib框架和PatchTST模型的分工况预测系统

import os
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional

class WorkConditionClassifier:
    """工况分类器 - 用于识别五种钻进工况"""
    
    def __init__(self):
        self.condition_mapping = {
            0: '起钻',      # Tripping up
            1: '下钻',      # Tripping down  
            2: '正常钻进',   # Normal drilling
            3: '正划眼',    # Forward reaming
            4: '倒划眼'     # Reverse reaming
        }
        
    def classify_by_drilling_parameters(self, data: pd.DataFrame) -> np.ndarray:
        """
        基于钻进参数特征识别工况
        
        参数规则（需要根据实际钻进经验调整）：
        - 起钻: WOB≈0, RPM低, HKLD高, 深度递减
        - 下钻: WOB≈0, RPM低, HKLD中等, 深度递增  
        - 正常钻进: WOB>0, R<PERSON>高, TOR高, 深度递增
        - 正划眼: WOB中等, R<PERSON>中高, 深度变化小
        - 倒划眼: WOB低, RPM反向或特殊模式
        """
        conditions = np.zeros(len(data))
        
        # 计算关键指标
        wob_mean = data['WOB'].rolling(window=5).mean()
        rpm_mean = data['RPM'].rolling(window=5).mean()
        dep_diff = data['DEP'].diff()
        hkld_mean = data['HKLD'].rolling(window=5).mean()
        tor_mean = data['TOR'].rolling(window=5).mean()
        
        for i in range(len(data)):
            wob = wob_mean.iloc[i] if not pd.isna(wob_mean.iloc[i]) else 0
            rpm = rpm_mean.iloc[i] if not pd.isna(rpm_mean.iloc[i]) else 0
            dep_change = dep_diff.iloc[i] if not pd.isna(dep_diff.iloc[i]) else 0
            hkld = hkld_mean.iloc[i] if not pd.isna(hkld_mean.iloc[i]) else 0
            tor = tor_mean.iloc[i] if not pd.isna(tor_mean.iloc[i]) else 0
            
            # 工况判断逻辑
            if wob < 10 and dep_change < -0.1:  # 起钻
                conditions[i] = 0
            elif wob < 10 and dep_change > 0.1:  # 下钻
                conditions[i] = 1
            elif wob > 50 and rpm > 60 and abs(dep_change) > 0.05:  # 正常钻进
                conditions[i] = 2
            elif 10 <= wob <= 50 and rpm > 30 and abs(dep_change) < 0.05:  # 正划眼
                conditions[i] = 3
            else:  # 倒划眼或其他
                conditions[i] = 4
                
        return conditions.astype(int)

class MultiConditionDataLoader:
    """多工况数据加载器"""
    
    def __init__(self, data_path: str):
        self.data_path = data_path
        self.condition_classifier = WorkConditionClassifier()
        
    def load_and_classify_data(self) -> Dict[int, List[Tuple[np.ndarray, int]]]:
        """
        加载数据并按工况分类
        返回: {工况ID: [(时间序列数据, 标签), ...]}
        """
        condition_data = {i: [] for i in range(5)}
        
        # 遍历所有数据文件
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    
                    # 确定标签（基于文件夹）
                    if 'earlysignal' in root:
                        label = 1  # 卡钻征兆
                    else:
                        label = 0  # 正常
                    
                    try:
                        # 读取数据
                        df = pd.read_csv(file_path, encoding='utf-8')
                        if len(df) < 10:  # 过滤太短的序列
                            continue
                            
                        # 工况分类
                        conditions = self.condition_classifier.classify_by_drilling_parameters(df)
                        
                        # 按工况分组数据
                        for condition_id in range(5):
                            condition_mask = conditions == condition_id
                            if condition_mask.sum() > 10:  # 确保有足够的数据点
                                condition_df = df[condition_mask]
                                
                                # 提取特征（排除date列）
                                feature_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 
                                              'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
                                features = condition_df[feature_cols].values
                                
                                condition_data[condition_id].append((features, label))
                                
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {e}")
                        continue
        
        return condition_data

class ConditionSpecificModel(nn.Module):
    """特定工况的PatchTST模型"""
    
    def __init__(self, condition_id: int, seq_len: int = 152, 
                 d_model: int = 128, n_heads: int = 8, num_layers: int = 3):
        super().__init__()
        self.condition_id = condition_id
        self.condition_name = WorkConditionClassifier().condition_mapping[condition_id]
        
        # PatchTST相关参数
        self.seq_len = seq_len
        self.d_model = d_model
        self.patch_len = 16
        self.stride = 8
        
        # 计算patch数量
        self.patch_num = (seq_len - self.patch_len) // self.stride + 1
        
        # Patch embedding
        self.patch_embedding = nn.Linear(self.patch_len * 10, d_model)  # 10个特征
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(self.patch_num, d_model))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=n_heads, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 2)  # 二分类：正常/卡钻征兆
        )
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, features)
        batch_size = x.size(0)
        
        # 创建patches
        patches = []
        for i in range(0, self.seq_len - self.patch_len + 1, self.stride):
            patch = x[:, i:i+self.patch_len, :].reshape(batch_size, -1)
            patches.append(patch)
        
        if len(patches) == 0:
            patches = [x[:, :self.patch_len, :].reshape(batch_size, -1)]
        
        patches = torch.stack(patches, dim=1)  # (batch_size, patch_num, patch_len*features)
        
        # Patch embedding
        embedded = self.patch_embedding(patches)  # (batch_size, patch_num, d_model)
        
        # Add positional encoding
        embedded = embedded + self.pos_encoding.unsqueeze(0)
        
        # Transformer encoding
        encoded = self.transformer(embedded)  # (batch_size, patch_num, d_model)
        
        # Global average pooling
        pooled = encoded.mean(dim=1)  # (batch_size, d_model)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class MultiConditionPredictor:
    """多工况预测系统"""
    
    def __init__(self, model_save_dir: str = './condition_models'):
        self.model_save_dir = model_save_dir
        self.models = {}
        self.condition_classifier = WorkConditionClassifier()
        
        os.makedirs(model_save_dir, exist_ok=True)
        
    def train_condition_models(self, condition_data: Dict[int, List[Tuple[np.ndarray, int]]]):
        """训练各工况专用模型"""
        
        for condition_id, data_list in condition_data.items():
            if len(data_list) < 10:  # 数据量太少，跳过
                print(f"工况 {self.condition_classifier.condition_mapping[condition_id]} 数据量不足，跳过训练")
                continue
                
            print(f"开始训练工况 {self.condition_classifier.condition_mapping[condition_id]} 模型...")
            
            # 创建模型
            model = ConditionSpecificModel(condition_id)
            
            # 这里应该添加完整的训练循环
            # 包括数据预处理、训练循环、验证等
            # 为了简化，这里只是示例框架
            
            # 保存模型
            model_path = os.path.join(self.model_save_dir, f'condition_{condition_id}_model.pth')
            torch.save(model.state_dict(), model_path)
            
            self.models[condition_id] = model
            print(f"工况 {self.condition_classifier.condition_mapping[condition_id]} 模型训练完成")
    
    def predict(self, time_series_data: np.ndarray) -> Dict[str, float]:
        """
        对输入时间序列进行预测
        
        返回: {
            'predicted_condition': 工况名称,
            'condition_probabilities': {工况: 概率},
            'drilling_risk': 卡钻风险概率
        }
        """
        # 首先识别工况
        df = pd.DataFrame(time_series_data, columns=['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 
                                                   'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP'])
        conditions = self.condition_classifier.classify_by_drilling_parameters(df)
        predicted_condition = np.bincount(conditions).argmax()
        
        # 使用对应工况的模型进行预测
        if predicted_condition in self.models:
            model = self.models[predicted_condition]
            model.eval()
            
            # 数据预处理和预测
            with torch.no_grad():
                # 这里需要添加数据预处理逻辑
                # input_tensor = preprocess_data(time_series_data)
                # risk_prob = model(input_tensor)
                risk_prob = 0.5  # 占位符
        else:
            risk_prob = 0.5  # 默认风险概率
        
        return {
            'predicted_condition': self.condition_classifier.condition_mapping[predicted_condition],
            'condition_id': predicted_condition,
            'drilling_risk': risk_prob
        }

# 使用示例
if __name__ == "__main__":
    # 1. 数据加载和分类
    data_loader = MultiConditionDataLoader('./dataset/earlysignaldetection')
    condition_data = data_loader.load_and_classify_data()
    
    # 2. 训练多工况模型
    predictor = MultiConditionPredictor()
    predictor.train_condition_models(condition_data)
    
    # 3. 预测示例
    # sample_data = np.random.randn(152, 10)  # 示例数据
    # result = predictor.predict(sample_data)
    # print(f"预测结果: {result}")

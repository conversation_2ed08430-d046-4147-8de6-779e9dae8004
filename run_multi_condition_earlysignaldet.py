#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多工况感知早期信号检测模型运行脚本
基于现有run_earlysignaldet_simple.py扩展，支持五种钻进工况的专用模型训练

作者: TSlib项目组 + 工况感知扩展
日期: 2024-06-29
"""

import sys
import os
import json
import pickle
from pathlib import Path
from types import SimpleNamespace
import numpy as np
import pandas as pd
from datetime import datetime

# ============================================================================
# 🔧 多工况配置参数
# ============================================================================

# 🎯 工况感知配置
ENABLE_CONDITION_AWARE = True      # 是否启用工况感知功能
CONDITION_IDENTIFICATION_METHOD = 'rule_based'  # 工况识别方法
CONDITION_CONFIDENCE_THRESHOLD = 0.8  # 工况识别置信度阈值

# 📊 各工况专用模型配置
CONDITION_MODEL_CONFIGS = {
    '起钻': {
        'train_epochs': 80,
        'batch_size': 16,
        'learning_rate': 0.001,
        'd_model': 128,
        'e_layers': 3,
        'n_heads': 8,
        'patch_len': 16,
        'stride': 8,
        'min_samples': 500
    },
    '下钻': {
        'train_epochs': 80,
        'batch_size': 16,
        'learning_rate': 0.001,
        'd_model': 128,
        'e_layers': 3,
        'n_heads': 8,
        'patch_len': 16,
        'stride': 8,
        'min_samples': 500
    },
    '正常钻进': {
        'train_epochs': 120,
        'batch_size': 32,
        'learning_rate': 0.0005,
        'd_model': 256,
        'e_layers': 4,
        'n_heads': 16,
        'patch_len': 32,
        'stride': 16,
        'min_samples': 1000
    },
    '正划眼': {
        'train_epochs': 100,
        'batch_size': 16,
        'learning_rate': 0.001,
        'd_model': 128,
        'e_layers': 3,
        'n_heads': 8,
        'patch_len': 16,
        'stride': 8,
        'min_samples': 300
    },
    '倒划眼': {
        'train_epochs': 60,
        'batch_size': 8,
        'learning_rate': 0.002,
        'd_model': 96,
        'e_layers': 2,
        'n_heads': 4,
        'patch_len': 12,
        'stride': 6,
        'min_samples': 200
    }
}

# 🔧 通用配置（继承自原始脚本）
TASK_NAME = 'earlysignaldet'
DATA_NAME = 'Earlysignaldet'
MODEL_TYPE = 'PatchTST'
IS_TRAINING = 1
DO_PREDICT = True

# ============================================================================

class MultiConditionWorkflowManager:
    """多工况训练流程管理器"""
    
    def __init__(self, data_path='./dataset/earlysignaldetection'):
        self.data_path = data_path
        self.condition_models = {}
        self.condition_classifier = None
        self.data_stats = {}
        
        # 创建模型保存目录
        self.model_save_dir = Path('./checkpoints/multi_condition/')
        self.model_save_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志目录
        self.log_dir = Path('./logs/multi_condition/')
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
    def analyze_data_distribution(self):
        """分析数据分布和工况样本数量"""
        print("📊 分析数据分布...")
        
        # 导入工况分类器
        from multi_condition_strategy import MultiConditionDataLoader
        
        # 加载数据并分析
        data_loader = MultiConditionDataLoader(self.data_path)
        condition_data = data_loader.load_and_classify_data()
        
        # 统计各工况样本数量
        for condition_id, data_list in condition_data.items():
            condition_name = data_loader.condition_classifier.condition_mapping[condition_id]
            
            # 统计正负样本
            positive_samples = sum(1 for _, label in data_list if label == 1)
            negative_samples = sum(1 for _, label in data_list if label == 0)
            total_samples = len(data_list)
            
            self.data_stats[condition_name] = {
                'total_samples': total_samples,
                'positive_samples': positive_samples,
                'negative_samples': negative_samples,
                'positive_ratio': positive_samples / total_samples if total_samples > 0 else 0,
                'meets_min_requirement': total_samples >= CONDITION_MODEL_CONFIGS[condition_name]['min_samples']
            }
            
            print(f"  {condition_name}:")
            print(f"    总样本数: {total_samples}")
            print(f"    正样本数: {positive_samples}")
            print(f"    负样本数: {negative_samples}")
            print(f"    正样本比例: {self.data_stats[condition_name]['positive_ratio']:.3f}")
            print(f"    满足最小样本要求: {'✅' if self.data_stats[condition_name]['meets_min_requirement'] else '❌'}")
            print()
        
        return condition_data
    
    def create_condition_specific_args(self, condition_name, base_args):
        """为特定工况创建专用参数配置"""
        condition_config = CONDITION_MODEL_CONFIGS[condition_name]
        
        # 复制基础参数
        condition_args = SimpleNamespace(**vars(base_args))
        
        # 应用工况专用配置
        condition_args.train_epochs = condition_config['train_epochs']
        condition_args.batch_size = condition_config['batch_size']
        condition_args.learning_rate = condition_config['learning_rate']
        condition_args.d_model = condition_config['d_model']
        condition_args.e_layers = condition_config['e_layers']
        condition_args.n_heads = condition_config['n_heads']
        condition_args.patch_len = condition_config['patch_len']
        condition_args.stride = condition_config['stride']
        
        # 更新模型ID以区分不同工况
        condition_args.model_id = f'earlysignaldetection_{condition_name}'
        
        # 更新检查点路径
        condition_args.checkpoints = str(self.model_save_dir / condition_name)
        Path(condition_args.checkpoints).mkdir(parents=True, exist_ok=True)
        
        return condition_args
    
    def train_condition_models(self, base_args, condition_data):
        """训练各工况专用模型"""
        print("🚀 开始训练各工况专用模型...")
        
        # 导入必要模块
        import torch
        from exp.exp_earlysignaldet import Exp_Earlysignaldet
        
        training_results = {}
        
        for condition_name in CONDITION_MODEL_CONFIGS.keys():
            print(f"\n{'='*60}")
            print(f"🎯 训练工况: {condition_name}")
            print(f"{'='*60}")
            
            # 检查样本数量是否满足要求
            if not self.data_stats[condition_name]['meets_min_requirement']:
                print(f"⚠️  警告: {condition_name} 样本数量不足，跳过训练")
                continue
            
            # 创建工况专用参数
            condition_args = self.create_condition_specific_args(condition_name, base_args)
            
            try:
                # 创建实验对象
                exp = Exp_Earlysignaldet(condition_args)
                
                # 生成设置字符串
                setting = self.generate_setting_string(condition_args, condition_name)
                
                print(f"📋 训练设置: {setting}")
                print(f"📊 训练参数:")
                print(f"  - 训练轮数: {condition_args.train_epochs}")
                print(f"  - 批次大小: {condition_args.batch_size}")
                print(f"  - 学习率: {condition_args.learning_rate}")
                print(f"  - 模型维度: {condition_args.d_model}")
                print(f"  - 编码器层数: {condition_args.e_layers}")
                
                # 开始训练
                print(f"\n🚀 开始训练 {condition_name} 模型...")
                train_loss = exp.train(setting)
                
                # 测试模型
                print(f"🧪 测试 {condition_name} 模型...")
                test_results = exp.test(setting)
                
                # 预测（如果需要）
                if condition_args.do_predict:
                    print(f"🔮 {condition_name} 模型预测...")
                    exp.predict(setting, True)
                
                # 保存训练结果
                training_results[condition_name] = {
                    'setting': setting,
                    'train_loss': train_loss,
                    'test_results': test_results,
                    'model_path': condition_args.checkpoints,
                    'training_completed': True
                }
                
                print(f"✅ {condition_name} 模型训练完成")
                
                # 清理GPU内存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"❌ {condition_name} 模型训练失败: {str(e)}")
                training_results[condition_name] = {
                    'training_completed': False,
                    'error': str(e)
                }
                continue
        
        # 保存训练结果摘要
        self.save_training_summary(training_results)
        
        return training_results
    
    def generate_setting_string(self, args, condition_name):
        """生成设置字符串"""
        return f'{args.task_name}_{condition_name}_{args.model}_{args.data}_ft{args.features}_sl{args.seq_len}_ll{args.label_len}_pl{args.pred_len}_dm{args.d_model}_nh{args.n_heads}_el{args.e_layers}_dl{args.d_layers}_df{args.d_ff}_fc{args.factor}_eb{args.embed}_dt{args.distil}_{args.des}_0'
    
    def save_training_summary(self, training_results):
        """保存训练结果摘要"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'data_stats': self.data_stats,
            'training_results': training_results,
            'config': CONDITION_MODEL_CONFIGS
        }
        
        summary_path = self.log_dir / f'training_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 训练摘要已保存: {summary_path}")
    
    def create_unified_predictor(self):
        """创建统一的多工况预测器"""
        print("🔧 创建统一预测器...")

        # 保存预测器配置
        predictor_config = {
            'model_paths': {},
            'condition_classifier_config': {
                'method': CONDITION_IDENTIFICATION_METHOD,
                'confidence_threshold': CONDITION_CONFIDENCE_THRESHOLD
            },
            'condition_configs': CONDITION_MODEL_CONFIGS
        }

        # 收集已训练模型的路径
        for condition_name in CONDITION_MODEL_CONFIGS.keys():
            model_path = self.model_save_dir / condition_name
            if model_path.exists():
                predictor_config['model_paths'][condition_name] = str(model_path)

        # 保存预测器配置
        config_path = self.model_save_dir / 'predictor_config.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(predictor_config, f, indent=2, ensure_ascii=False)

        print(f"📄 预测器配置已保存: {config_path}")

        # 创建预测器使用示例
        example_code = '''
# 使用多工况预测器示例代码:
from multi_condition_predictor import MultiConditionPredictor

# 加载预测器
predictor = MultiConditionPredictor.load_from_config('{}')

# 进行预测
result = predictor.predict(drilling_data)
print(f"预测工况: {{result['condition']}}")
print(f"卡钻风险: {{result['risk_score']:.3f}}")
print(f"预警级别: {{result['alert_level']}}")
        '''.format(config_path)

        example_path = self.model_save_dir / 'usage_example.py'
        with open(example_path, 'w', encoding='utf-8') as f:
            f.write(example_code)

        print(f"📝 使用示例已保存: {example_path}")

def create_base_args():
    """创建基础参数配置（基于原始脚本）"""
    args = SimpleNamespace()
    
    # 基础配置
    args.task_name = TASK_NAME
    args.is_training = IS_TRAINING
    args.model_id = 'earlysignaldetection'
    args.model = MODEL_TYPE
    
    # 数据配置
    args.data = DATA_NAME
    args.root_path = './dataset/earlysignaldetection'
    args.data_path = ''
    args.features = 'M'
    args.target = 'label'
    args.freq = 'h'
    args.checkpoints = './checkpoints/'
    
    # 序列配置
    args.seq_len = 96
    args.label_len = 48
    args.pred_len = 24
    args.seasonal_patterns = 'Monthly'
    args.inverse = False
    
    # 基础模型配置（将被工况专用配置覆盖）
    args.e_layers = 3
    args.d_layers = 1
    args.d_model = 128
    args.d_ff = 256
    args.n_heads = 8
    args.factor = 1
    args.enc_in = 10
    args.dec_in = 10
    args.c_out = 2
    args.num_class = 2
    
    # 基础训练配置（将被工况专用配置覆盖）
    args.batch_size = 16
    args.learning_rate = 0.001
    args.train_epochs = 100
    args.patience = 10
    args.dropout = 0.1
    
    # PatchTST参数（将被工况专用配置覆盖）
    args.patch_len = 16
    args.stride = 8
    args.fc_dropout = 0.1
    args.head_dropout = 0.0
    args.padding_patch = 'end'
    args.revin = 1
    args.affine = 0
    args.subtract_last = 0
    args.decomposition = 0
    args.kernel_size = 25
    args.individual = 0
    
    # 其他配置
    args.embed = 'timeF'
    args.distil = True
    args.mix = True
    args.activation = 'gelu'
    args.output_attention = False
    args.use_amp = False
    args.lradj = 'type1'
    
    # 必需参数
    args.num_kernels = 6
    args.moving_avg = 25
    args.num_workers = 10
    args.loss = 'MSE'
    args.anomaly_ratio = 0.25
    args.mask_rate = 0.25
    
    # 设备配置
    args.use_gpu = True
    args.gpu = 0
    args.use_multi_gpu = False
    args.devices = '0,1,2,3'
    
    # 实验配置
    args.des = 'Exp'
    args.itr = 1
    args.top_k = 3
    args.p_hidden_dims = [128, 128]
    args.p_hidden_layers = 2
    args.do_predict = DO_PREDICT
    
    return args

def main():
    """主函数"""
    print("🚀 多工况感知早期信号检测模型启动")
    print("="*80)
    
    # 检查数据路径
    data_path = Path('./dataset/earlysignaldetection')
    if not data_path.exists():
        print(f"❌ 错误: 数据路径不存在 {data_path.absolute()}")
        return False
    
    try:
        # 导入必要模块
        import torch
        import random
        import numpy as np
        
        # 设置随机种子
        fix_seed = 2021
        random.seed(fix_seed)
        torch.manual_seed(fix_seed)
        np.random.seed(fix_seed)
        
        # 创建工作流管理器
        workflow_manager = MultiConditionWorkflowManager(str(data_path))
        
        # 分析数据分布
        condition_data = workflow_manager.analyze_data_distribution()
        
        # 创建基础参数
        base_args = create_base_args()
        
        # 设置GPU
        base_args.use_gpu = True if torch.cuda.is_available() and base_args.use_gpu else False
        print(f"🖥️  使用设备: {'GPU' if base_args.use_gpu else 'CPU'}")
        
        if base_args.use_gpu and base_args.use_multi_gpu:
            base_args.devices = base_args.devices.replace(' ', '')
            device_ids = base_args.devices.split(',')
            base_args.device_ids = [int(id_) for id_ in device_ids]
            base_args.gpu = base_args.device_ids[0]
        
        # 训练各工况模型
        if base_args.is_training:
            training_results = workflow_manager.train_condition_models(base_args, condition_data)
            
            # 显示训练结果摘要
            print("\n" + "="*80)
            print("📊 训练结果摘要")
            print("="*80)
            
            for condition_name, result in training_results.items():
                if result.get('training_completed', False):
                    print(f"✅ {condition_name}: 训练成功")
                else:
                    print(f"❌ {condition_name}: 训练失败 - {result.get('error', '未知错误')}")
        
        # 创建统一预测器
        workflow_manager.create_unified_predictor()
        
        print("\n" + "="*80)
        print("✅ 多工况感知模型训练流程完成!")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

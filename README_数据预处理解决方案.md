# 钻进数据预处理解决方案

## 🎯 解决方案概述

本解决方案专门针对**工况标签频繁切换的钻进数据**进行预处理，解决用户提出的四个核心挑战：

1. **工况序列分割策略** - 如何合理分割频繁切换的长序列
2. **工况标签清洗标准化** - 处理空值、乱码、不一致标签
3. **训练样本构建逻辑** - 从混合工况序列中提取纯净训练样本
4. **数据质量控制** - 评估和过滤低质量数据段

## 📁 文件结构

```
钻进数据预处理解决方案/
├── advanced_data_preprocessor.py      # 核心预处理器
├── process_real_drilling_data.py      # 实际数据处理脚本
├── test_data_preprocessing.py         # 测试脚本
├── condition_mapping_config.json      # 工况映射配置
├── run_earlysignaldet_simple.py      # 简化版训练脚本
└── README_数据预处理解决方案.md       # 本文件
```

## 🚀 快速开始

### 1. 准备数据
将您的钻进数据CSV文件放在以下目录：
```
./dataset/earlysignaldetection/
```

### 2. 运行预处理
```python
python process_real_drilling_data.py
```

### 3. 查看结果
处理结果将保存在：
```
./processed_data/
├── segments/           # 按工况分组的段数据
├── training_samples/   # 训练样本
└── reports/           # 处理报告
```

## 🔧 核心功能

### 1. 工况标签标准化
- **自动映射**: 将各种变体标签统一为5种标准工况
- **无效标签处理**: 识别并处理空值、乱码、异常标签
- **插值修复**: 对短的无效标签段进行智能插值

```python
# 支持的标签映射示例
'坐卡' → '起钻'
'起钻具' → '起钻'  
'钻进' → '正常钻进'
'划眼' → '正划眼'
```

### 2. 智能序列分割
- **连续性分割**: 按工况连续性进行初步分割
- **长段分割**: 将过长段分割为合适长度，支持重叠
- **转换区域移除**: 移除工况切换时的过渡数据点
- **质量过滤**: 基于多维度质量评估过滤低质量段

### 3. 训练样本构建
- **工况特定**: 为每种工况单独构建训练样本
- **样本平衡**: 智能平衡正负样本比例
- **质量优先**: 优先选择高质量段作为训练样本
- **条件分布**: 确保负样本涵盖其他工况类型

### 4. 数据质量控制
- **参数一致性**: 验证钻进参数与工况标签的一致性
- **时间连续性**: 检查时间序列的连续性
- **完整性检查**: 评估数据缺失情况
- **异常检测**: 识别参数异常和标签错误

## ⚙️ 配置参数

### 基础配置
```python
PROCESSING_CONFIG = {
    'min_segment_length': 30,          # 最小段长度
    'max_segment_length': 300,         # 最大段长度
    'transition_buffer': 8,            # 转换缓冲区
    'min_quality_score': 0.65,         # 最小质量分数
    'min_condition_duration_seconds': 120,  # 最小工况持续时间
}
```

### 训练样本配置
```python
TRAINING_CONFIG = {
    '起钻': {'target_samples': 500, 'positive_ratio': 0.25},
    '下钻': {'target_samples': 500, 'positive_ratio': 0.25},
    '正常钻进': {'target_samples': 800, 'positive_ratio': 0.3},
    # ...
}
```

## 📊 处理流程

```mermaid
graph TD
    A[原始CSV数据] --> B[数据分析]
    B --> C[工况标签清洗]
    C --> D[序列分割]
    D --> E[质量评估]
    E --> F[训练样本构建]
    F --> G[结果输出]
    
    C --> C1[标签映射]
    C --> C2[无效值处理]
    C --> C3[插值修复]
    
    D --> D1[连续性分割]
    D --> D2[长段分割]
    D --> D3[转换区域移除]
    
    E --> E1[参数一致性]
    E --> E2[时间连续性]
    E --> E3[数据完整性]
```

## 🎯 针对用户问题的解决方案

### 问题1: 工况序列分割策略
**解决方案**: 
- 采用**连续性分割 + 长段分割**的混合策略
- 对于频繁切换的数据，先按连续性分割，再将过长段分割为合适长度
- 支持重叠分割，确保不丢失边界信息

### 问题2: 工况标签清洗标准化
**解决方案**:
- 建立完整的**标签映射表**，支持各种变体和同义词
- 使用**正则表达式**识别无效标签模式
- 实现**智能插值**，修复短的无效标签段

### 问题3: 训练样本构建逻辑
**解决方案**:
- **工况特定提取**: 为每种目标工况单独提取训练样本
- **质量优先选择**: 优先选择高质量分数的段
- **样本平衡策略**: 智能平衡不同工况的负样本分布

### 问题4: 数据质量控制
**解决方案**:
- **多维度质量评估**: 工况一致性、参数合理性、时间连续性
- **参数验证规则**: 基于钻进工艺的参数合理性检查
- **质量分数计算**: 综合评估得出0-1的质量分数

## 📈 预期效果

### 数据质量提升
- 工况标签一致性 > 90%
- 数据完整性 > 90%
- 段质量分数 > 0.65

### 训练样本质量
- 每种工况200-800个高质量样本
- 正负样本比例合理(15%-30%)
- 样本纯度 > 85%

### 处理效率
- 自动化处理，无需人工干预
- 批量处理多个文件
- 生成详细的处理报告

## 🔍 使用示例

### 基础使用
```python
from advanced_data_preprocessor import AdvancedDataPreprocessor

# 初始化预处理器
preprocessor = AdvancedDataPreprocessor()

# 处理单个文件
result = preprocessor.process_single_file('data.csv')

# 批量处理
batch_result = preprocessor.process_directory('./data/', './output/')
```

### 创建训练样本
```python
# 为"起钻"工况创建训练样本
training_samples = preprocessor.create_training_samples(
    segments, 
    target_condition='起钻',
    positive_ratio=0.25
)
```

### 质量验证
```python
# 验证段的一致性
validation_result = preprocessor.validate_segment_consistency(segment)
print(f"质量分数: {validation_result['consistency_score']}")
```

## 🛠️ 高级功能

### 1. 自定义配置
- 支持自定义分割参数
- 可调整质量评估标准
- 灵活的训练样本配置

### 2. 可视化报告
- 工况分布图表
- 质量分数分布
- 段长度统计
- 训练样本统计

### 3. 详细日志
- 处理过程记录
- 错误信息追踪
- 性能统计信息

## 📋 注意事项

1. **数据格式**: 确保CSV文件包含必要的钻进参数列
2. **工况标签**: 工况标签列应包含中文或英文的工况描述
3. **时间戳**: 建议包含时间戳列以提高处理精度
4. **内存使用**: 大文件处理时注意内存使用情况
5. **参数调整**: 根据实际数据特点调整配置参数

## 🚀 下一步

1. **运行测试**: 使用 `test_data_preprocessing.py` 进行功能测试
2. **处理实际数据**: 使用 `process_real_drilling_data.py` 处理您的数据
3. **模型训练**: 使用处理后的数据进行PatchTST模型训练
4. **性能评估**: 评估预处理对模型性能的改善效果

## 📞 技术支持

如有问题或需要定制化功能，请参考：
- 详细的代码注释和文档字符串
- `test_data_preprocessing.py` 中的使用示例
- 生成的处理报告和使用指南

---

**本解决方案专门针对您提供的实际钻进数据特点设计，解决工况标签频繁切换的核心问题，为后续的早期信号检测模型训练提供高质量的数据基础。**

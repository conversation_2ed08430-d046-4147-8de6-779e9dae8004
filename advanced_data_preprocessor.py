#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级数据预处理器 - 专门处理工况标签频繁切换的钻进数据

针对实际钻进数据的特点：
1. 工况标签在短时间内频繁切换
2. 单个CSV文件包含多种工况的混合序列
3. 工况标签质量不一致（空值、乱码、不准确）

作者: TSlib项目组
日期: 2024-06-29
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import warnings
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import re
from collections import Counter, defaultdict
# import matplotlib.pyplot as plt
# import seaborn as sns

warnings.filterwarnings('ignore')

class WorkConditionLabelProcessor:
    """工况标签处理器"""
    
    def __init__(self):
        # 工况标签标准化映射表
        self.condition_mapping = {
            # 标准工况
            '起钻': '起钻',
            '下钻': '下钻', 
            '正常钻进': '正常钻进',
            '正划眼': '正划眼',
            '倒划眼': '倒划眼',
            
            # 常见变体和同义词
            '坐卡': '起钻',  # 坐卡通常是起钻过程中的状态
            '起下钻': '起钻',
            '起钻具': '起钻',
            '下钻具': '下钻',
            '钻进': '正常钻进',
            '正常': '正常钻进',
            '划眼': '正划眼',
            '扩眼': '正划眼',
            '倒划': '倒划眼',
            '反划': '倒划眼',
            
            # 数值编码
            '0': '正常钻进',
            '1': '起钻',
            '2': '下钻',
            '3': '正划眼',
            '4': '倒划眼',
            
            # 其他可能的标签
            '停钻': '正常钻进',  # 临时停止，归类为正常钻进
            '接单根': '下钻',    # 接单根通常在下钻过程中
            '循环': '正常钻进',   # 循环清洁井眼
        }
        
        # 无效标签模式
        self.invalid_patterns = [
            r'^$',           # 空字符串
            r'^\s*$',        # 只有空白字符
            r'^nan$',        # NaN字符串
            r'^null$',       # null字符串
            r'^none$',       # none字符串
            r'^\d{4}',       # 以4位数字开头（可能是时间戳）
            r'^[^\u4e00-\u9fa5a-zA-Z]',  # 不以中文或英文开头
        ]
        
        # 工况持续时间统计
        self.condition_durations = defaultdict(list)
    
    def clean_and_standardize_labels(self, labels: pd.Series) -> pd.Series:
        """
        清洗和标准化工况标签
        
        Args:
            labels: 原始工况标签序列
            
        Returns:
            清洗后的标准化标签序列
        """
        cleaned_labels = labels.copy()
        
        # 1. 处理缺失值和无效值
        cleaned_labels = cleaned_labels.fillna('UNKNOWN')
        cleaned_labels = cleaned_labels.astype(str).str.strip()
        
        # 2. 识别和标记无效标签
        for pattern in self.invalid_patterns:
            mask = cleaned_labels.str.match(pattern, case=False, na=False)
            cleaned_labels.loc[mask] = 'UNKNOWN'
        
        # 3. 标准化映射
        cleaned_labels = cleaned_labels.map(self.condition_mapping).fillna('UNKNOWN')
        
        # 4. 处理连续的UNKNOWN标签
        cleaned_labels = self._interpolate_unknown_labels(cleaned_labels)
        
        return cleaned_labels
    
    def _interpolate_unknown_labels(self, labels: pd.Series) -> pd.Series:
        """插值处理未知标签"""
        result = labels.copy()
        
        # 前向填充短的UNKNOWN段
        unknown_mask = (result == 'UNKNOWN')
        
        # 找到UNKNOWN段的起始和结束位置
        unknown_groups = []
        start_idx = None
        
        for i, is_unknown in enumerate(unknown_mask):
            if is_unknown and start_idx is None:
                start_idx = i
            elif not is_unknown and start_idx is not None:
                unknown_groups.append((start_idx, i-1))
                start_idx = None
        
        # 处理最后一个UNKNOWN段
        if start_idx is not None:
            unknown_groups.append((start_idx, len(result)-1))
        
        # 对短的UNKNOWN段进行插值
        for start, end in unknown_groups:
            segment_length = end - start + 1
            
            # 如果UNKNOWN段很短（<10个点），尝试插值
            if segment_length < 10:
                # 获取前后的有效标签
                prev_label = result.iloc[start-1] if start > 0 else None
                next_label = result.iloc[end+1] if end < len(result)-1 else None
                
                # 如果前后标签相同，用该标签填充
                if prev_label and next_label and prev_label == next_label:
                    result.iloc[start:end+1] = prev_label
                # 如果只有前标签，用前标签填充
                elif prev_label and prev_label != 'UNKNOWN':
                    result.iloc[start:end+1] = prev_label
                # 如果只有后标签，用后标签填充
                elif next_label and next_label != 'UNKNOWN':
                    result.iloc[start:end+1] = next_label
        
        return result
    
    def detect_condition_transitions(self, labels: pd.Series, timestamps: pd.Series = None) -> List[Dict]:
        """
        检测工况转换点
        
        Args:
            labels: 工况标签序列
            timestamps: 时间戳序列（可选）
            
        Returns:
            转换点信息列表
        """
        transitions = []
        
        if len(labels) == 0:
            return transitions
        
        prev_condition = labels.iloc[0]
        transition_start = 0
        
        for i in range(1, len(labels)):
            current_condition = labels.iloc[i]
            
            if current_condition != prev_condition:
                # 记录转换信息
                transition_info = {
                    'from_condition': prev_condition,
                    'to_condition': current_condition,
                    'start_index': transition_start,
                    'end_index': i-1,
                    'duration_points': i - transition_start,
                    'transition_index': i
                }
                
                if timestamps is not None:
                    transition_info.update({
                        'start_time': timestamps.iloc[transition_start],
                        'end_time': timestamps.iloc[i-1],
                        'transition_time': timestamps.iloc[i],
                        'duration_seconds': (timestamps.iloc[i-1] - timestamps.iloc[transition_start]).total_seconds()
                    })
                
                transitions.append(transition_info)
                
                # 更新状态
                prev_condition = current_condition
                transition_start = i
        
        # 添加最后一个段
        if transition_start < len(labels):
            final_info = {
                'from_condition': prev_condition,
                'to_condition': prev_condition,  # 最后一段
                'start_index': transition_start,
                'end_index': len(labels)-1,
                'duration_points': len(labels) - transition_start,
                'transition_index': len(labels)
            }
            
            if timestamps is not None:
                final_info.update({
                    'start_time': timestamps.iloc[transition_start],
                    'end_time': timestamps.iloc[-1],
                    'duration_seconds': (timestamps.iloc[-1] - timestamps.iloc[transition_start]).total_seconds()
                })
            
            transitions.append(final_info)
        
        return transitions
    
    def filter_short_segments(self, labels: pd.Series, min_duration_points: int = 30, 
                            timestamps: pd.Series = None, min_duration_seconds: float = 150) -> pd.Series:
        """
        过滤持续时间过短的工况段
        
        Args:
            labels: 工况标签序列
            min_duration_points: 最小持续点数
            timestamps: 时间戳序列
            min_duration_seconds: 最小持续秒数
            
        Returns:
            过滤后的标签序列
        """
        if len(labels) == 0:
            return labels
        
        transitions = self.detect_condition_transitions(labels, timestamps)
        filtered_labels = labels.copy()
        
        for trans in transitions:
            duration_ok = trans['duration_points'] >= min_duration_points
            
            if timestamps is not None and 'duration_seconds' in trans:
                duration_ok = duration_ok and trans['duration_seconds'] >= min_duration_seconds
            
            # 如果段太短，将其合并到相邻的较长段中
            if not duration_ok:
                start_idx = trans['start_index']
                end_idx = trans['end_index']
                
                # 寻找前后的有效段
                prev_condition = None
                next_condition = None
                
                if start_idx > 0:
                    prev_condition = filtered_labels.iloc[start_idx - 1]
                
                if end_idx < len(filtered_labels) - 1:
                    next_condition = filtered_labels.iloc[end_idx + 1]
                
                # 选择合并目标
                if prev_condition and next_condition:
                    # 如果前后工况相同，合并到该工况
                    if prev_condition == next_condition:
                        filtered_labels.iloc[start_idx:end_idx+1] = prev_condition
                    else:
                        # 否则合并到前一个工况
                        filtered_labels.iloc[start_idx:end_idx+1] = prev_condition
                elif prev_condition:
                    filtered_labels.iloc[start_idx:end_idx+1] = prev_condition
                elif next_condition:
                    filtered_labels.iloc[start_idx:end_idx+1] = next_condition
        
        return filtered_labels


class SequenceSegmentationProcessor:
    """序列分割处理器"""
    
    def __init__(self, min_segment_length: int = 50, max_segment_length: int = 500,
                 overlap_ratio: float = 0.1, transition_buffer: int = 10):
        """
        初始化序列分割处理器
        
        Args:
            min_segment_length: 最小段长度
            max_segment_length: 最大段长度
            overlap_ratio: 重叠比例
            transition_buffer: 转换缓冲区大小
        """
        self.min_segment_length = min_segment_length
        self.max_segment_length = max_segment_length
        self.overlap_ratio = overlap_ratio
        self.transition_buffer = transition_buffer
        
        # 不同工况的推荐段长度
        self.condition_segment_lengths = {
            '起钻': {'min': 60, 'max': 400, 'preferred': 200},
            '下钻': {'min': 60, 'max': 400, 'preferred': 200},
            '正常钻进': {'min': 100, 'max': 600, 'preferred': 300},
            '正划眼': {'min': 80, 'max': 500, 'preferred': 250},
            '倒划眼': {'min': 50, 'max': 300, 'preferred': 150}
        }
    
    def segment_by_condition_continuity(self, data: pd.DataFrame, condition_col: str = 'condition') -> List[Dict]:
        """
        按工况连续性分割序列
        
        Args:
            data: 包含工况标签的数据
            condition_col: 工况标签列名
            
        Returns:
            分割后的段信息列表
        """
        if len(data) == 0:
            return []
        
        segments = []
        current_condition = data[condition_col].iloc[0]
        segment_start = 0
        
        for i in range(1, len(data)):
            if data[condition_col].iloc[i] != current_condition:
                # 当前段结束
                segment_end = i - 1
                segment_length = segment_end - segment_start + 1
                
                # 检查段长度是否满足要求
                if segment_length >= self.min_segment_length:
                    segment_info = {
                        'condition': current_condition,
                        'start_index': segment_start,
                        'end_index': segment_end,
                        'length': segment_length,
                        'data': data.iloc[segment_start:segment_end+1].copy(),
                        'quality_score': self._calculate_segment_quality(
                            data.iloc[segment_start:segment_end+1], current_condition
                        )
                    }
                    segments.append(segment_info)
                
                # 开始新段
                current_condition = data[condition_col].iloc[i]
                segment_start = i
        
        # 处理最后一段
        if segment_start < len(data):
            segment_end = len(data) - 1
            segment_length = segment_end - segment_start + 1
            
            if segment_length >= self.min_segment_length:
                segment_info = {
                    'condition': current_condition,
                    'start_index': segment_start,
                    'end_index': segment_end,
                    'length': segment_length,
                    'data': data.iloc[segment_start:segment_end+1].copy(),
                    'quality_score': self._calculate_segment_quality(
                        data.iloc[segment_start:segment_end+1], current_condition
                    )
                }
                segments.append(segment_info)
        
        return segments
    
    def split_long_segments(self, segments: List[Dict]) -> List[Dict]:
        """
        分割过长的段
        
        Args:
            segments: 原始段列表
            
        Returns:
            分割后的段列表
        """
        split_segments = []
        
        for segment in segments:
            condition = segment['condition']
            length = segment['length']
            data = segment['data']
            
            # 获取该工况的推荐长度
            condition_config = self.condition_segment_lengths.get(
                condition, {'min': self.min_segment_length, 'max': self.max_segment_length}
            )
            max_length = condition_config['max']
            
            if length <= max_length:
                # 段长度合适，直接添加
                split_segments.append(segment)
            else:
                # 段太长，需要分割
                preferred_length = condition_config.get('preferred', max_length)
                overlap_size = int(preferred_length * self.overlap_ratio)
                
                start_idx = 0
                while start_idx < length:
                    end_idx = min(start_idx + preferred_length, length)
                    
                    if end_idx - start_idx >= self.min_segment_length:
                        sub_segment = {
                            'condition': condition,
                            'start_index': segment['start_index'] + start_idx,
                            'end_index': segment['start_index'] + end_idx - 1,
                            'length': end_idx - start_idx,
                            'data': data.iloc[start_idx:end_idx].copy(),
                            'quality_score': self._calculate_segment_quality(
                                data.iloc[start_idx:end_idx], condition
                            ),
                            'parent_segment': segment['start_index']
                        }
                        split_segments.append(sub_segment)
                    
                    # 移动到下一个位置（考虑重叠）
                    start_idx += preferred_length - overlap_size
                    
                    # 避免无限循环
                    if start_idx >= length - self.min_segment_length:
                        break
        
        return split_segments
    
    def remove_transition_zones(self, segments: List[Dict]) -> List[Dict]:
        """
        移除工况转换区域的数据点
        
        Args:
            segments: 段列表
            
        Returns:
            移除转换区域后的段列表
        """
        cleaned_segments = []
        
        for segment in segments:
            data = segment['data'].copy()
            length = len(data)
            
            # 如果段太短，无法移除转换区域
            if length <= 2 * self.transition_buffer:
                continue
            
            # 移除开头和结尾的转换区域
            start_trim = self.transition_buffer
            end_trim = length - self.transition_buffer
            
            trimmed_data = data.iloc[start_trim:end_trim].copy()
            
            if len(trimmed_data) >= self.min_segment_length:
                cleaned_segment = {
                    'condition': segment['condition'],
                    'start_index': segment['start_index'] + start_trim,
                    'end_index': segment['start_index'] + end_trim - 1,
                    'length': len(trimmed_data),
                    'data': trimmed_data,
                    'quality_score': self._calculate_segment_quality(
                        trimmed_data, segment['condition']
                    ),
                    'transition_removed': True
                }
                cleaned_segments.append(cleaned_segment)
        
        return cleaned_segments
    
    def _calculate_segment_quality(self, data: pd.DataFrame, expected_condition: str) -> float:
        """
        计算段的质量分数
        
        Args:
            data: 段数据
            expected_condition: 期望的工况
            
        Returns:
            质量分数 (0-1)
        """
        if len(data) == 0:
            return 0.0
        
        quality_score = 1.0
        
        # 1. 工况一致性检查
        if 'condition' in data.columns:
            condition_consistency = (data['condition'] == expected_condition).mean()
            quality_score *= condition_consistency
        
        # 2. 数据完整性检查
        required_cols = ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP']
        available_cols = [col for col in required_cols if col in data.columns]
        
        if available_cols:
            completeness_scores = []
            for col in available_cols:
                completeness = 1 - data[col].isna().mean()
                completeness_scores.append(completeness)
            
            avg_completeness = np.mean(completeness_scores)
            quality_score *= avg_completeness
        
        # 3. 数据变异性检查（避免常数序列）
        if available_cols:
            variability_scores = []
            for col in available_cols:
                if data[col].std() > 0:
                    variability_scores.append(1.0)
                else:
                    variability_scores.append(0.5)  # 常数序列降低质量
            
            avg_variability = np.mean(variability_scores)
            quality_score *= avg_variability
        
        # 4. 长度适宜性检查
        length = len(data)
        if expected_condition in self.condition_segment_lengths:
            config = self.condition_segment_lengths[expected_condition]
            preferred_length = config['preferred']
            
            # 长度越接近推荐长度，质量越高
            length_ratio = min(length, preferred_length) / max(length, preferred_length)
            quality_score *= (0.7 + 0.3 * length_ratio)  # 基础分0.7，长度适宜性占0.3
        
        return max(0.0, min(1.0, quality_score))


class AdvancedDataPreprocessor:
    """高级数据预处理器主类"""
    
    def __init__(self, config: Dict = None):
        """
        初始化高级数据预处理器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or self._get_default_config()
        
        # 初始化子处理器
        self.label_processor = WorkConditionLabelProcessor()
        self.segmentation_processor = SequenceSegmentationProcessor(
            min_segment_length=self.config['min_segment_length'],
            max_segment_length=self.config['max_segment_length'],
            overlap_ratio=self.config['overlap_ratio'],
            transition_buffer=self.config['transition_buffer']
        )
        
        # 统计信息
        self.processing_stats = {
            'total_files': 0,
            'processed_files': 0,
            'total_segments': 0,
            'condition_distribution': defaultdict(int),
            'quality_distribution': defaultdict(int),
            'processing_errors': []
        }
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'min_segment_length': 50,
            'max_segment_length': 500,
            'overlap_ratio': 0.1,
            'transition_buffer': 10,
            'min_quality_score': 0.6,
            'enable_transition_removal': True,
            'enable_short_segment_filtering': True,
            'min_condition_duration_seconds': 150,
            'required_columns': ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP'],
            'condition_column': 'condition',
            'timestamp_column': 'timestamp'
        }
    
    def process_single_file(self, file_path: str, condition_column: str = None) -> Dict:
        """
        处理单个CSV文件
        
        Args:
            file_path: CSV文件路径
            condition_column: 工况标签列名
            
        Returns:
            处理结果字典
        """
        try:
            # 读取数据，尝试多种编码方式
            data = None
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']

            for encoding in encodings:
                try:
                    data = pd.read_csv(file_path, encoding=encoding)
                    print(f"✅ 成功使用 {encoding} 编码读取文件: {file_path}")
                    break
                except UnicodeDecodeError:
                    continue

            if data is None:
                raise ValueError(f"❌ 无法使用任何编码方式读取文件: {file_path}")
            
            if len(data) == 0:
                return {'status': 'empty', 'segments': []}
            
            # 确定工况列名
            condition_col = condition_column or self._detect_condition_column(data)
            if not condition_col:
                return {'status': 'no_condition_column', 'segments': []}
            
            # 处理时间戳
            timestamp_col = self._detect_timestamp_column(data)
            timestamps = None
            if timestamp_col:
                try:
                    timestamps = pd.to_datetime(data[timestamp_col])
                except:
                    timestamps = None
            
            # 1. 清洗和标准化工况标签
            original_labels = data[condition_col].copy()
            cleaned_labels = self.label_processor.clean_and_standardize_labels(original_labels)
            data[condition_col] = cleaned_labels
            
            # 2. 过滤短工况段
            if self.config['enable_short_segment_filtering']:
                filtered_labels = self.label_processor.filter_short_segments(
                    cleaned_labels, 
                    min_duration_points=30,
                    timestamps=timestamps,
                    min_duration_seconds=self.config['min_condition_duration_seconds']
                )
                data[condition_col] = filtered_labels
            
            # 3. 按工况连续性分割
            segments = self.segmentation_processor.segment_by_condition_continuity(data, condition_col)
            
            # 4. 分割过长的段
            segments = self.segmentation_processor.split_long_segments(segments)
            
            # 5. 移除转换区域
            if self.config['enable_transition_removal']:
                segments = self.segmentation_processor.remove_transition_zones(segments)
            
            # 6. 质量过滤
            high_quality_segments = [
                seg for seg in segments 
                if seg['quality_score'] >= self.config['min_quality_score']
            ]
            
            # 更新统计信息
            self.processing_stats['processed_files'] += 1
            self.processing_stats['total_segments'] += len(high_quality_segments)
            
            for segment in high_quality_segments:
                self.processing_stats['condition_distribution'][segment['condition']] += 1
                quality_bin = f"{int(segment['quality_score'] * 10) * 10}%-{int(segment['quality_score'] * 10) * 10 + 10}%"
                self.processing_stats['quality_distribution'][quality_bin] += 1
            
            return {
                'status': 'success',
                'file_path': file_path,
                'original_length': len(data),
                'segments': high_quality_segments,
                'condition_transitions': self.label_processor.detect_condition_transitions(
                    data[condition_col], timestamps
                ),
                'label_cleaning_stats': {
                    'original_unique_labels': len(original_labels.unique()),
                    'cleaned_unique_labels': len(cleaned_labels.unique()),
                    'unknown_ratio': (cleaned_labels == 'UNKNOWN').mean()
                }
            }
            
        except Exception as e:
            error_info = {
                'file_path': file_path,
                'error': str(e),
                'error_type': type(e).__name__
            }
            self.processing_stats['processing_errors'].append(error_info)
            
            return {
                'status': 'error',
                'file_path': file_path,
                'error': str(e)
            }
    
    def _detect_condition_column(self, data: pd.DataFrame) -> Optional[str]:
        """自动检测工况标签列"""
        possible_names = ['工况', 'condition', '状态', 'status', '操作', 'operation']
        
        for col in data.columns:
            col_lower = str(col).lower()
            if any(name in col_lower for name in possible_names):
                return col
        
        # 检查最后一列是否包含文本标签
        last_col = data.columns[-1]
        if data[last_col].dtype == 'object':
            unique_values = data[last_col].dropna().unique()
            if len(unique_values) < 20:  # 假设工况类别不会太多
                return last_col
        
        return None
    
    def _detect_timestamp_column(self, data: pd.DataFrame) -> Optional[str]:
        """自动检测时间戳列"""
        possible_names = ['时间', 'time', 'timestamp', '日期', 'date', 'datetime']
        
        for col in data.columns:
            col_lower = str(col).lower()
            if any(name in col_lower for name in possible_names):
                return col
        
        # 检查第一列是否是时间格式
        first_col = data.columns[0]
        try:
            pd.to_datetime(data[first_col].iloc[:10])
            return first_col
        except:
            pass
        
        return None

    def process_directory(self, data_dir: str, output_dir: str = None,
                         condition_column: str = None) -> Dict:
        """
        批量处理目录中的所有CSV文件

        Args:
            data_dir: 数据目录路径
            output_dir: 输出目录路径
            condition_column: 工况标签列名

        Returns:
            批量处理结果
        """
        data_path = Path(data_dir)
        if not data_path.exists():
            raise FileNotFoundError(f"数据目录不存在: {data_dir}")

        # 查找所有CSV文件
        csv_files = list(data_path.glob("**/*.csv"))
        self.processing_stats['total_files'] = len(csv_files)

        print(f"🔍 发现 {len(csv_files)} 个CSV文件")

        all_segments = []
        processing_results = []

        for i, csv_file in enumerate(csv_files):
            print(f"📄 处理文件 {i+1}/{len(csv_files)}: {csv_file.name}")

            result = self.process_single_file(str(csv_file), condition_column)
            processing_results.append(result)

            if result['status'] == 'success':
                all_segments.extend(result['segments'])

        # 保存处理结果
        if output_dir:
            self._save_processing_results(all_segments, output_dir)

        # 生成统计报告
        stats_report = self._generate_statistics_report(all_segments)

        return {
            'total_files': len(csv_files),
            'processed_files': self.processing_stats['processed_files'],
            'total_segments': len(all_segments),
            'segments_by_condition': self._group_segments_by_condition(all_segments),
            'statistics_report': stats_report,
            'processing_results': processing_results
        }

    def _save_processing_results(self, segments: List[Dict], output_dir: str):
        """保存处理结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 按工况分组保存
        segments_by_condition = self._group_segments_by_condition(segments)

        for condition, condition_segments in segments_by_condition.items():
            condition_dir = output_path / condition
            condition_dir.mkdir(exist_ok=True)

            for i, segment in enumerate(condition_segments):
                # 保存段数据
                segment_file = condition_dir / f"segment_{i:04d}_quality_{segment['quality_score']:.3f}.csv"
                segment['data'].to_csv(segment_file, index=False, encoding='utf-8-sig')

                # 保存段元信息
                meta_file = condition_dir / f"segment_{i:04d}_meta.json"
                meta_info = {
                    'condition': segment['condition'],
                    'length': segment['length'],
                    'quality_score': segment['quality_score'],
                    'start_index': segment['start_index'],
                    'end_index': segment['end_index']
                }

                with open(meta_file, 'w', encoding='utf-8') as f:
                    json.dump(meta_info, f, ensure_ascii=False, indent=2)

        # 保存总体统计
        stats_file = output_path / "processing_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.processing_stats, f, ensure_ascii=False, indent=2, default=str)

        print(f"✅ 处理结果已保存到: {output_dir}")

    def _group_segments_by_condition(self, segments: List[Dict]) -> Dict[str, List[Dict]]:
        """按工况分组段"""
        grouped = defaultdict(list)
        for segment in segments:
            grouped[segment['condition']].append(segment)
        return dict(grouped)

    def _generate_statistics_report(self, segments: List[Dict]) -> Dict:
        """生成统计报告"""
        if not segments:
            return {}

        # 基础统计
        total_segments = len(segments)
        conditions = [seg['condition'] for seg in segments]
        condition_counts = Counter(conditions)

        # 质量统计
        quality_scores = [seg['quality_score'] for seg in segments]

        # 长度统计
        lengths = [seg['length'] for seg in segments]

        report = {
            'total_segments': total_segments,
            'condition_distribution': dict(condition_counts),
            'condition_percentages': {
                cond: count/total_segments*100
                for cond, count in condition_counts.items()
            },
            'quality_statistics': {
                'mean': np.mean(quality_scores),
                'std': np.std(quality_scores),
                'min': np.min(quality_scores),
                'max': np.max(quality_scores),
                'median': np.median(quality_scores)
            },
            'length_statistics': {
                'mean': np.mean(lengths),
                'std': np.std(lengths),
                'min': np.min(lengths),
                'max': np.max(lengths),
                'median': np.median(lengths)
            }
        }

        # 按工况的详细统计
        condition_details = {}
        for condition in condition_counts.keys():
            condition_segments = [seg for seg in segments if seg['condition'] == condition]
            condition_qualities = [seg['quality_score'] for seg in condition_segments]
            condition_lengths = [seg['length'] for seg in condition_segments]

            condition_details[condition] = {
                'count': len(condition_segments),
                'avg_quality': np.mean(condition_qualities),
                'avg_length': np.mean(condition_lengths),
                'quality_range': [np.min(condition_qualities), np.max(condition_qualities)],
                'length_range': [np.min(condition_lengths), np.max(condition_lengths)]
            }

        report['condition_details'] = condition_details

        return report

    def generate_preprocessing_report(self, output_file: str) -> str:
        """生成预处理报告"""
        from datetime import datetime

        report_content = f"""# 钻进数据预处理报告

## 处理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 配置参数
- 最小段长度: {self.config['min_segment_length']}
- 最大段长度: {self.config['max_segment_length']}
- 最小质量分数: {self.config['min_quality_score']}
- 转换缓冲区: {self.config['transition_buffer']}

## 处理结果
预处理完成，详细结果请查看输出目录。

## 建议
1. 检查质量分数较低的段
2. 根据需要调整配置参数
3. 验证训练样本的质量
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        return report_content

    def create_training_samples(self, segments: List[Dict], target_condition: str,
                              positive_ratio: float = 0.25) -> Dict:
        """创建训练样本"""
        # 分离正负样本
        positive_segments = [seg for seg in segments if seg['condition'] == target_condition]
        negative_segments = [seg for seg in segments if seg['condition'] != target_condition]

        # 计算样本数量
        n_positive = len(positive_segments)
        n_negative = int(n_positive / positive_ratio) - n_positive if positive_ratio > 0 else len(negative_segments)
        n_negative = min(n_negative, len(negative_segments))

        # 选择负样本（按质量分数排序）
        negative_segments = sorted(negative_segments, key=lambda x: x['quality_score'], reverse=True)
        selected_negative = negative_segments[:n_negative]

        # 计算统计信息
        sample_info = {
            'target_condition': target_condition,
            'n_positive': n_positive,
            'n_negative': n_negative,
            'positive_ratio': n_positive / (n_positive + n_negative) if (n_positive + n_negative) > 0 else 0,
            'avg_positive_quality': np.mean([seg['quality_score'] for seg in positive_segments]) if positive_segments else 0,
            'avg_negative_quality': np.mean([seg['quality_score'] for seg in selected_negative]) if selected_negative else 0,
        }

        return {
            'positive_samples': positive_segments,
            'negative_samples': selected_negative,
            'sample_info': sample_info
        }

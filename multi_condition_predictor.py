#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多工况预测器
用于加载训练好的多工况模型并进行实时预测

作者: TSlib项目组
日期: 2024-06-29
"""

import json
import pickle
import numpy as np
import pandas as pd
import torch
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

class WorkConditionClassifier:
    """工况分类器 - 基于规则的方法"""
    
    def __init__(self):
        self.condition_mapping = {
            0: '起钻',      # Tripping up
            1: '下钻',      # Tripping down  
            2: '正常钻进',   # Normal drilling
            3: '正划眼',    # Forward reaming
            4: '倒划眼'     # Reverse reaming
        }
        
        # 工况识别的置信度阈值
        self.confidence_thresholds = {
            '起钻': 0.85,
            '下钻': 0.85,
            '正常钻进': 0.70,
            '正划眼': 0.80,
            '倒划眼': 0.75
        }
    
    def classify_condition(self, data: pd.DataFrame, return_confidence: bool = True) -> Union[str, Tuple[str, float]]:
        """
        识别钻进工况
        
        Args:
            data: 钻进数据DataFrame，包含WOB, RPM, DEP等参数
            return_confidence: 是否返回置信度
            
        Returns:
            工况名称或(工况名称, 置信度)元组
        """
        if len(data) == 0:
            return ('正常钻进', 0.5) if return_confidence else '正常钻进'
        
        # 计算关键指标的统计量
        window_size = min(10, len(data))
        recent_data = data.tail(window_size)
        
        # 提取关键参数
        wob_mean = recent_data['WOB'].mean() if 'WOB' in recent_data.columns else 0
        rpm_mean = recent_data['RPM'].mean() if 'RPM' in recent_data.columns else 0
        hkld_mean = recent_data['HKLD'].mean() if 'HKLD' in recent_data.columns else 0
        
        # 计算深度变化率
        if 'DEP' in recent_data.columns and len(recent_data) > 1:
            dep_change_rate = recent_data['DEP'].diff().mean()
        else:
            dep_change_rate = 0
        
        # 工况识别逻辑
        condition_scores = {}
        
        # 起钻判断 (WOB低, 深度递减, HKLD高)
        起钻_score = 0
        if wob_mean < 20:
            起钻_score += 0.3
        if dep_change_rate < -0.05:
            起钻_score += 0.4
        if hkld_mean > 800:
            起钻_score += 0.3
        condition_scores['起钻'] = 起钻_score
        
        # 下钻判断 (WOB低, 深度递增, HKLD中等)
        下钻_score = 0
        if wob_mean < 20:
            下钻_score += 0.3
        if dep_change_rate > 0.05:
            下钻_score += 0.4
        if 400 < hkld_mean < 800:
            下钻_score += 0.3
        condition_scores['下钻'] = 下钻_score
        
        # 正常钻进判断 (WOB高, RPM高, 深度变化明显)
        正常钻进_score = 0
        if wob_mean > 40:
            正常钻进_score += 0.4
        if rpm_mean > 50:
            正常钻进_score += 0.3
        if abs(dep_change_rate) > 0.02:
            正常钻进_score += 0.3
        condition_scores['正常钻进'] = 正常钻进_score
        
        # 正划眼判断 (WOB中等, RPM中等, 深度变化小)
        正划眼_score = 0
        if 20 <= wob_mean <= 60:
            正划眼_score += 0.4
        if 30 <= rpm_mean <= 80:
            正划眼_score += 0.3
        if abs(dep_change_rate) < 0.02:
            正划眼_score += 0.3
        condition_scores['正划眼'] = 正划眼_score
        
        # 倒划眼判断 (其他情况的默认值)
        倒划眼_score = 0.2  # 基础分数
        if 10 <= wob_mean <= 40:
            倒划眼_score += 0.2
        if 20 <= rpm_mean <= 60:
            倒划眼_score += 0.2
        condition_scores['倒划眼'] = 倒划眼_score
        
        # 选择得分最高的工况
        best_condition = max(condition_scores, key=condition_scores.get)
        confidence = condition_scores[best_condition]
        
        # 如果置信度太低，默认为正常钻进
        if confidence < 0.5:
            best_condition = '正常钻进'
            confidence = 0.5
        
        if return_confidence:
            return best_condition, confidence
        else:
            return best_condition
    
    def extract_condition_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取工况相关的统计特征"""
        if len(data) == 0:
            return np.zeros((1, 16))
        
        condition_features = []
        window_size = min(10, len(data))
        
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            window_data = data.iloc[start_idx:i+1]
            
            features = []
            
            # WOB相关特征
            wob_data = window_data['WOB'] if 'WOB' in window_data.columns else pd.Series([0])
            features.extend([
                wob_data.mean(),
                wob_data.std(),
                (wob_data > 50).sum() / len(wob_data),
                (wob_data < 10).sum() / len(wob_data),
            ])
            
            # RPM相关特征  
            rpm_data = window_data['RPM'] if 'RPM' in window_data.columns else pd.Series([0])
            features.extend([
                rpm_data.mean(),
                rpm_data.std(),
                (rpm_data > 60).sum() / len(rpm_data),
            ])
            
            # 深度变化特征
            dep_data = window_data['DEP'] if 'DEP' in window_data.columns else pd.Series(range(len(window_data)))
            dep_diff = dep_data.diff().fillna(0)
            features.extend([
                dep_diff.mean(),
                dep_diff.std(),
                (dep_diff > 0.1).sum() / len(dep_diff),
                (dep_diff < -0.1).sum() / len(dep_diff),
            ])
            
            # HKLD和TOR特征
            hkld_data = window_data['HKLD'] if 'HKLD' in window_data.columns else pd.Series([0])
            tor_data = window_data['TOR'] if 'TOR' in window_data.columns else pd.Series([0])
            features.extend([
                hkld_data.mean(),
                hkld_data.std(),
                tor_data.mean(),
                tor_data.std(),
            ])
            
            # 钻进效率特征
            drilling_rate = abs(dep_diff.sum()) / max(len(window_data), 1)
            features.append(drilling_rate)
            
            condition_features.append(features)
        
        return np.array(condition_features)


class MultiConditionPredictor:
    """多工况预测器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.models = {}
        self.condition_classifier = WorkConditionClassifier()
        self.config = {}
        self.model_loaded = False
        
        if config_path:
            self.load_from_config(config_path)
    
    @classmethod
    def load_from_config(cls, config_path: str):
        """从配置文件加载预测器"""
        predictor = cls()
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            predictor.config = json.load(f)
        
        print(f"📋 加载预测器配置: {config_path}")
        
        # 加载各工况模型
        predictor._load_condition_models()
        
        return predictor
    
    def _load_condition_models(self):
        """加载各工况的训练模型"""
        print("🔄 加载工况模型...")
        
        model_paths = self.config.get('model_paths', {})
        
        for condition_name, model_path in model_paths.items():
            try:
                # 这里需要根据实际的模型保存格式来加载
                # 由于TSlib使用PyTorch，模型通常保存为.pth文件
                model_dir = Path(model_path)
                
                # 查找模型文件
                model_files = list(model_dir.glob("*.pth"))
                if model_files:
                    model_file = model_files[0]  # 取第一个模型文件
                    
                    # 注意：这里需要根据实际的模型类来加载
                    # 由于我们无法直接加载TSlib的模型，这里提供一个框架
                    print(f"  找到 {condition_name} 模型: {model_file}")
                    
                    # 实际加载代码应该类似：
                    # model = torch.load(model_file, map_location='cpu')
                    # self.models[condition_name] = model
                    
                    # 暂时用占位符
                    self.models[condition_name] = f"模型路径: {model_file}"
                    
                else:
                    print(f"  ⚠️  未找到 {condition_name} 的模型文件")
                    
            except Exception as e:
                print(f"  ❌ 加载 {condition_name} 模型失败: {e}")
        
        self.model_loaded = len(self.models) > 0
        print(f"✅ 成功加载 {len(self.models)} 个工况模型")
    
    def predict(self, drilling_data: pd.DataFrame, return_details: bool = False) -> Dict:
        """
        进行多工况感知预测
        
        Args:
            drilling_data: 钻进数据DataFrame
            return_details: 是否返回详细信息
            
        Returns:
            预测结果字典
        """
        if not self.model_loaded:
            raise RuntimeError("模型未加载，请先调用load_from_config()")
        
        # 步骤1: 工况识别
        condition, confidence = self.condition_classifier.classify_condition(
            drilling_data, return_confidence=True
        )
        
        # 步骤2: 数据预处理
        processed_data = self._preprocess_data(drilling_data)
        
        # 步骤3: 选择对应模型进行预测
        if condition in self.models:
            # 这里应该调用实际的模型预测
            # risk_score = self.models[condition].predict(processed_data)
            
            # 暂时使用模拟预测
            risk_score = self._simulate_prediction(processed_data, condition)
        else:
            # 如果没有对应工况的模型，使用默认模型
            print(f"⚠️  未找到 {condition} 的专用模型，使用默认预测")
            risk_score = self._simulate_prediction(processed_data, '正常钻进')
        
        # 步骤4: 预警级别判断
        alert_level = self._determine_alert_level(risk_score, condition)
        
        # 构建结果
        result = {
            'condition': condition,
            'condition_confidence': confidence,
            'risk_score': risk_score,
            'alert_level': alert_level,
            'timestamp': pd.Timestamp.now().isoformat(),
            'data_points': len(drilling_data)
        }
        
        if return_details:
            result['details'] = {
                'condition_features': self.condition_classifier.extract_condition_features(drilling_data),
                'processed_data_shape': processed_data.shape if hasattr(processed_data, 'shape') else None,
                'available_models': list(self.models.keys())
            }
        
        return result
    
    def _preprocess_data(self, data: pd.DataFrame) -> np.ndarray:
        """数据预处理"""
        # 确保有必要的列
        required_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
        
        processed_data = data.copy()
        
        # 添加缺失的列
        for col in required_cols:
            if col not in processed_data.columns:
                processed_data[col] = 0.0
        
        # 选择需要的列
        processed_data = processed_data[required_cols]
        
        # 处理缺失值
        processed_data = processed_data.fillna(method='ffill').fillna(0)
        
        # 标准化（这里使用简单的标准化，实际应该使用训练时的标准化参数）
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        processed_array = scaler.fit_transform(processed_data.values)
        
        return processed_array
    
    def _simulate_prediction(self, data: np.ndarray, condition: str) -> float:
        """模拟预测（用于演示，实际应该调用真实模型）"""
        # 基于工况和数据特征生成模拟的风险分数
        np.random.seed(42)  # 确保结果可重现
        
        # 不同工况的基础风险
        base_risks = {
            '起钻': 0.15,
            '下钻': 0.20,
            '正常钻进': 0.35,
            '正划眼': 0.25,
            '倒划眼': 0.30
        }
        
        base_risk = base_risks.get(condition, 0.25)
        
        # 基于数据特征调整风险
        if len(data) > 0:
            # 计算数据的变异性作为风险因子
            data_variance = np.var(data, axis=0).mean()
            risk_adjustment = min(data_variance * 0.1, 0.3)
            
            # 添加一些随机性
            random_factor = np.random.normal(0, 0.05)
            
            final_risk = np.clip(base_risk + risk_adjustment + random_factor, 0, 1)
        else:
            final_risk = base_risk
        
        return final_risk
    
    def _determine_alert_level(self, risk_score: float, condition: str) -> str:
        """确定预警级别"""
        # 不同工况的预警阈值
        thresholds = {
            '起钻': {'low': 0.3, 'medium': 0.6, 'high': 0.8},
            '下钻': {'low': 0.3, 'medium': 0.6, 'high': 0.8},
            '正常钻进': {'low': 0.4, 'medium': 0.7, 'high': 0.85},
            '正划眼': {'low': 0.35, 'medium': 0.65, 'high': 0.8},
            '倒划眼': {'low': 0.35, 'medium': 0.65, 'high': 0.8}
        }
        
        condition_thresholds = thresholds.get(condition, thresholds['正常钻进'])
        
        if risk_score >= condition_thresholds['high']:
            return 'HIGH'
        elif risk_score >= condition_thresholds['medium']:
            return 'MEDIUM'
        elif risk_score >= condition_thresholds['low']:
            return 'LOW'
        else:
            return 'NORMAL'
    
    def batch_predict(self, data_list: List[pd.DataFrame]) -> List[Dict]:
        """批量预测"""
        results = []
        
        for i, data in enumerate(data_list):
            try:
                result = self.predict(data)
                results.append(result)
            except Exception as e:
                results.append({
                    'error': str(e),
                    'index': i
                })
        
        return results
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'loaded_models': list(self.models.keys()),
            'condition_classifier': type(self.condition_classifier).__name__,
            'config': self.config,
            'model_loaded': self.model_loaded
        }


# 使用示例
if __name__ == "__main__":
    # 创建示例数据
    sample_data = pd.DataFrame({
        'DEP': np.random.uniform(1000, 2000, 100),
        'BITDEP': np.random.uniform(1000, 2000, 100),
        'WOB': np.random.uniform(20, 80, 100),
        'HKLD': np.random.uniform(400, 1200, 100),
        'RPM': np.random.uniform(40, 120, 100),
        'TOR': np.random.uniform(10, 50, 100),
        'SPP': np.random.uniform(5, 25, 100),
        'CSIP': np.random.uniform(0, 10, 100),
        'HOKHEI': np.random.uniform(5, 15, 100),
        'DRITIME': np.arange(100) * 5
    })
    
    # 测试工况分类器
    classifier = WorkConditionClassifier()
    condition, confidence = classifier.classify_condition(sample_data)
    print(f"识别工况: {condition} (置信度: {confidence:.3f})")
    
    # 测试预测器（需要先有配置文件）
    # predictor = MultiConditionPredictor.load_from_config('./checkpoints/multi_condition/predictor_config.json')
    # result = predictor.predict(sample_data)
    # print(f"预测结果: {result}")

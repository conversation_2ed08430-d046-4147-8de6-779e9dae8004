# 钻进数据预处理结果使用指南

## 处理概览
- 处理时间: 2025-06-30 00:36:13
- 处理文件数: 314/2392
- 总段数: 136

## 工况分布
- **UNKNOWN**: 79 段
- **正常钻进**: 44 段
- **起钻**: 2 段
- **正划眼**: 11 段

## 训练样本统计

### 起钻
- 正样本: 1 个
- 负样本: 3 个  
- 正样本比例: 0.250
- 平均质量分数: 0.749

### 下钻
- 正样本: 0 个
- 负样本: 0 个  
- 正样本比例: 0.000
- 平均质量分数: 0.000

### 正常钻进
- 正样本: 44 个
- 负样本: 92 个  
- 正样本比例: 0.324
- 平均质量分数: 0.737

### 正划眼
- 正样本: 7 个
- 负样本: 28 个  
- 正样本比例: 0.200
- 平均质量分数: 0.732

### 倒划眼
- 正样本: 0 个
- 负样本: 0 个  
- 正样本比例: 0.000
- 平均质量分数: 0.000

## 使用方法

### 1. 训练模型
```python
# 使用处理后的训练样本训练PatchTST模型
from run_earlysignaldet_simple import create_args_for_earlysignaldet
from exp.exp_earlysignaldet import Exp_Earlysignaldet

# 为特定工况创建配置
args = create_args_for_earlysignaldet()
args.data_path = './processed_data/training_samples/起钻/'  # 指定工况目录

# 初始化实验
exp = Exp_Earlysignaldet(args)

# 训练模型
exp.train()
```

### 2. 数据质量检查
- 所有段的质量分数都 >= 0.65
- 段长度在 30-300 之间
- 已移除工况转换区域的数据点

### 3. 注意事项
1. **工况标签**: 已标准化处理，统一为5种标准工况
2. **数据完整性**: 已过滤缺失值过多的段
3. **时间连续性**: 已验证时间序列的连续性
4. **参数合理性**: 已验证钻进参数与工况的一致性

### 4. 文件结构
```
processed_data/
├── segments/           # 按工况分组的段数据
│   ├── 起钻/
│   ├── 下钻/
│   └── ...
├── training_samples/   # 训练样本
│   ├── 起钻/
│   │   ├── positive_0000.csv
│   │   ├── negative_0000.csv
│   │   └── sample_info.json
│   └── ...
└── reports/           # 处理报告
    ├── processing_report.md
    ├── data_analysis.json
    └── processing_visualization.png
```

## 下一步建议
1. 使用 `run_multi_condition_earlysignaldet.py` 进行多工况模型训练
2. 根据实际需求调整训练样本比例
3. 定期验证模型性能并更新训练数据

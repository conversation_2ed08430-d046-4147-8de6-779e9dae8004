#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细预测测试器
显示具体时间点的卡钻预测结果，包括时间戳、预测概率和风险等级
"""

import sys
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from real_data_tester import RealDataTester

class DetailedPredictionTester(RealDataTester):
    def __init__(self):
        super().__init__()
        
    def analyze_single_file_detailed(self, file_path, max_anomalies=20):
        """详细分析单个文件，显示具体的异常时间点"""
        print(f"\n{'='*80}")
        print(f"详细分析文件: {Path(file_path).name}")
        print(f"{'='*80}")
        
        try:
            # 读取数据
            data = pd.read_csv(file_path, encoding='utf-8')
            print(f"数据形状: {data.shape}")
            
            # 检查时间列
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                print(f"时间范围: {data['date'].min()} 到 {data['date'].max()}")
            
            # 确定工况
            if 'RIGSTA' in data.columns:
                conditions_in_data = data['RIGSTA'].value_counts()
                print(f"工况分布:")
                for condition, count in conditions_in_data.items():
                    print(f"  {condition}: {count} 条记录 ({count/len(data)*100:.1f}%)")
                
                main_condition = conditions_in_data.index[0]
                if main_condition in self.condition_mapping:
                    test_condition = self.condition_mapping[main_condition]
                else:
                    test_condition = '正常钻进'
            else:
                test_condition = '正常钻进'
            
            print(f"使用模型: {test_condition}")
            
            # 进行预测
            result, error = self.predict_condition(data, test_condition)
            
            if error:
                print(f"预测失败: {error}")
                return None
            
            # 分析预测结果
            predictions = result['predictions']
            probabilities = result['probabilities']
            timestamps = result['timestamps']
            
            print(f"\n预测结果概览:")
            print(f"  总时间窗口数: {len(predictions)}")
            print(f"  异常窗口数: {sum(predictions)}")
            print(f"  正常窗口数: {len(predictions) - sum(predictions)}")
            print(f"  异常比例: {np.mean(predictions)*100:.2f}%")
            
            # 找出异常时间点
            anomaly_indices = [i for i, pred in enumerate(predictions) if pred == 1]
            
            if anomaly_indices:
                print(f"\n[警告] 检测到 {len(anomaly_indices)} 个异常时间窗口:")
                print(f"{'序号':<4} {'时间戳':<20} {'异常概率':<10} {'正常概率':<10} {'风险等级':<8}")
                print("-" * 60)
                
                # 显示前max_anomalies个异常点
                for i, idx in enumerate(anomaly_indices[:max_anomalies]):
                    timestamp = timestamps[idx] if idx < len(timestamps) else f"Window_{idx+1}"
                    anomaly_prob = probabilities[idx][1] if len(probabilities[idx]) > 1 else 0
                    normal_prob = probabilities[idx][0] if len(probabilities[idx]) > 0 else 0
                    
                    if anomaly_prob > 0.9:
                        risk_level = "极高风险"
                    elif anomaly_prob > 0.8:
                        risk_level = "高风险"
                    elif anomaly_prob > 0.6:
                        risk_level = "中风险"
                    else:
                        risk_level = "低风险"
                    
                    print(f"{i+1:<4} {str(timestamp):<20} {anomaly_prob:<10.4f} {normal_prob:<10.4f} {risk_level:<8}")
                
                if len(anomaly_indices) > max_anomalies:
                    print(f"... 还有 {len(anomaly_indices) - max_anomalies} 个异常点未显示")
                
                # 统计风险等级分布
                risk_stats = {"极高风险": 0, "高风险": 0, "中风险": 0, "低风险": 0}
                for idx in anomaly_indices:
                    anomaly_prob = probabilities[idx][1] if len(probabilities[idx]) > 1 else 0
                    if anomaly_prob > 0.9:
                        risk_stats["极高风险"] += 1
                    elif anomaly_prob > 0.8:
                        risk_stats["高风险"] += 1
                    elif anomaly_prob > 0.6:
                        risk_stats["中风险"] += 1
                    else:
                        risk_stats["低风险"] += 1
                
                print(f"\n风险等级分布:")
                for level, count in risk_stats.items():
                    if count > 0:
                        print(f"  {level}: {count} 个时间窗口 ({count/len(anomaly_indices)*100:.1f}%)")
                
                # 最高风险时间点
                max_prob_idx = np.argmax([probabilities[idx][1] if len(probabilities[idx]) > 1 else 0 for idx in anomaly_indices])
                max_risk_idx = anomaly_indices[max_prob_idx]
                max_timestamp = timestamps[max_risk_idx] if max_risk_idx < len(timestamps) else f"Window_{max_risk_idx+1}"
                max_prob = probabilities[max_risk_idx][1] if len(probabilities[max_risk_idx]) > 1 else 0
                
                print(f"\n[最高风险] 最高风险时间点:")
                print(f"  时间: {max_timestamp}")
                print(f"  异常概率: {max_prob:.4f}")
                print(f"  对应数据行: {max_risk_idx*60} - {max_risk_idx*60+180}")
                
            else:
                print(f"\n[正常] 未检测到异常时间窗口")
            
            return {
                'file': str(file_path),
                'condition': test_condition,
                'total_windows': len(predictions),
                'anomaly_windows': len(anomaly_indices),
                'anomaly_ratio': np.mean(predictions),
                'max_anomaly_prob': max([prob[1] for prob in probabilities]) if probabilities and len(probabilities[0]) > 1 else 0,
                'anomaly_details': [(timestamps[idx] if idx < len(timestamps) else f"Window_{idx+1}", 
                                   probabilities[idx][1] if len(probabilities[idx]) > 1 else 0) 
                                  for idx in anomaly_indices[:max_anomalies]]
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            return None
    
    def save_detailed_results(self, file_path, results, output_file=None):
        """保存详细的预测结果到CSV文件"""
        if not results or not results['anomaly_details']:
            print("没有异常结果需要保存")
            return
        
        if output_file is None:
            filename = Path(file_path).stem
            output_file = f"{filename}_详细预测结果.csv"
        
        # 创建详细结果DataFrame
        detailed_data = []
        for timestamp, prob in results['anomaly_details']:
            detailed_data.append({
                '时间戳': timestamp,
                '异常概率': prob,
                '正常概率': 1 - prob,
                '风险等级': '极高风险' if prob > 0.9 else '高风险' if prob > 0.8 else '中风险' if prob > 0.6 else '低风险',
                '工况': results['condition'],
                '文件名': Path(results['file']).name
            })
        
        df = pd.DataFrame(detailed_data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n详细预测结果已保存到: {output_file}")

def main():
    """主函数"""
    tester = DetailedPredictionTester()
    
    # 加载模型
    tester.load_trained_models()
    
    if not tester.models:
        print("没有可用的模型，请先训练模型")
        return
    
    # 选择一个测试文件进行详细分析
    test_files = [
        "dataset/processed_data/test_data/宁209H67-6_20220528_160000_测试数据.csv",  # 异常比例最高的文件
        "dataset/processed_data/test_data/泸203H12-4_20220308_220000_测试数据.csv",  # 最高异常概率的文件
        "dataset/processed_data/test_data/宁209H70-5_20220513_055000_测试数据.csv",  # 正划眼工况文件
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n开始详细分析: {Path(test_file).name}")
            results = tester.analyze_single_file_detailed(test_file, max_anomalies=10)
            
            if results and results['anomaly_details']:
                tester.save_detailed_results(test_file, results)
            
            print(f"\n{'='*80}")
            input("按回车键继续分析下一个文件...")
        else:
            print(f"文件不存在: {test_file}")

if __name__ == "__main__":
    main()

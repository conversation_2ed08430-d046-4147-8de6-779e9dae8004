# 🚀 早期信号检测模型 - VSCode运行指南

本指南将帮助您在VSCode环境中运行早期信号检测模型，替代原有的PyCharm命令行配置。

## 📋 目录结构

```
kazuan-6.29/
├── run_earlysignaldet.py      # 主运行脚本
├── experiment_manager.py      # 实验管理器
├── README_VSCODE.md          # 本说明文件
├── .vscode/                  # VSCode配置文件夹
│   ├── launch.json           # 调试配置
│   ├── settings.json         # 工作区设置
│   └── tasks.json           # 任务配置
└── ... (其他原有文件)
```

## 🎯 快速开始

### 方法1: 直接运行Python脚本

```bash
# 完整训练+预测流程
python run_earlysignaldet.py

# 仅训练
python run_earlysignaldet.py --no_predict

# 仅预测
python run_earlysignaldet.py --no_training

# 快速测试
python run_earlysignaldet.py --train_epochs 5 --batch_size 8
```

### 方法2: 使用VSCode调试功能

1. 按 `F5` 或点击 `运行和调试`
2. 选择以下预设配置之一：
   - 🚀 **早期信号检测 - 完整训练+预测**: 默认完整流程
   - 🎯 **早期信号检测 - 仅训练**: 只进行模型训练
   - 🔮 **早期信号检测 - 仅预测**: 只进行预测
   - ⚡ **早期信号检测 - 快速测试**: 5轮训练的快速测试
   - 🔧 **早期信号检测 - 自定义参数**: 使用优化参数
   - 🐛 **调试模式**: 启用断点调试

### 方法3: 使用任务面板

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Tasks: Run Task`
3. 选择相应任务：
   - 🚀 运行早期信号检测 - 完整流程
   - 🎯 运行早期信号检测 - 仅训练
   - 🔮 运行早期信号检测 - 仅预测
   - ⚡ 快速测试 - 早期信号检测

## 🧪 实验管理器使用

### 列出所有预设实验

```bash
python experiment_manager.py --list
```

### 运行单个实验

```bash
# 运行基础实验
python experiment_manager.py --run baseline

# 运行快速测试
python experiment_manager.py --run quick_test

# 运行大模型实验
python experiment_manager.py --run large_model
```

### 批量运行实验

```bash
# 批量运行多个实验
python experiment_manager.py --batch baseline quick_test large_model
```

### 交互式模式

```bash
# 启动交互式实验管理器
python experiment_manager.py
```

## 🔧 参数配置说明

### 主要参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--batch_size` | 16 | 批次大小 |
| `--learning_rate` | 0.001 | 学习率 |
| `--train_epochs` | 100 | 训练轮数 |
| `--d_model` | 128 | 模型维度 |
| `--e_layers` | 3 | 编码器层数 |
| `--patience` | 10 | 早停耐心值 |
| `--no_predict` | False | 不进行预测 |
| `--no_training` | False | 不进行训练 |

### 配置类参数

在 `run_earlysignaldet.py` 中的 `EarlySignalDetectionConfig` 类包含所有配置参数：

```python
config = EarlySignalDetectionConfig()
config.update_config(
    batch_size=32,
    learning_rate=0.0005,
    d_model=256
)
```

## 📊 预设实验配置

| 实验ID | 名称 | 描述 | 主要特点 |
|--------|------|------|----------|
| `baseline` | 基础实验 | 默认参数配置 | 标准配置 |
| `quick_test` | 快速测试 | 快速验证代码 | 5轮训练，小批次 |
| `large_model` | 大模型实验 | 更大模型容量 | d_model=256, 4层编码器 |
| `low_lr` | 小学习率实验 | 精细调优 | lr=0.0005, 150轮训练 |
| `large_batch` | 大批次实验 | 大批次训练 | batch_size=32 |
| `deep_network` | 深层网络实验 | 更深网络结构 | 6层编码器 |
| `predict_only` | 仅预测实验 | 使用已训练模型预测 | 不训练，仅预测 |

## 🐛 调试功能

### 设置断点

1. 在代码行号左侧点击设置断点
2. 选择 "🐛 调试模式" 配置
3. 按 `F5` 开始调试

### 调试技巧

- 使用 `F10` 单步执行
- 使用 `F11` 进入函数
- 使用 `Shift+F11` 跳出函数
- 在调试控制台中查看变量值

## 📁 输出文件说明

### 训练结果

- **模型文件**: `./checkpoints/` 目录下
- **训练日志**: 控制台输出
- **结果文件**: `./results/` 目录下

### 实验结果

- **实验记录**: `./experiment_results/` 目录下
- **文件格式**: JSON格式，包含完整配置和结果

## ⚡ 常用任务

### 检查环境

```bash
# 检查依赖包
python -c "import torch, numpy, pandas, sklearn; print('✅ 环境正常')"

# 检查GPU
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"
```

### 清理缓存

```bash
# 清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} +

# 或使用任务面板中的 "🧹 清理缓存文件"
```

### 查看数据集

```bash
# 查看数据集结构
python -c "import os; [print(f'{root}/{file}') for root, dirs, files in os.walk('./dataset/earlysignaldetection') for file in files[:3] if file.endswith('.csv')]"
```

## 🔍 故障排除

### 常见问题

1. **模块导入错误**
   - 确保在项目根目录运行
   - 检查 `PYTHONPATH` 设置

2. **数据路径错误**
   - 确认 `./dataset/earlysignaldetection` 目录存在
   - 检查数据文件完整性

3. **GPU内存不足**
   - 减小 `batch_size` 参数
   - 使用 `--batch_size 8` 或更小值

4. **训练中断**
   - 检查早停设置 `patience`
   - 查看训练日志确认原因

### 获取帮助

```bash
# 查看脚本帮助
python run_earlysignaldet.py --help

# 查看实验管理器帮助
python experiment_manager.py --help
```

## 🎉 完成！

现在您可以在VSCode中愉快地运行早期信号检测模型了！

- 使用 `F5` 快速启动调试
- 使用任务面板执行常用操作
- 使用实验管理器进行批量实验
- 享受VSCode的强大调试功能

如有问题，请检查控制台输出或查看上述故障排除部分。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版钻井卡钻预测系统
解决时间戳显示、未知工况处理、结果可视化等问题
"""

import sys
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from real_data_tester import RealDataTester

class EnhancedDrillingPredictor(RealDataTester):
    def __init__(self):
        super().__init__()
        
        # 扩展的工况映射规则
        self.condition_mapping = {
            # 标准工况
            '钻进': '正常钻进',
            '正常钻进': '正常钻进',
            '起钻': '起钻', 
            '下钻': '下钻',
            '正划眼': '正划眼',
            '倒划眼': '倒划眼',
            
            # 其他工况映射规则
            '其他': '正常钻进',  # 默认映射到正常钻进
            '循环': '正常钻进',  # 循环作业映射到正常钻进
            '接单根': '下钻',    # 接单根作业映射到下钻
            '卸单根': '起钻',    # 卸单根作业映射到起钻
            '短起': '起钻',      # 短起映射到起钻
            '短下': '下钻',      # 短下映射到下钻
            '划眼': '正划眼',    # 划眼映射到正划眼
            '扩眼': '正划眼',    # 扩眼映射到正划眼
            '通井': '正常钻进',  # 通井映射到正常钻进
            '洗井': '正常钻进',  # 洗井映射到正常钻进
            '测试': '正常钻进',  # 测试映射到正常钻进
            '停钻': '正常钻进',  # 停钻映射到正常钻进
        }
        
        # 未知工况处理策略
        self.unknown_condition_strategy = 'default_model'  # 'default_model' 或 'skip'
        self.default_model = '正常钻进'
        
    def get_condition_for_prediction(self, rigsta_value):
        """
        根据RIGSTA值确定预测使用的工况模型
        
        Args:
            rigsta_value: RIGSTA工况标签值
            
        Returns:
            tuple: (mapped_condition, is_unknown)
        """
        if rigsta_value in self.condition_mapping:
            return self.condition_mapping[rigsta_value], False
        else:
            # 处理未知工况
            if self.unknown_condition_strategy == 'default_model':
                print(f"警告: 未知工况 '{rigsta_value}'，使用默认模型 '{self.default_model}'")
                return self.default_model, True
            else:
                print(f"警告: 未知工况 '{rigsta_value}'，跳过预测")
                return None, True
    
    def create_time_windows_with_timestamps(self, data, window_size=180, stride=60):
        """创建时间窗口并生成对应的真实时间戳"""
        windows = []
        timestamps = []
        
        # 检查是否有date列用于时间戳
        has_date_column = 'date' in data.columns

        if has_date_column:
            # 确保date列为datetime类型
            if not pd.api.types.is_datetime64_any_dtype(data['date']):
                data['date'] = pd.to_datetime(data['date'])

        for i in range(0, len(data) - window_size + 1, stride):
            # 只使用特征列创建窗口
            feature_data = data[self.feature_cols].iloc[i:i+window_size].values
            if feature_data.shape[0] == window_size:
                windows.append(feature_data)

                if has_date_column:
                    # 使用窗口结束时间作为时间戳
                    end_time = data.iloc[i+window_size-1]['date']
                    timestamps.append(end_time)
                else:
                    # 使用窗口索引
                    timestamps.append(f"Window_{len(windows)}")

        return np.array(windows) if windows else np.empty((0, window_size, len(self.feature_cols))), timestamps
    
    def analyze_file_enhanced(self, file_path, save_visualization=True):
        """增强版文件分析，包含真实时间戳和可视化"""
        print(f"\n{'='*80}")
        print(f"增强版分析: {Path(file_path).name}")
        print(f"{'='*80}")
        
        try:
            # 读取数据
            data = pd.read_csv(file_path, encoding='utf-8')
            print(f"数据形状: {data.shape}")
            
            # 处理时间列
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                print(f"时间范围: {data['date'].min()} 到 {data['date'].max()}")
                time_span = data['date'].max() - data['date'].min()
                print(f"时间跨度: {time_span}")
            
            # 分析工况分布
            condition_stats = {}
            unknown_conditions = []
            
            if 'RIGSTA' in data.columns:
                conditions_in_data = data['RIGSTA'].value_counts()
                print(f"\n工况分布分析:")
                print(f"{'工况':<15} {'记录数':<10} {'占比':<8} {'映射模型':<12} {'状态'}")
                print("-" * 60)
                
                for condition, count in conditions_in_data.items():
                    mapped_condition, is_unknown = self.get_condition_for_prediction(condition)
                    percentage = count/len(data)*100
                    status = "未知工况" if is_unknown else "已知工况"
                    
                    print(f"{condition:<15} {count:<10} {percentage:<7.1f}% {mapped_condition or 'N/A':<12} {status}")
                    
                    if mapped_condition:
                        if mapped_condition not in condition_stats:
                            condition_stats[mapped_condition] = 0
                        condition_stats[mapped_condition] += count
                    
                    if is_unknown:
                        unknown_conditions.append(condition)
                
                # 确定主要使用的模型
                if condition_stats:
                    main_model = max(condition_stats.items(), key=lambda x: x[1])[0]
                else:
                    main_model = self.default_model
            else:
                main_model = self.default_model
                print(f"未找到RIGSTA列，使用默认模型: {main_model}")
            
            print(f"\n使用模型: {main_model}")
            if unknown_conditions:
                print(f"发现未知工况: {unknown_conditions}")
            
            # 预处理数据
            processed_data = self.preprocess_data(data, main_model)
            
            # 创建时间窗口（使用增强版方法）
            windows, timestamps = self.create_time_windows_with_timestamps(processed_data)
            
            if len(windows) == 0:
                print("错误: 数据长度不足，无法创建时间窗口")
                return None
            
            # 进行预测
            model = self.models.get(main_model)
            if not model:
                print(f"错误: 没有找到模型 {main_model}")
                return None
            
            # 预测
            predictions = []
            probabilities = []
            
            print(f"\n开始预测 {len(windows)} 个时间窗口...")
            
            with torch.no_grad():
                for i in range(0, len(windows), 32):  # 批处理
                    batch = windows[i:i+32]
                    batch_tensor = torch.FloatTensor(batch).to(self.device)
                    
                    outputs = model(batch_tensor)
                    probs = torch.softmax(outputs, dim=1)
                    preds = torch.argmax(probs, dim=1)
                    
                    predictions.extend(preds.cpu().numpy())
                    probabilities.extend(probs.cpu().numpy())
            
            # 分析结果
            anomaly_count = sum(predictions)
            anomaly_ratio = np.mean(predictions)
            
            print(f"\n预测结果概览:")
            print(f"  总时间窗口数: {len(predictions)}")
            print(f"  异常窗口数: {anomaly_count}")
            print(f"  正常窗口数: {len(predictions) - anomaly_count}")
            print(f"  异常比例: {anomaly_ratio*100:.2f}%")
            
            # 创建详细结果DataFrame
            results_df = self.create_detailed_results_df(
                timestamps, predictions, probabilities, main_model, 
                unknown_conditions, file_path
            )
            
            # 保存详细结果
            output_file = self.save_enhanced_results(results_df, file_path)
            
            # 生成可视化
            if save_visualization and len(results_df) > 0:
                self.create_visualizations(results_df, file_path)
            
            return {
                'file': str(file_path),
                'model_used': main_model,
                'total_windows': len(predictions),
                'anomaly_windows': anomaly_count,
                'anomaly_ratio': anomaly_ratio,
                'unknown_conditions': unknown_conditions,
                'results_df': results_df,
                'output_file': output_file
            }
            
        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_detailed_results_df(self, timestamps, predictions, probabilities, 
                                 model_used, unknown_conditions, file_path):
        """创建详细结果DataFrame"""
        results_data = []
        
        for i, (timestamp, pred, prob) in enumerate(zip(timestamps, predictions, probabilities)):
            anomaly_prob = prob[1] if len(prob) > 1 else 0
            normal_prob = prob[0] if len(prob) > 0 else 0
            
            # 确定风险等级
            if anomaly_prob > 0.9:
                risk_level = "极高风险"
            elif anomaly_prob > 0.8:
                risk_level = "高风险"
            elif anomaly_prob > 0.6:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            # 只保存异常的时间窗口
            if pred == 1:
                results_data.append({
                    '时间戳': timestamp,
                    '异常概率': anomaly_prob,
                    '正常概率': normal_prob,
                    '风险等级': risk_level,
                    '使用模型': model_used,
                    '窗口索引': i,
                    '对应数据行开始': i * 60,
                    '对应数据行结束': i * 60 + 180,
                    '是否异常': '是' if pred == 1 else '否',
                    '文件名': Path(file_path).name,
                    '未知工况': ', '.join(unknown_conditions) if unknown_conditions else '无'
                })
        
        return pd.DataFrame(results_data)

    def save_enhanced_results(self, results_df, file_path):
        """保存增强版结果"""
        if results_df.empty:
            print("没有异常结果需要保存")
            return None

        filename = Path(file_path).stem
        output_file = f"{filename}_增强版预测结果.csv"

        # 保存CSV文件
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n增强版预测结果已保存到: {output_file}")

        # 打印前10个异常时间点
        print(f"\n前10个异常时间点:")
        print(f"{'序号':<4} {'时间戳':<20} {'异常概率':<10} {'风险等级':<10}")
        print("-" * 50)

        for i, row in results_df.head(10).iterrows():
            timestamp_str = str(row['时间戳'])[:19] if len(str(row['时间戳'])) > 19 else str(row['时间戳'])
            print(f"{i+1:<4} {timestamp_str:<20} {row['异常概率']:<10.4f} {row['风险等级']:<10}")

        if len(results_df) > 10:
            print(f"... 还有 {len(results_df) - 10} 个异常点")

        return output_file

    def create_visualizations(self, results_df, file_path):
        """创建可视化图表"""
        if results_df.empty:
            print("没有异常数据，跳过可视化")
            return

        filename = Path(file_path).stem

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'钻井卡钻预测分析 - {filename}', fontsize=16, fontweight='bold')

        # 1. 异常概率时间序列图
        ax1 = axes[0, 0]
        if pd.api.types.is_datetime64_any_dtype(results_df['时间戳']):
            x_data = results_df['时间戳']
        else:
            x_data = range(len(results_df))

        colors = ['red' if level == '极高风险' else 'orange' if level == '高风险'
                 else 'yellow' if level == '中风险' else 'green'
                 for level in results_df['风险等级']]

        scatter = ax1.scatter(x_data, results_df['异常概率'], c=colors, alpha=0.7, s=50)
        ax1.plot(x_data, results_df['异常概率'], 'b-', alpha=0.3, linewidth=1)
        ax1.set_title('异常概率时间序列')
        ax1.set_ylabel('异常概率')
        ax1.grid(True, alpha=0.3)

        # 添加风险阈值线
        ax1.axhline(y=0.9, color='red', linestyle='--', alpha=0.5, label='极高风险阈值')
        ax1.axhline(y=0.8, color='orange', linestyle='--', alpha=0.5, label='高风险阈值')
        ax1.axhline(y=0.6, color='yellow', linestyle='--', alpha=0.5, label='中风险阈值')
        ax1.legend()

        # 2. 风险等级分布饼图
        ax2 = axes[0, 1]
        risk_counts = results_df['风险等级'].value_counts()
        colors_pie = ['red', 'orange', 'yellow', 'green']
        ax2.pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%',
                colors=colors_pie[:len(risk_counts)])
        ax2.set_title('风险等级分布')

        # 3. 异常概率分布直方图
        ax3 = axes[1, 0]
        ax3.hist(results_df['异常概率'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.set_title('异常概率分布')
        ax3.set_xlabel('异常概率')
        ax3.set_ylabel('频次')
        ax3.grid(True, alpha=0.3)

        # 4. 风险等级时间热力图
        ax4 = axes[1, 1]

        # 创建风险等级数值映射
        risk_mapping = {'低风险': 1, '中风险': 2, '高风险': 3, '极高风险': 4}
        results_df['风险数值'] = results_df['风险等级'].map(risk_mapping)

        if len(results_df) > 1:
            # 创建时间窗口的热力图数据
            time_windows = np.arange(len(results_df))
            risk_values = results_df['风险数值'].values

            # 创建2D数组用于热力图
            heatmap_data = risk_values.reshape(1, -1)

            im = ax4.imshow(heatmap_data, cmap='YlOrRd', aspect='auto')
            ax4.set_title('风险等级时间分布')
            ax4.set_xlabel('时间窗口')
            ax4.set_yticks([])

            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax4)
            cbar.set_label('风险等级')
            cbar.set_ticks([1, 2, 3, 4])
            cbar.set_ticklabels(['低风险', '中风险', '高风险', '极高风险'])
        else:
            ax4.text(0.5, 0.5, '数据点不足\n无法生成热力图',
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('风险等级时间分布')

        plt.tight_layout()

        # 保存图表
        chart_file = f"{filename}_可视化分析.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存到: {chart_file}")

        plt.show()

        # 创建详细的时间序列图
        self.create_detailed_timeline_chart(results_df, filename)

    def create_detailed_timeline_chart(self, results_df, filename):
        """创建详细的时间线图表"""
        if results_df.empty:
            return

        plt.figure(figsize=(20, 8))

        # 准备数据
        if pd.api.types.is_datetime64_any_dtype(results_df['时间戳']):
            x_data = results_df['时间戳']
            plt.xlabel('时间')
        else:
            x_data = range(len(results_df))
            plt.xlabel('时间窗口索引')

        # 绘制异常概率曲线
        plt.plot(x_data, results_df['异常概率'], 'b-', linewidth=2, label='异常概率', alpha=0.7)

        # 根据风险等级着色散点
        risk_colors = {
            '极高风险': 'red',
            '高风险': 'orange',
            '中风险': 'yellow',
            '低风险': 'green'
        }

        for risk_level, color in risk_colors.items():
            mask = results_df['风险等级'] == risk_level
            if mask.any():
                if pd.api.types.is_datetime64_any_dtype(results_df['时间戳']):
                    x_masked = results_df.loc[mask, '时间戳']
                else:
                    x_masked = [x_data[i] for i in range(len(x_data)) if mask.iloc[i]]
                plt.scatter(x_masked, results_df.loc[mask, '异常概率'],
                           c=color, label=risk_level, s=60, alpha=0.8, edgecolors='black')

        # 添加风险阈值线
        plt.axhline(y=0.9, color='red', linestyle='--', alpha=0.5, label='极高风险阈值(0.9)')
        plt.axhline(y=0.8, color='orange', linestyle='--', alpha=0.5, label='高风险阈值(0.8)')
        plt.axhline(y=0.6, color='yellow', linestyle='--', alpha=0.5, label='中风险阈值(0.6)')

        plt.title(f'钻井卡钻预测时间线分析 - {filename}', fontsize=14, fontweight='bold')
        plt.ylabel('异常概率')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存详细时间线图
        timeline_file = f"{filename}_详细时间线.png"
        plt.savefig(timeline_file, dpi=300, bbox_inches='tight')
        print(f"详细时间线图已保存到: {timeline_file}")

        plt.show()

def main():
    """主函数"""
    predictor = EnhancedDrillingPredictor()

    # 加载模型
    predictor.load_trained_models()

    if not predictor.models:
        print("没有可用的模型，请先训练模型")
        return

    # 测试文件
    test_files = [
        "dataset/processed_data/test_data/宁209H67-6_20220528_160000_测试数据.csv",
        "dataset/processed_data/test_data/泸203H12-4_20220308_220000_测试数据.csv",
        "dataset/processed_data/test_data/宁209H70-5_20220513_055000_测试数据.csv",
    ]

    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n开始增强版分析: {Path(test_file).name}")
            results = predictor.analyze_file_enhanced(test_file, save_visualization=True)

            if results:
                print(f"\n分析完成:")
                print(f"  使用模型: {results['model_used']}")
                print(f"  异常窗口数: {results['anomaly_windows']}")
                print(f"  异常比例: {results['anomaly_ratio']*100:.2f}%")
                if results['unknown_conditions']:
                    print(f"  未知工况: {results['unknown_conditions']}")

            print(f"\n{'='*80}")
            input("按回车键继续分析下一个文件...")
        else:
            print(f"文件不存在: {test_file}")

if __name__ == "__main__":
    main()

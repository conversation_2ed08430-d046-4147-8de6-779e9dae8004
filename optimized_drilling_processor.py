#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的钻井数据处理器
专门针对工况分类和时间窗口切分的最佳实践

处理策略：
1. 先按工况（RIGSTA）分类
2. 每个工况内的时间段独立处理
3. 对每个独立时间段进行3分钟窗口切分
4. 避免虚假的时间连续性
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class OptimizedDrillingProcessor:
    """
    优化的钻井数据处理器
    """
    
    def __init__(self):
        # 核心配置
        self.window_minutes = 3  # 3分钟时间窗口
        self.min_segment_minutes = 3  # 最小时间段长度（分钟）
        self.sampling_interval_seconds = 10  # 假设10秒采样间隔
        
        # 计算最小记录数
        self.min_records_per_window = (self.window_minutes * 60) // self.sampling_interval_seconds  # 18条记录
        self.min_records_per_segment = self.min_records_per_window  # 至少一个完整窗口
        
        # 必需的钻井参数
        self.required_params = [
            'DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP'
        ]
        
        # 可选参数
        self.optional_params = [
            'HOKHEI', 'DRITIME', 'CSIP'
        ]
        
        # 关键字段
        self.key_fields = ['RIGSTA', 'date']
        
        print(f"🔧 初始化处理器:")
        print(f"   - 时间窗口: {self.window_minutes} 分钟")
        print(f"   - 最小时间段: {self.min_segment_minutes} 分钟")
        print(f"   - 每窗口最小记录数: {self.min_records_per_window}")
    
    def detect_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)
                return encoding
            except UnicodeDecodeError:
                continue
        
        return 'utf-8'  # 默认编码
    
    def load_and_validate_data(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        加载并验证数据
        """
        try:
            encoding = self.detect_encoding(file_path)
            df = pd.read_csv(file_path, encoding=encoding)
            
            print(f"📁 加载文件: {Path(file_path).name}")
            print(f"   - 编码: {encoding}")
            print(f"   - 原始记录数: {len(df)}")
            print(f"   - 字段数: {len(df.columns)}")
            
            # 验证必需字段
            missing_required = [field for field in self.required_params if field not in df.columns]
            missing_key = [field for field in self.key_fields if field not in df.columns]
            
            if missing_required:
                print(f"❌ 缺少必需钻井参数: {missing_required}")
                return None
                
            if missing_key:
                print(f"❌ 缺少关键字段: {missing_key}")
                return None
            
            # 数据清理
            df = self.clean_data(df)
            
            if df is None or len(df) == 0:
                print("❌ 数据清理后为空")
                return None
                
            print(f"   - 清理后记录数: {len(df)}")
            return df
            
        except Exception as e:
            print(f"❌ 加载文件失败 {file_path}: {str(e)}")
            return None
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清理
        """
        # 处理时间字段
        if 'date' not in df.columns:
            if 'WELLDATE' in df.columns and 'WELLTIME' in df.columns:
                df['date'] = pd.to_datetime(df['WELLDATE'].astype(str) + ' ' + df['WELLTIME'].astype(str))
            else:
                print("⚠️ 无法构建时间字段")
                return df
        else:
            df['date'] = pd.to_datetime(df['date'])
        
        # 按时间排序
        df = df.sort_values('date').reset_index(drop=True)
        
        # 清理工况字段
        if 'RIGSTA' in df.columns:
            df['RIGSTA'] = df['RIGSTA'].fillna('未知工况').astype(str).str.strip()
            # 过滤空工况
            df = df[df['RIGSTA'] != ''].reset_index(drop=True)
        
        # 清理数值字段
        for param in self.required_params + self.optional_params:
            if param in df.columns:
                df[param] = pd.to_numeric(df[param], errors='coerce')
        
        # 移除全为NaN的行
        df = df.dropna(subset=self.required_params, how='all').reset_index(drop=True)
        
        return df
    
    def extract_condition_segments(self, df: pd.DataFrame) -> Dict[str, List[pd.DataFrame]]:
        """
        按工况提取独立时间段
        
        关键策略：
        1. 先按工况分类
        2. 识别每个工况内的连续时间段
        3. 每个时间段独立处理，保持时间完整性
        """
        condition_segments = {}
        
        # 识别工况变化点
        df['condition_change'] = (df['RIGSTA'] != df['RIGSTA'].shift(1)) | (df['date'].diff() > timedelta(minutes=5))
        df['segment_id'] = df['condition_change'].cumsum()
        
        print(f"\n🔍 工况分析:")
        
        # 按工况和段ID分组
        for (condition, segment_id), group in df.groupby(['RIGSTA', 'segment_id']):
            # 清理辅助列
            segment = group.drop(['condition_change', 'segment_id'], axis=1).reset_index(drop=True)
            
            # 检查时间段长度
            if len(segment) >= self.min_records_per_segment:
                if condition not in condition_segments:
                    condition_segments[condition] = []
                
                condition_segments[condition].append(segment)
                
                # 计算时间跨度
                time_span = (segment['date'].iloc[-1] - segment['date'].iloc[0]).total_seconds() / 60
                print(f"  ✅ 工况 '{condition}' - 段 {len(condition_segments[condition])}: {len(segment)} 条记录, {time_span:.1f} 分钟")
            else:
                print(f"  ⚠️ 跳过工况 '{condition}' 短时间段: {len(segment)} 条记录")
        
        return condition_segments
    
    def create_time_windows(self, segment: pd.DataFrame) -> List[pd.DataFrame]:
        """
        对单个时间段创建3分钟时间窗口
        
        策略：滑动窗口，步长1分钟，确保充分利用数据
        """
        windows = []
        
        if len(segment) < self.min_records_per_window:
            return windows
        
        # 计算窗口参数
        window_size = self.min_records_per_window
        step_size = window_size // 3  # 1分钟步长
        
        # 滑动窗口切分
        for start_idx in range(0, len(segment) - window_size + 1, step_size):
            end_idx = start_idx + window_size
            window = segment.iloc[start_idx:end_idx].copy().reset_index(drop=True)
            
            # 验证时间窗口质量
            if self.validate_window(window):
                windows.append(window)
        
        return windows
    
    def validate_window(self, window: pd.DataFrame) -> bool:
        """
        验证时间窗口质量
        """
        # 检查数据完整性
        missing_ratio = window[self.required_params].isnull().sum().sum() / (len(window) * len(self.required_params))
        if missing_ratio > 0.1:  # 超过10%缺失
            return False
        
        # 检查时间连续性
        time_gaps = window['date'].diff().dt.total_seconds()
        max_gap = time_gaps.max()
        if max_gap > 60:  # 超过1分钟间隔
            return False
        
        return True
    
    def process_data_folder(self, folder_path: str, data_type: str) -> Dict[str, List[pd.DataFrame]]:
        """
        处理数据文件夹
        
        返回结构: {工况: [时间窗口列表]}
        """
        print(f"\n📂 处理 {data_type} 数据文件夹: {folder_path}")
        
        folder = Path(folder_path)
        if not folder.exists():
            print(f"❌ 文件夹不存在: {folder_path}")
            return {}
        
        all_condition_windows = {}
        csv_files = list(folder.glob("*.csv"))
        
        if not csv_files:
            print("❌ 未找到CSV文件")
            return {}
        
        print(f"📋 找到 {len(csv_files)} 个CSV文件")
        
        for file_path in csv_files:
            # 加载数据
            df = self.load_and_validate_data(str(file_path))
            if df is None:
                continue
            
            # 按工况提取时间段
            condition_segments = self.extract_condition_segments(df)
            
            # 对每个工况的每个时间段创建时间窗口
            for condition, segments in condition_segments.items():
                if condition not in all_condition_windows:
                    all_condition_windows[condition] = []
                
                for segment in segments:
                    windows = self.create_time_windows(segment)
                    all_condition_windows[condition].extend(windows)
                    print(f"    📊 工况 '{condition}' 生成 {len(windows)} 个时间窗口")
        
        # 统计结果
        print(f"\n📈 {data_type} 数据处理完成:")
        for condition, windows in all_condition_windows.items():
            print(f"  🔹 工况 '{condition}': {len(windows)} 个时间窗口")
        
        return all_condition_windows

    def create_training_dataset(self, normal_windows: Dict, anomaly_windows: Dict, output_dir: str):
        """
        创建训练数据集

        输出结构：
        output_dir/
        ├── condition1/
        │   ├── normal/
        │   │   ├── window_001.csv
        │   │   └── window_002.csv
        │   └── anomaly/
        │       ├── window_001.csv
        │       └── window_002.csv
        └── condition2/
            ├── normal/
            └── anomaly/
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"\n💾 创建训练数据集: {output_dir}")

        # 获取所有工况
        all_conditions = set(normal_windows.keys()) | set(anomaly_windows.keys())

        dataset_summary = {}

        for condition in all_conditions:
            condition_dir = output_path / condition
            normal_dir = condition_dir / "normal"
            anomaly_dir = condition_dir / "anomaly"

            normal_dir.mkdir(parents=True, exist_ok=True)
            anomaly_dir.mkdir(parents=True, exist_ok=True)

            # 保存正常数据窗口
            normal_count = 0
            if condition in normal_windows:
                for i, window in enumerate(normal_windows[condition]):
                    window_file = normal_dir / f"window_{i+1:03d}.csv"
                    window.to_csv(window_file, index=False, encoding='utf-8-sig')
                    normal_count += 1

            # 保存异常数据窗口
            anomaly_count = 0
            if condition in anomaly_windows:
                for i, window in enumerate(anomaly_windows[condition]):
                    window_file = anomaly_dir / f"window_{i+1:03d}.csv"
                    window.to_csv(window_file, index=False, encoding='utf-8-sig')
                    anomaly_count += 1

            # 统计信息
            total_windows = normal_count + anomaly_count
            anomaly_ratio = anomaly_count / total_windows if total_windows > 0 else 0

            dataset_summary[condition] = {
                'normal_windows': normal_count,
                'anomaly_windows': anomaly_count,
                'total_windows': total_windows,
                'anomaly_ratio': anomaly_ratio
            }

            print(f"  📁 工况 '{condition}':")
            print(f"     - 正常窗口: {normal_count}")
            print(f"     - 异常窗口: {anomaly_count}")
            print(f"     - 异常比例: {anomaly_ratio:.3f}")

        # 保存数据集摘要
        summary_file = output_path / "dataset_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_summary, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 数据集创建完成!")
        print(f"📊 数据集摘要已保存: {summary_file}")

        return dataset_summary

    def process_classified_data(self, normal_folder: str, anomaly_folder: str, output_dir: str):
        """
        处理已分类的数据

        主要流程：
        1. 处理正常数据文件夹
        2. 处理异常数据文件夹
        3. 按工况组织数据
        4. 创建训练数据集
        """
        print("🚀 开始处理已分类的钻井数据")
        print("=" * 60)

        # 处理正常数据
        normal_windows = self.process_data_folder(normal_folder, "正常")

        # 处理异常数据
        anomaly_windows = self.process_data_folder(anomaly_folder, "异常")

        # 创建训练数据集
        dataset_summary = self.create_training_dataset(normal_windows, anomaly_windows, output_dir)

        # 生成处理报告
        self.generate_processing_report(dataset_summary, output_dir)

        print("\n🎉 数据处理完成!")
        return dataset_summary

    def generate_processing_report(self, dataset_summary: Dict, output_dir: str):
        """
        生成处理报告
        """
        report_file = Path(output_dir) / "processing_report.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 钻井数据处理报告\n\n")
            f.write(f"**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 处理配置\n\n")
            f.write(f"- **时间窗口长度**: {self.window_minutes} 分钟\n")
            f.write(f"- **最小时间段长度**: {self.min_segment_minutes} 分钟\n")
            f.write(f"- **每窗口最小记录数**: {self.min_records_per_window}\n\n")

            f.write("## 数据集统计\n\n")
            f.write("| 工况 | 正常窗口 | 异常窗口 | 总窗口数 | 异常比例 |\n")
            f.write("|------|----------|----------|----------|----------|\n")

            total_normal = 0
            total_anomaly = 0

            for condition, stats in dataset_summary.items():
                f.write(f"| {condition} | {stats['normal_windows']} | {stats['anomaly_windows']} | {stats['total_windows']} | {stats['anomaly_ratio']:.3f} |\n")
                total_normal += stats['normal_windows']
                total_anomaly += stats['anomaly_windows']

            f.write(f"| **总计** | **{total_normal}** | **{total_anomaly}** | **{total_normal + total_anomaly}** | **{total_anomaly/(total_normal + total_anomaly):.3f}** |\n\n")

            f.write("## 处理策略说明\n\n")
            f.write("### 1. 工况分类优先\n")
            f.write("- 先按RIGSTA字段进行工况分类\n")
            f.write("- 识别每个工况内的连续时间段\n")
            f.write("- 避免不同工况数据的混合\n\n")

            f.write("### 2. 独立时间段处理\n")
            f.write("- 每个时间段独立处理，避免虚假连续性\n")
            f.write("- 时间间隔超过5分钟的数据被分为不同段\n")
            f.write("- 保持钻井作业的真实时间特征\n\n")

            f.write("### 3. 滑动窗口切分\n")
            f.write("- 3分钟时间窗口，1分钟步长\n")
            f.write("- 充分利用数据，增加训练样本\n")
            f.write("- 确保时间窗口的数据质量\n\n")

        print(f"📋 处理报告已生成: {report_file}")


def main():
    """
    主函数 - 演示优化的处理流程
    """
    # 初始化处理器
    processor = OptimizedDrillingProcessor()

    # 配置路径（请根据实际情况修改）
    normal_folder = "正常数据文件夹"  # 替换为实际路径
    anomaly_folder = "异常数据文件夹"  # 替换为实际路径
    output_dir = "optimized_training_data"

    # 检查路径是否存在
    if not Path(normal_folder).exists():
        print(f"❌ 正常数据文件夹不存在: {normal_folder}")
        print("请修改 normal_folder 路径")
        return

    if not Path(anomaly_folder).exists():
        print(f"❌ 异常数据文件夹不存在: {anomaly_folder}")
        print("请修改 anomaly_folder 路径")
        return

    # 执行处理
    try:
        dataset_summary = processor.process_classified_data(
            normal_folder=normal_folder,
            anomaly_folder=anomaly_folder,
            output_dir=output_dir
        )

        print(f"\n🎯 处理完成! 训练数据已保存到: {output_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

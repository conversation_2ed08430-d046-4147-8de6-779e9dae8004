# 多工况感知早期征兆检测系统 - 快速开始指南

## 🚀 快速开始

### 1. 数据质量验证（推荐第一步）

```bash
# 验证数据质量
python data_quality_validator.py
```

**预期输出:**
```
🔍 开始数据质量验证...
📁 发现 1234 个CSV文件
  处理进度: 0/1234
  处理进度: 100/1234
  ...
✅ 数据质量验证完成
  有效文件: 1100
  无效文件: 134
  有效率: 89.1%
```

### 2. 运行多工况训练

```bash
# 启动多工况感知训练
python run_multi_condition_earlysignaldet.py
```

**训练流程:**
1. 数据分布分析
2. 工况自动识别
3. 各工况专用模型训练
4. 模型验证和保存

### 3. 使用预测器

```python
from multi_condition_predictor import MultiConditionPredictor
import pandas as pd

# 加载预测器
predictor = MultiConditionPredictor.load_from_config(
    './checkpoints/multi_condition/predictor_config.json'
)

# 准备钻进数据
drilling_data = pd.DataFrame({
    'DEP': [1500, 1501, 1502],
    'WOB': [45, 50, 48],
    'RPM': [80, 85, 82],
    'HKLD': [600, 620, 610],
    # ... 其他参数
})

# 进行预测
result = predictor.predict(drilling_data)
print(f"预测工况: {result['condition']}")
print(f"卡钻风险: {result['risk_score']:.3f}")
print(f"预警级别: {result['alert_level']}")
```

## 📊 数据要求详解

### 最小样本要求

| 工况 | 最小样本数 | 推荐样本数 | 正样本比例 |
|------|------------|------------|------------|
| 起钻 | 500 | 1000+ | 15% |
| 下钻 | 500 | 1000+ | 15% |
| 正常钻进 | 1000 | 2000+ | 30% |
| 正划眼 | 300 | 600+ | 25% |
| 倒划眼 | 200 | 400+ | 20% |

### 数据质量标准

```python
QUALITY_REQUIREMENTS = {
    'min_sequence_length': 50,      # 最小序列长度
    'max_sequence_length': 500,     # 最大序列长度
    'missing_value_threshold': 0.1, # 缺失值比例 < 10%
    'required_features': [          # 必需特征
        'DEP', 'BITDEP', 'WOB', 'HKLD', 
        'RPM', 'TOR', 'SPP'
    ],
    'sampling_frequency': '5S',     # 推荐采样频率
    'feature_completeness': 0.9     # 特征完整性 > 90%
}
```

## 🔧 配置参数说明

### 工况专用模型配置

```python
CONDITION_MODEL_CONFIGS = {
    '起钻': {
        'train_epochs': 80,     # 训练轮数
        'batch_size': 16,       # 批次大小
        'learning_rate': 0.001, # 学习率
        'd_model': 128,         # 模型维度
        'e_layers': 3,          # 编码器层数
        'patch_len': 16,        # 补丁长度
        'stride': 8             # 步长
    },
    # ... 其他工况配置
}
```

### 工况识别规则

| 工况 | WOB | RPM | 深度变化 | HKLD | 识别逻辑 |
|------|-----|-----|----------|------|----------|
| 起钻 | <20 | 任意 | 递减 | >800 | 提升钻具 |
| 下钻 | <20 | 任意 | 递增 | 400-800 | 下放钻具 |
| 正常钻进 | >40 | >50 | 明显变化 | 任意 | 破岩钻进 |
| 正划眼 | 20-60 | 30-80 | 变化小 | 任意 | 井眼修整 |
| 倒划眼 | 10-40 | 20-60 | 变化小 | 任意 | 其他操作 |

## 📈 性能评估

### 主要指标

```python
EVALUATION_METRICS = {
    'drilling_risk_accuracy': '>90%',    # 卡钻预测准确率
    'drilling_risk_precision': '>85%',   # 精确率
    'drilling_risk_recall': '>80%',      # 召回率
    'condition_identification': '>85%',   # 工况识别准确率
    'false_positive_rate': '<10%',       # 误报率
    'early_warning_time': '>300s'       # 提前预警时间
}
```

### 分工况性能监控

每个工况模型都有独立的性能指标：
- 训练损失曲线
- 验证准确率
- 混淆矩阵
- ROC曲线

## 🔄 完整工作流程

### 训练阶段

```mermaid
graph TD
    A[原始CSV数据] --> B[数据质量验证]
    B --> C[工况自动识别]
    C --> D[数据预处理]
    D --> E[按工况分组]
    E --> F[训练专用模型]
    F --> G[模型验证]
    G --> H[保存模型]
```

### 预测阶段

```mermaid
graph TD
    A[实时钻进数据] --> B[数据预处理]
    B --> C[工况识别]
    C --> D[选择专用模型]
    D --> E[风险预测]
    E --> F[预警决策]
    F --> G[输出结果]
```

## 🛠️ 故障排除

### 常见问题

**1. 数据质量验证失败**
```bash
# 检查数据路径
ls -la ./dataset/earlysignaldetection/

# 检查文件编码
file ./dataset/earlysignaldetection/sample.csv
```

**2. 工况样本数量不足**
```python
# 查看数据分布
python -c "
from run_multi_condition_earlysignaldet import MultiConditionWorkflowManager
manager = MultiConditionWorkflowManager()
manager.analyze_data_distribution()
"
```

**3. 模型训练失败**
- 检查GPU内存是否充足
- 降低batch_size参数
- 检查数据路径是否正确

**4. 预测器加载失败**
```python
# 检查配置文件
import json
with open('./checkpoints/multi_condition/predictor_config.json') as f:
    config = json.load(f)
    print(json.dumps(config, indent=2))
```

### 性能优化建议

**1. 数据层面**
- 增加数据增强技术
- 平衡各工况样本数量
- 提高数据质量

**2. 模型层面**
- 调整超参数
- 使用学习率调度
- 实施早停机制

**3. 系统层面**
- 使用GPU加速
- 批量预测
- 模型并行

## 📝 日志和监控

### 训练日志
```
./logs/multi_condition/training_summary_20240629_143022.json
./logs/data_quality_report.txt
```

### 模型文件
```
./checkpoints/multi_condition/
├── 起钻/
├── 下钻/
├── 正常钻进/
├── 正划眼/
├── 倒划眼/
└── predictor_config.json
```

## 🔮 预测结果格式

```json
{
    "condition": "正常钻进",
    "condition_confidence": 0.85,
    "risk_score": 0.65,
    "alert_level": "MEDIUM",
    "timestamp": "2024-06-29T14:30:22",
    "data_points": 100
}
```

### 预警级别说明

| 级别 | 风险分数范围 | 建议行动 |
|------|--------------|----------|
| NORMAL | 0.0 - 0.3 | 正常监控 |
| LOW | 0.3 - 0.6 | 注意观察 |
| MEDIUM | 0.6 - 0.8 | 加强监控 |
| HIGH | 0.8 - 1.0 | 立即处理 |

## 📞 技术支持

如遇到问题，请提供：
1. 错误日志
2. 数据样本
3. 配置参数
4. 运行环境信息

## 🔄 版本更新

### v1.0.0 (当前版本)
- 基础多工况感知功能
- 五种工况自动识别
- PatchTST模型集成
- 数据质量验证

### 计划功能
- 在线学习能力
- 模型自动调优
- 实时监控面板
- 专家反馈集成

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解决中文乱码问题并处理实际钻进数据
专门处理包含中文工况标签的钻进数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
from advanced_data_preprocessor import AdvancedDataPreprocessor

def load_with_correct_encoding(file_path: str) -> pd.DataFrame:
    """
    使用正确编码加载CSV文件，解决中文乱码问题
    """
    print(f"🔍 正在加载数据文件: {file_path}")
    
    # 尝试多种编码方式，优先使用中文编码
    encodings = ['gbk', 'gb2312', 'utf-8', 'utf-8-sig', 'cp936', 'latin1']
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"✅ 成功使用 {encoding} 编码读取数据")
            print(f"📊 数据形状: {df.shape}")
            
            # 检查是否有中文字符正确显示
            if 'CW' in df.columns:
                sample_conditions = df['CW'].dropna().unique()[:5]
                print(f"📋 工况标签示例: {sample_conditions}")
                
                # 检查是否包含中文字符
                has_chinese = any('钻' in str(cond) or '起' in str(cond) or '下' in str(cond) 
                                for cond in sample_conditions if pd.notna(cond))
                if has_chinese:
                    print(f"✅ 中文字符显示正常")
                else:
                    print(f"⚠️ 可能仍有编码问题，但继续处理")
            
            return df
            
        except UnicodeDecodeError as e:
            print(f"❌ {encoding} 编码失败: 尝试下一种编码")
            continue
        except Exception as e:
            print(f"❌ 读取失败 ({encoding}): {str(e)}")
            continue
    
    raise ValueError("❌ 无法使用任何编码方式读取文件")

def analyze_chinese_conditions(df: pd.DataFrame, condition_column: str = 'CW') -> dict:
    """
    分析中文工况标签分布
    """
    print(f"\n🔍 分析工况标签分布...")
    
    if condition_column not in df.columns:
        print(f"❌ 未找到工况列: {condition_column}")
        print(f"可用列: {list(df.columns)}")
        return {}
    
    # 获取所有唯一的工况标签
    unique_conditions = df[condition_column].dropna().unique()
    condition_counts = df[condition_column].value_counts()
    
    print(f"📊 发现 {len(unique_conditions)} 种工况标签:")
    for condition, count in condition_counts.items():
        if pd.notna(condition):
            percentage = (count / len(df)) * 100
            print(f"  - '{condition}': {count} 次 ({percentage:.1f}%)")
    
    # 分析工况切换频率
    condition_changes = (df[condition_column] != df[condition_column].shift()).sum()
    avg_duration = len(df) / condition_changes if condition_changes > 0 else len(df)
    
    print(f"\n📈 工况切换分析:")
    print(f"  - 总切换次数: {condition_changes}")
    print(f"  - 平均持续时长: {avg_duration:.1f} 个数据点")
    
    return {
        'unique_conditions': [str(c) for c in unique_conditions if pd.notna(c)],
        'condition_counts': {str(k): v for k, v in condition_counts.items() if pd.notna(k)},
        'total_records': len(df),
        'condition_changes': condition_changes,
        'avg_duration': avg_duration
    }

def explain_positive_negative_samples():
    """
    详细解释正样本和负样本的含义
    """
    print("\n" + "="*70)
    print("📚 正样本和负样本详细说明")
    print("="*70)
    
    print("""
🎯 在钻井卡钻早期预警系统中：

📈 正样本 (Positive Samples) - 目标事件样本:
   ┌─────────────────────────────────────────────────────────────┐
   │ • 定义: 包含我们要预测的目标工况的数据段                      │
   │ • 在卡钻预警中: 通常是"坐卡"、"卡钻"等异常工况               │
   │ • 特征: 显示了卡钻发生前后的参数变化模式                     │
   │ • 价值: 让模型学习识别卡钻的特征信号                         │
   │ • 例子: 标记为"坐卡"的时间段数据                             │
   └─────────────────────────────────────────────────────────────┘

📉 负样本 (Negative Samples) - 正常操作样本:
   ┌─────────────────────────────────────────────────────────────┐
   │ • 定义: 不包含目标工况的正常操作数据段                       │
   │ • 在卡钻预警中: "起钻"、"下钻"、"正常钻进"等正常工况         │
   │ • 特征: 显示正常钻井操作的参数模式                           │
   │ • 价值: 帮助模型区分正常操作和异常情况                       │
   │ • 例子: 标记为"起钻"、"下钻"、"正常钻进"的数据段             │
   └─────────────────────────────────────────────────────────────┘

⚖️ 样本平衡策略:
   • 正负样本比例: 通常 1:3 到 1:4 (正:负)
   • 为什么需要平衡: 
     - 卡钻是罕见事件，负样本天然更多
     - 不平衡会导致模型偏向预测"正常"
     - 平衡后模型更敏感，能更好检测异常

🔧 在您的数据中的应用:
   • 如果预测目标是"坐卡": 
     - 正样本: 包含"坐卡"标签的数据段
     - 负样本: "起钻"、"下钻"、"正常钻进"等其他工况
   
   • 系统自动处理:
     - 识别各种工况标签
     - 按目标工况分离正负样本
     - 自动平衡样本比例
     - 确保训练数据质量

📊 实际效果:
   • 正样本教会模型: "这就是卡钻的样子"
   • 负样本教会模型: "这些都是正常操作"
   • 平衡比例确保: 模型既不过敏也不迟钝
    """)

def standardize_chinese_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    标准化包含中文标签的钻进数据格式
    """
    print(f"\n🔧 标准化数据格式...")
    
    # 列名映射表
    column_mapping = {
        'DEP': 'DEP',           # 井深
        'BITDEP': 'BITDEP',     # 钻头深度
        'HOOKLD': 'HKLD',       # 大钩载荷
        'HKLD': 'HKLD',         # 大钩载荷
        'DRTIME': 'timestamp',   # 时间
        'WOB': 'WOB',           # 钻压
        'RPM': 'RPM',           # 转速
        'TOR': 'TOR',           # 扭矩
        'SPP': 'SPP',           # 立管压力
        'CW': 'condition',       # 工况
        'RIGSTA': 'RIGSTA',     # 钻机状态
        'date': 'timestamp'      # 时间（备选）
    }
    
    # 重命名列
    df_renamed = df.rename(columns=column_mapping)
    
    # 添加缺失的必要列
    if 'CSIP' not in df_renamed.columns:
        df_renamed['CSIP'] = 0
    
    # 处理时间列
    if 'timestamp' not in df_renamed.columns:
        # 创建虚拟时间戳（每5秒一个数据点）
        df_renamed['timestamp'] = pd.date_range(
            start='2022-06-23 18:24:00', 
            periods=len(df_renamed), 
            freq='5S'
        )
    
    print(f"✅ 数据格式标准化完成")
    print(f"📊 标准化后列名: {list(df_renamed.columns)}")
    
    return df_renamed

def process_chinese_drilling_data(input_file: str, target_condition: str = "坐卡", 
                                output_dir: str = "processed_chinese_data"):
    """
    处理包含中文标签的实际钻进数据
    """
    print("🎯 开始处理包含中文标签的钻进数据")
    print("=" * 60)
    
    try:
        # 1. 使用正确编码加载数据
        df = load_with_correct_encoding(input_file)
        
        # 2. 分析中文工况标签
        condition_analysis = analyze_chinese_conditions(df)
        
        # 3. 标准化数据格式
        df_standard = standardize_chinese_data(df)
        
        # 4. 保存标准化数据
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        standard_file = output_path / "standardized_chinese_data.csv"
        df_standard.to_csv(standard_file, index=False, encoding='utf-8-sig')
        print(f"✅ 标准化数据已保存: {standard_file}")
        
        # 5. 配置预处理参数（针对频繁切换的工况）
        config = {
            'min_segment_length': 20,      # 较短的最小长度，适应频繁切换
            'max_segment_length': 100,     # 较短的最大长度
            'min_quality_score': 0.5,      # 较低的质量阈值，保留更多样本
            'transition_buffer': 2,        # 较小的缓冲区
        }
        
        print(f"\n🚀 开始预处理（针对频繁工况切换优化）...")
        preprocessor = AdvancedDataPreprocessor(config)
        result = preprocessor.process_single_file(str(standard_file))
        
        # 6. 分析和保存结果
        if result['status'] == 'success':
            print(f"✅ 预处理成功!")
            print(f"📊 生成段数: {len(result['segments'])}")
            
            # 按工况分组
            condition_groups = {}
            for segment in result['segments']:
                condition = segment['condition']
                if condition not in condition_groups:
                    condition_groups[condition] = []
                condition_groups[condition].append(segment)
            
            print(f"\n📋 预处理后工况分布:")
            for condition, segments in condition_groups.items():
                avg_quality = np.mean([s['quality_score'] for s in segments])
                print(f"  - {condition}: {len(segments)} 个段 (平均质量: {avg_quality:.3f})")
            
            # 7. 创建训练样本
            print(f"\n📊 创建训练样本 (目标工况: {target_condition})...")
            all_segments = result['segments']
            training_samples = preprocessor.create_training_samples(
                all_segments, target_condition, positive_ratio=0.25
            )
            
            print(f"训练样本统计:")
            print(f"  - 正样本数: {training_samples['sample_info']['n_positive']}")
            print(f"  - 负样本数: {training_samples['sample_info']['n_negative']}")
            print(f"  - 正样本比例: {training_samples['sample_info']['positive_ratio']:.3f}")
            print(f"  - 正样本平均质量: {training_samples['sample_info']['avg_positive_quality']:.3f}")
            print(f"  - 负样本平均质量: {training_samples['sample_info']['avg_negative_quality']:.3f}")
            
            # 8. 保存训练样本
            samples_dir = output_path / "training_samples"
            samples_dir.mkdir(exist_ok=True)
            
            # 保存正样本
            positive_dir = samples_dir / "positive"
            positive_dir.mkdir(exist_ok=True)
            for i, sample in enumerate(training_samples['positive_samples']):
                sample_file = positive_dir / f"positive_{i:04d}_quality_{sample['quality_score']:.3f}.csv"
                sample['data'].to_csv(sample_file, index=False, encoding='utf-8-sig')
            
            # 保存负样本
            negative_dir = samples_dir / "negative"
            negative_dir.mkdir(exist_ok=True)
            for i, sample in enumerate(training_samples['negative_samples']):
                sample_file = negative_dir / f"negative_{i:04d}_quality_{sample['quality_score']:.3f}.csv"
                sample['data'].to_csv(sample_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 训练样本已保存到: {samples_dir}")
        
        # 9. 生成详细报告
        report = {
            'processing_time': datetime.now().isoformat(),
            'input_file': input_file,
            'target_condition': target_condition,
            'original_data': {
                'shape': df.shape,
                'condition_analysis': condition_analysis
            },
            'preprocessing_config': config,
            'results': {
                'status': result['status'],
                'total_segments': len(result['segments']) if result['status'] == 'success' else 0,
                'training_samples': training_samples['sample_info'] if result['status'] == 'success' else {}
            }
        }
        
        report_file = output_path / "chinese_data_processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        print("🎉 中文数据处理完成!")
        
        return report
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 首先解释正负样本含义
    explain_positive_negative_samples()
    
    # 使用示例
    print("\n" + "="*60)
    print("🚀 使用示例")
    print("="*60)
    
    # 您需要修改这里的文件路径
    input_file = "your_chinese_drilling_data.csv"  # 替换为您的实际文件路径
    target_condition = "坐卡"  # 您要预测的目标工况
    
    print(f"📁 输入文件: {input_file}")
    print(f"🎯 目标工况: {target_condition}")
    
    if Path(input_file).exists():
        result = process_chinese_drilling_data(input_file, target_condition)
        if result:
            print("\n✅ 处理完成！您现在可以使用生成的训练样本训练模型了。")
    else:
        print(f"\n⚠️ 请将您的数据文件路径替换到 input_file 变量中")
        print(f"当前设置的文件: {input_file}")
        print(f"\n💡 使用方法:")
        print(f"1. 将您的CSV文件路径替换到 input_file")
        print(f"2. 确认目标工况名称 (target_condition)")
        print(f"3. 运行脚本: python fix_encoding_and_process.py")

# 钻井卡钻预警数据处理的正确流程

## 🎯 您的实际情况

✅ **已经完成的工作**：
- 您已经根据专业经验人工挑选分类好了数据
- 正常数据文件夹：包含正常钻井操作的数据文件
- 征兆数据文件夹：包含卡钻征兆的数据文件

❌ **之前的误解**：
- 误以为需要人工标注环节
- 误以为要基于工况标签判断正负样本
- 忽略了您已经完成了最关键的分类工作

## 🔧 正确的处理流程

### 1️⃣ 数据预处理
```python
# 处理中文编码问题
df = load_data_with_encoding(file_path)

# 标准化列名
df_std = standardize_columns(df)

# 数据质量检查
quality_score = calculate_segment_quality(df_std)
```

### 2️⃣ 按工况分类
```python
# 对正常数据按工况分类
normal_segments = extract_condition_segments(normal_data)
# 结果：{'起钻': [...], '下钻': [...], '正常钻进': [...]}

# 对征兆数据按工况分类  
anomaly_segments = extract_condition_segments(anomaly_data)
# 结果：{'起钻': [...], '下钻': [...], '正常钻进': [...]}
```

### 3️⃣ 创建滑动窗口
```python
# 从每个工况的数据段创建滑动窗口
for condition, segments in normal_segments.items():
    for segment in segments:
        windows = create_sliding_windows(segment, window_size=100, step_size=20)
```

### 4️⃣ 生成训练数据集
```python
# 每个工况都有正常样本和异常样本
training_dataset/
├── 起钻/
│   ├── normal/     # 起钻工况的正常样本
│   └── anomaly/    # 起钻工况的异常样本
├── 下钻/
│   ├── normal/     # 下钻工况的正常样本
│   └── anomaly/    # 下钻工况的异常样本
└── 正常钻进/
    ├── normal/     # 正常钻进工况的正常样本
    └── anomaly/    # 正常钻进工况的异常样本
```

### 5️⃣ 模型训练
```python
# 可以为每个工况单独训练模型
python run_earlysignaldet.py \
    --data_path training_dataset/起钻/ \
    --model PatchTST \
    --task_name earlysignaldet

# 或者多工况联合训练
python run_earlysignaldet.py \
    --data_path training_dataset/ \
    --model PatchTST \
    --task_name earlysignaldet
```

## 📁 使用方法

### 步骤1：准备数据
```
您的数据结构：
正常数据/
├── normal_file_1.csv
├── normal_file_2.csv
└── ...

征兆数据/
├── anomaly_file_1.csv
├── anomaly_file_2.csv
└── ...
```

### 步骤2：修改配置
在 `process_classified_data.py` 中修改路径：
```python
# 数据路径（请根据实际情况修改）
normal_data_folder = "您的正常数据文件夹路径"
anomaly_data_folder = "您的征兆数据文件夹路径"
output_folder = "training_dataset"
```

### 步骤3：运行处理
```bash
python process_classified_data.py
```

### 步骤4：训练模型
```bash
# 使用生成的数据训练PatchTST模型
python run_earlysignaldet.py \
    --data_path training_dataset/起钻/ \
    --model PatchTST \
    --batch_size 16 \
    --train_epochs 100
```

## 💡 关键优势

### ✅ 利用专业经验
- 直接使用您的人工分类结果
- 避免了复杂的自动标注过程
- 保证了数据质量和准确性

### ✅ 按工况细分
- 每个工况都有专门的正负样本
- 可以训练针对性更强的模型
- 提高预警的准确性

### ✅ 标准化流程
- 自动处理中文编码问题
- 统一数据格式和列名
- 生成标准的训练数据集

### ✅ 灵活的训练策略
- 可以单工况训练
- 可以多工况联合训练
- 支持不同的模型架构

## 🚫 不需要的环节

❌ **人工标注**：您已经分类好了数据
❌ **基于工况标签判断**：工况标签只用于分类，不用于判断正负样本
❌ **复杂的异常检测**：直接使用您的分类结果

## 📊 预期结果

处理完成后，您将得到：

```
training_dataset/
├── 起钻/
│   ├── normal/          # 起钻工况正常样本
│   │   ├── normal_0000.csv
│   │   ├── normal_0001.csv
│   │   └── ...
│   └── anomaly/         # 起钻工况异常样本
│       ├── anomaly_0000.csv
│       ├── anomaly_0001.csv
│       └── ...
├── 下钻/
│   ├── normal/          # 下钻工况正常样本
│   └── anomaly/         # 下钻工况异常样本
├── 正常钻进/
│   ├── normal/          # 正常钻进工况正常样本
│   └── anomaly/         # 正常钻进工况异常样本
├── dataset_info.json    # 数据集统计信息
└── usage_instructions.md # 使用说明
```

## 🎯 核心理念

**您的专业经验 + 按工况分类 + 标准化处理 = 高质量训练数据**

这个流程：
1. **尊重您的专业判断**：直接使用您分类好的数据
2. **提高模型针对性**：按工况细分训练数据
3. **简化处理流程**：去除不必要的标注环节
4. **标准化输出**：生成符合PatchTST要求的数据格式

## 🚀 立即开始

1. 下载 `process_classified_data.py`
2. 修改其中的文件夹路径
3. 运行脚本处理您的数据
4. 使用生成的数据训练模型

这就是适合您实际情况的正确数据处理流程！

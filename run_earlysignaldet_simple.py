#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
早期信号检测模型简化运行脚本 - 硬编码配置版本
直接在代码中配置所有参数，无需命令行参数

作者: TSlib项目组
日期: 2024-06-29
"""

import sys
from pathlib import Path
from types import SimpleNamespace

# ============================================================================
# 🔧 主要配置参数 - 在这里修改您的实验设置
# ============================================================================
#
# 📝 使用说明:
# 1. 直接修改下面的参数值来配置您的实验
# 2. 修改后保存文件，然后运行: python run_earlysignaldet_simple.py
# 3. 无需任何命令行参数，所有配置都在代码中完成
#
# 🎯 常用配置组合:
# - 快速测试: TRAIN_EPOCHS=5, BATCH_SIZE=8
# - 完整训练: TRAIN_EPOCHS=100, BATCH_SIZE=16
# - 仅预测: IS_TRAINING=0, DO_PREDICT=True
# ============================================================================

# 🎯 核心训练参数
TRAIN_EPOCHS = 100          # 训练轮数 (建议: 5-200)
BATCH_SIZE = 16             # 批次大小 (建议: 8-32)
LEARNING_RATE = 0.001       # 学习率 (建议: 0.0001-0.01)
D_MODEL = 128               # 模型维度 (建议: 64-512)
E_LAYERS = 3                # 编码器层数 (建议: 1-6)
PATIENCE = 10               # 早停耐心值 (建议: 5-20)

# 🚀 运行模式配置
IS_TRAINING = 1             # 是否训练模式 (1=训练, 0=仅测试)
DO_PREDICT = True           # 是否进行预测 (True/False)

# 📊 模型配置
MODEL_TYPE = 'PatchTST'     # 模型类型 (PatchTST/TimesNet/Autoformer等)
TASK_NAME = 'earlysignaldet' # 任务名称 (固定值，请勿修改)
DATA_NAME = 'Earlysignaldet' # 数据集名称 (固定值，请勿修改)

# 🔧 高级参数 (通常不需要修改)
DROPOUT = 0.1               # Dropout率 (建议: 0.0-0.3)
N_HEADS = 8                 # 注意力头数 (建议: 4-16)
D_FF = 256                  # 前馈网络维度 (建议: 128-1024)
PATCH_LEN = 16              # PatchTST补丁长度 (建议: 8-32)
STRIDE = 8                  # PatchTST步长 (建议: 4-16)

# ============================================================================

def create_args_for_earlysignaldet():
    """创建早期信号检测的参数配置"""

    # 创建参数对象，使用硬编码的配置值
    args = SimpleNamespace()

    # 基础配置 - 使用顶部定义的配置常量
    args.task_name = TASK_NAME
    args.is_training = IS_TRAINING
    args.model_id = 'earlysignaldetection'
    args.model = MODEL_TYPE

    # 数据配置
    args.data = DATA_NAME
    args.root_path = './dataset/earlysignaldetection'
    args.data_path = ''
    args.features = 'M'
    args.target = 'label'
    args.freq = 'h'
    args.checkpoints = './checkpoints/'

    # 序列配置
    args.seq_len = 96
    args.label_len = 48
    args.pred_len = 24
    args.seasonal_patterns = 'Monthly'
    args.inverse = False
    
    # 模型配置 - 使用顶部定义的配置常量
    args.e_layers = E_LAYERS
    args.d_layers = 1
    args.d_model = D_MODEL
    args.d_ff = D_FF
    args.n_heads = N_HEADS
    args.factor = 1
    args.enc_in = 10
    args.dec_in = 10
    args.c_out = 2
    args.num_class = 2

    # 训练配置 - 使用顶部定义的配置常量
    args.batch_size = BATCH_SIZE
    args.learning_rate = LEARNING_RATE
    args.train_epochs = TRAIN_EPOCHS
    args.patience = PATIENCE
    args.dropout = DROPOUT
    
    # PatchTST特有参数 - 使用顶部定义的配置常量
    args.patch_len = PATCH_LEN
    args.stride = STRIDE
    args.fc_dropout = 0.1
    args.head_dropout = 0.0
    args.padding_patch = 'end'
    args.revin = 1
    args.affine = 0
    args.subtract_last = 0
    args.decomposition = 0
    args.kernel_size = 25
    args.individual = 0
    
    # 其他配置
    args.embed = 'timeF'
    args.distil = True
    args.mix = True
    args.activation = 'gelu'
    args.output_attention = False
    args.use_amp = False
    args.lradj = 'type1'

    # print_args.py需要的参数
    args.num_kernels = 6
    args.moving_avg = 25
    args.num_workers = 10
    args.loss = 'MSE'

    # 异常检测任务参数
    args.anomaly_ratio = 0.25

    # 填充任务参数
    args.mask_rate = 0.25
    
    # 设备配置
    args.use_gpu = True
    args.gpu = 0
    args.use_multi_gpu = False
    args.devices = '0,1,2,3'

    # 实验配置
    args.des = 'Exp'
    args.itr = 1
    args.top_k = 3

    # de-stationary projector 参数
    args.p_hidden_dims = [128, 128]
    args.p_hidden_layers = 2

    # 预测配置 - 使用顶部定义的配置常量
    args.do_predict = DO_PREDICT

    return args

def main():
    """主函数"""
    print("🚀 早期信号检测模型 - 硬编码配置版启动器")
    print("="*60)

    # 显示当前配置
    print("📋 当前配置参数:")
    print(f"  训练轮数: {TRAIN_EPOCHS}")
    print(f"  批次大小: {BATCH_SIZE}")
    print(f"  学习率: {LEARNING_RATE}")
    print(f"  模型维度: {D_MODEL}")
    print(f"  编码器层数: {E_LAYERS}")
    print(f"  是否训练: {'是' if IS_TRAINING else '否'}")
    print(f"  是否预测: {'是' if DO_PREDICT else '否'}")
    print(f"  模型类型: {MODEL_TYPE}")
    print()

    # 检查数据路径
    data_path = Path('./dataset/earlysignaldetection')
    if not data_path.exists():
        print(f"❌ 错误: 数据路径不存在 {data_path.absolute()}")
        print("请确保数据集位于正确的路径下")
        return False

    # 创建参数配置
    args = create_args_for_earlysignaldet()
    
    # 配置已在顶部显示，这里不再重复打印
    
    try:
        print("\n📦 导入运行模块...")
        
        # 导入必要的模块
        import torch
        import random
        import numpy as np
        from exp.exp_earlysignaldet import Exp_Earlysignaldet
        from utils.print_args import print_args
        
        print("✅ 模块导入成功")
        
        # 设置随机种子
        fix_seed = 2021
        random.seed(fix_seed)
        torch.manual_seed(fix_seed)
        np.random.seed(fix_seed)
        
        # 设置GPU
        args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False
        
        if args.use_gpu and args.use_multi_gpu:
            args.devices = args.devices.replace(' ', '')
            device_ids = args.devices.split(',')
            args.device_ids = [int(id_) for id_ in device_ids]
            args.gpu = args.device_ids[0]
        
        print('\n📊 实验参数:')
        print_args(args)
        
        # 创建实验对象
        Exp = Exp_Earlysignaldet
        
        print("\n🚀 开始运行早期信号检测模型...")
        print("-" * 60)
        
        # 执行训练或测试
        if args.is_training:
            for ii in range(args.itr):
                exp = Exp(args)
                setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                    args.task_name, args.model_id, args.model, args.data, args.features,
                    args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                    args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                    args.distil, args.des, ii)
                
                print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
                exp.train(setting)
                
                print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                exp.test(setting)
                
                if args.do_predict:
                    print('>>>>>>>predicting : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                    exp.predict(setting, True)
                
                torch.cuda.empty_cache()
        else:
            ii = 0
            setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                args.task_name, args.model_id, args.model, args.data, args.features,
                args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                args.distil, args.des, ii)
            
            exp = Exp(args)
            print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
            exp.test(setting, test=1)
            torch.cuda.empty_cache()
        
        print("-" * 60)
        print("✅ 模型运行完成!")
        return True
        
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

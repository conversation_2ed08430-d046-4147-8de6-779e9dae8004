#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
早期信号检测模型简化运行脚本
直接调用原始run.py的逻辑，避免复杂的模块导入问题

作者: TSlib项目组
日期: 2024-06-29
"""

import sys
import argparse
from pathlib import Path

def create_args_for_earlysignaldet():
    """创建早期信号检测的参数配置"""
    
    # 创建参数解析器（模拟run.py的参数）
    parser = argparse.ArgumentParser(description='Early Signal Detection')
    
    # 基础配置
    parser.add_argument('--task_name', type=str, default='earlysignaldet')
    parser.add_argument('--is_training', type=int, default=1)
    parser.add_argument('--model_id', type=str, default='earlysignaldetection')
    parser.add_argument('--model', type=str, default='PatchTST')
    
    # 数据配置
    parser.add_argument('--data', type=str, default='Earlysignaldet')
    parser.add_argument('--root_path', type=str, default='./dataset/earlysignaldetection')
    parser.add_argument('--data_path', type=str, default='')
    parser.add_argument('--features', type=str, default='M')
    parser.add_argument('--target', type=str, default='label')
    parser.add_argument('--freq', type=str, default='h')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/')
    
    # 序列配置
    parser.add_argument('--seq_len', type=int, default=96)
    parser.add_argument('--label_len', type=int, default=48)
    parser.add_argument('--pred_len', type=int, default=24)
    parser.add_argument('--seasonal_patterns', type=str, default='Monthly')
    parser.add_argument('--inverse', action='store_true', default=False)
    
    # 模型配置
    parser.add_argument('--e_layers', type=int, default=3)
    parser.add_argument('--d_layers', type=int, default=1)
    parser.add_argument('--d_model', type=int, default=128)
    parser.add_argument('--d_ff', type=int, default=256)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--factor', type=int, default=1)
    parser.add_argument('--enc_in', type=int, default=10)
    parser.add_argument('--dec_in', type=int, default=10)
    parser.add_argument('--c_out', type=int, default=2)
    parser.add_argument('--num_class', type=int, default=2)
    
    # 训练配置
    parser.add_argument('--batch_size', type=int, default=16)
    parser.add_argument('--learning_rate', type=float, default=0.001)
    parser.add_argument('--train_epochs', type=int, default=100)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--dropout', type=float, default=0.1)
    
    # PatchTST特有参数
    parser.add_argument('--patch_len', type=int, default=16)
    parser.add_argument('--stride', type=int, default=8)
    parser.add_argument('--fc_dropout', type=float, default=0.1)
    parser.add_argument('--head_dropout', type=float, default=0.0)
    parser.add_argument('--padding_patch', type=str, default='end')
    parser.add_argument('--revin', type=int, default=1)
    parser.add_argument('--affine', type=int, default=0)
    parser.add_argument('--subtract_last', type=int, default=0)
    parser.add_argument('--decomposition', type=int, default=0)
    parser.add_argument('--kernel_size', type=int, default=25)
    parser.add_argument('--individual', type=int, default=0)
    
    # 其他配置
    parser.add_argument('--embed', type=str, default='timeF')
    parser.add_argument('--distil', action='store_true', default=True)
    parser.add_argument('--mix', action='store_true', default=True)
    parser.add_argument('--activation', type=str, default='gelu')
    parser.add_argument('--output_attention', action='store_true', default=False)
    parser.add_argument('--use_amp', action='store_true', default=False)
    parser.add_argument('--lradj', type=str, default='type1')
    
    # 设备配置
    parser.add_argument('--use_gpu', type=bool, default=True)
    parser.add_argument('--gpu', type=int, default=0)
    parser.add_argument('--use_multi_gpu', action='store_true', default=False)
    parser.add_argument('--devices', type=str, default='0,1,2,3')
    
    # 实验配置
    parser.add_argument('--des', type=str, default='Exp')
    parser.add_argument('--itr', type=int, default=1)
    parser.add_argument('--top_k', type=int, default=3)
    
    # 预测配置
    parser.add_argument('--do_predict', action='store_true', default=False)
    
    return parser

def main():
    """主函数"""
    print("🚀 早期信号检测模型 - 简化版启动器")
    print("="*60)
    
    # 检查数据路径
    data_path = Path('./dataset/earlysignaldetection')
    if not data_path.exists():
        print(f"❌ 错误: 数据路径不存在 {data_path.absolute()}")
        print("请确保数据集位于正确的路径下")
        return False
    
    # 创建参数解析器
    parser = create_args_for_earlysignaldet()
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 打印配置
    print("\n📋 当前配置:")
    print(f"  任务类型: {args.task_name}")
    print(f"  模型类型: {args.model}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  学习率: {args.learning_rate}")
    print(f"  训练轮数: {args.train_epochs}")
    print(f"  模型维度: {args.d_model}")
    print(f"  编码器层数: {args.e_layers}")
    print(f"  是否预测: {args.do_predict}")
    
    try:
        print("\n📦 导入运行模块...")
        
        # 导入必要的模块
        import torch
        import random
        import numpy as np
        from exp.exp_earlysignaldet import Exp_Earlysignaldet
        from utils.print_args import print_args
        
        print("✅ 模块导入成功")
        
        # 设置随机种子
        fix_seed = 2021
        random.seed(fix_seed)
        torch.manual_seed(fix_seed)
        np.random.seed(fix_seed)
        
        # 设置GPU
        args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False
        
        if args.use_gpu and args.use_multi_gpu:
            args.devices = args.devices.replace(' ', '')
            device_ids = args.devices.split(',')
            args.device_ids = [int(id_) for id_ in device_ids]
            args.gpu = args.device_ids[0]
        
        print('\n📊 实验参数:')
        print_args(args)
        
        # 创建实验对象
        Exp = Exp_Earlysignaldet
        
        print("\n🚀 开始运行早期信号检测模型...")
        print("-" * 60)
        
        # 执行训练或测试
        if args.is_training:
            for ii in range(args.itr):
                exp = Exp(args)
                setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                    args.task_name, args.model_id, args.model, args.data, args.features,
                    args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                    args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                    args.distil, args.des, ii)
                
                print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
                exp.train(setting)
                
                print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                exp.test(setting)
                
                if args.do_predict:
                    print('>>>>>>>predicting : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
                    exp.predict(setting, True)
                
                torch.cuda.empty_cache()
        else:
            ii = 0
            setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
                args.task_name, args.model_id, args.model, args.data, args.features,
                args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
                args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
                args.distil, args.des, ii)
            
            exp = Exp(args)
            print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
            exp.test(setting, test=1)
            torch.cuda.empty_cache()
        
        print("-" * 60)
        print("✅ 模型运行完成!")
        return True
        
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

# 钻井数据后处理器使用说明

## 🎯 功能概述

`drilling_data_postprocessor.py` 是钻井卡钻数据导出器的配套后处理工具，专门用于：

1. **工况分析**: 分析已导出数据的工况分布情况
2. **工况筛选**: 只保留5种核心工况的数据（正常钻进、起钻、下钻、正划眼、倒划眼）
3. **数据分类**: 按工况重新组织文件夹结构
4. **时间窗口切分**: 将征兆数据和正常数据按3分钟窗口切分成训练样本
5. **质量分析**: 生成详细的数据分布和质量报告

## 📋 使用前提

1. **已完成数据导出**: 必须先运行 `drilling_stuck_data_exporter.py` 完成原始数据导出
2. **输入目录结构**: 确保存在以下目录结构：
   ```
   stuck_prediction_data/
   ├── symptom_data/     # 征兆数据
   ├── test_data/        # 测试数据
   └── normal_data/      # 正常数据
   ```

## 🔧 使用方法

### 1. 配置参数

修改 `main()` 函数中的路径配置：

```python
INPUT_DIR = r"D:\采数据\PROCESS\stuck_prediction_data"  # 原始导出数据目录
OUTPUT_DIR = r"D:\采数据\PROCESS\processed_data"        # 处理后数据目录
```

### 2. 运行后处理

```bash
python drilling_data_postprocessor.py
```

### 3. 处理流程

脚本会自动执行以下步骤：

1. **📊 分析原始数据工况分布**
   - 扫描所有CSV文件
   - 统计各种工况的数量和分布
   - 显示Top 10工况统计

2. **🔄 按工况筛选和分类数据**
   - 只保留5种目标工况的数据
   - 按工况创建子目录
   - 对征兆数据和正常数据进行时间窗口切分

3. **📋 生成分析报告**
   - 创建详细的Markdown报告
   - 包含处理前后的数据对比
   - 提供数据使用建议

## 📊 输出结构

```
processed_data/
├── symptom_data/           # 征兆数据（异常样本）
│   ├── 正常钻进/
│   │   ├── 井名_时间_征兆数据_窗口1.csv  # 0-3分钟
│   │   ├── 井名_时间_征兆数据_窗口2.csv  # 3-6分钟
│   │   ├── 井名_时间_征兆数据_窗口3.csv  # 6-9分钟
│   │   ├── 井名_时间_征兆数据_窗口4.csv  # 9-12分钟
│   │   └── 井名_时间_征兆数据_窗口5.csv  # 12-15分钟
│   ├── 起钻/
│   ├── 下钻/
│   ├── 正划眼/
│   └── 倒划眼/
├── test_data/              # 测试数据（模型验证）
│   ├── 正常钻进/
│   │   └── 井名_时间_测试数据.csv
│   ├── 起钻/
│   ├── 下钻/
│   ├── 正划眼/
│   └── 倒划眼/
├── normal_data/            # 正常数据（正常样本）
│   ├── 正常钻进/
│   ├── 起钻/
│   ├── 下钻/
│   ├── 正划眼/
│   └── 倒划眼/
└── 工况分布分析报告.md     # 详细分析报告
```

## 🎯 核心特性

### 1. 智能工况映射

自动识别和标准化各种工况表达方式：

```python
condition_mapping = {
    '正常钻进': ['正常钻进', '钻进', '钻井', 'DRILLING', '钻', '进'],
    '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT', '起', '上'],
    '下钻': ['下钻', '下放', '下钻具', 'TRIP_IN', '下', '入'],
    '正划眼': ['正划眼', '划眼', '扩眼', 'REAMING', '划', '扩'],
    '倒划眼': ['倒划眼', '反划眼', 'BACK_REAMING', '倒', '反']
}
```

### 2. 精确时间窗口切分

**征兆数据**：15分钟数据切分为5个3分钟窗口
- **窗口1**: 0-3分钟（卡钻前15-12分钟）
- **窗口2**: 3-6分钟（卡钻前12-9分钟）
- **窗口3**: 6-9分钟（卡钻前9-6分钟）
- **窗口4**: 9-12分钟（卡钻前6-3分钟）
- **窗口5**: 12-15分钟（卡钻前3-0分钟）

**正常数据**：24小时数据切分为多个3分钟窗口
- 每3分钟一个窗口，覆盖完整24小时
- 大幅增加正常样本数量（约480个窗口）
- 与征兆数据保持相同的时间粒度

**测试数据**：保持24小时完整数据
- 不进行时间窗口切分
- 用于验证模型的长期预警能力

### 3. 自动编码检测

支持多种中文编码格式：
- UTF-8
- UTF-8-SIG
- GBK
- GB2312

### 4. 详细进度监控

实时显示处理进度和统计信息：
- 文件处理进度
- 工况分布统计
- 时间窗口创建数量
- 错误处理和恢复

## 📈 报告内容

生成的 `工况分布分析报告.md` 包含：

1. **处理概览**: 文件数量、成功率等基本统计
2. **原始数据工况分布**: 处理前的完整工况统计
3. **筛选后数据分布**: 5种目标工况的数据统计
4. **时间窗口统计**: 征兆数据的窗口切分情况
5. **输出文件结构**: 完整的目录结构说明
6. **数据使用建议**: 模型训练的数据组织建议
7. **工况平衡建议**: 各工况样本数量平衡分析

## ⚠️ 注意事项

### 1. 数据完整性检查
- 确保原始导出数据完整
- 检查RIGSTA字段是否存在
- 验证时间字段格式正确

### 2. 磁盘空间要求
- 处理后的数据量可能比原始数据大（特别是征兆数据切分后）
- 确保有足够的磁盘空间

### 3. 内存使用
- 大文件处理时注意内存使用
- 必要时可以分批处理

### 4. 工况识别
- 检查工况映射是否覆盖所有实际工况
- 根据需要调整 `condition_mapping` 配置

## 🔧 自定义配置

### 修改目标工况

```python
target_conditions = [
    '正常钻进',
    '起钻',
    '下钻', 
    '正划眼',
    '倒划眼'
    # 可以添加或修改工况
]
```

### 修改时间窗口

```python
time_windows = [
    {'name': '窗口1', 'start': 0, 'end': 3, 'description': '0-3分钟'},
    {'name': '窗口2', 'start': 3, 'end': 6, 'description': '3-6分钟'},
    # 可以调整窗口大小和数量
]
```

### 添加工况映射

```python
condition_mapping = {
    '正常钻进': ['正常钻进', '钻进', '钻井', 'DRILLING', '钻', '进'],
    # 根据实际数据添加更多映射关系
}
```

## 📋 故障排除

### 常见问题

1. **输入目录不存在**
   - 检查路径配置是否正确
   - 确认已完成原始数据导出

2. **没有找到目标工况数据**
   - 检查工况映射配置
   - 查看原始数据的RIGSTA字段值

3. **时间窗口切分失败**
   - 检查数据中是否有date字段
   - 确认时间格式正确

4. **编码问题**
   - 脚本会自动尝试多种编码
   - 如有特殊编码需求，可修改编码列表

### 调试模式

添加更多调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

**总结**: 这个后处理器是钻井卡钻预警模型数据准备流程的关键步骤，能够将原始导出数据转换为适合模型训练的结构化数据集，特别是征兆数据的时间窗口切分功能，为PatchTST模型提供了理想的输入格式。

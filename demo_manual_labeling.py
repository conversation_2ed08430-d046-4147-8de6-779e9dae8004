#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示人工标注工作流程
创建示例数据并展示完整的标注流程
"""

import pandas as pd
import numpy as np
from pathlib import Path
from manual_labeling_preprocessor import ManualLabelingPreprocessor

def create_realistic_drilling_data():
    """
    创建更真实的钻井数据，包含一些异常模式
    """
    print("🔧 创建真实的钻井数据样本...")
    
    n_points = 500  # 500个数据点
    
    # 基础参数
    base_data = {
        'DEP': np.linspace(2970, 2975, n_points),  # 井深逐渐增加
        'BITDEP': np.linspace(2970, 2975, n_points),  # 钻头深度
    }
    
    # 创建不同的钻井阶段，包含一些异常
    stages = [
        # 阶段1: 正常钻进 (0-150)
        {
            'range': (0, 150),
            'condition': '正常钻进',
            'WOB': (160, 20),      # 钻压正常
            'RPM': (105, 5),       # 转速正常
            'HKLD': (970, 25),     # 大钩载荷正常
            'TOR': (18, 3),        # 扭矩正常
            'SPP': (25.8, 2),      # 立管压力正常
            'anomaly': False       # 无异常
        },
        
        # 阶段2: 开始出现异常 (150-200) - 这里可能是卡钻前兆
        {
            'range': (150, 200),
            'condition': '正常钻进',
            'WOB': (180, 30),      # 钻压开始升高，波动增大
            'RPM': (100, 8),       # 转速略有下降，波动增大
            'HKLD': (1000, 40),    # 大钩载荷升高
            'TOR': (22, 5),        # 扭矩升高
            'SPP': (27, 3),        # 立管压力升高
            'anomaly': True        # 有异常 - 潜在卡钻前兆
        },
        
        # 阶段3: 起钻操作 (200-280)
        {
            'range': (200, 280),
            'condition': '起钻',
            'WOB': (50, 15),       # 钻压降低（起钻时正常）
            'RPM': (0, 2),         # 转速为0（起钻时正常）
            'HKLD': (1100, 50),    # 大钩载荷高（拉钻具）
            'TOR': (5, 2),         # 扭矩低
            'SPP': (20, 2),        # 立管压力低
            'anomaly': False       # 起钻操作，正常
        },
        
        # 阶段4: 坐卡（实际上是正常的钻具处理）(280-320)
        {
            'range': (280, 320),
            'condition': '坐卡',
            'WOB': (200, 25),      # 钻压较高
            'RPM': (80, 10),       # 转速较低
            'HKLD': (950, 30),     # 大钩载荷正常
            'TOR': (15, 4),        # 扭矩正常
            'SPP': (24, 2),        # 立管压力正常
            'anomaly': False       # 这只是工况，不是真正的卡钻
        },
        
        # 阶段5: 下钻 (320-400)
        {
            'range': (320, 400),
            'condition': '下钻',
            'WOB': (30, 10),       # 钻压低（下钻时正常）
            'RPM': (0, 1),         # 转速为0
            'HKLD': (800, 30),     # 大钩载荷较低
            'TOR': (3, 1),         # 扭矩很低
            'SPP': (18, 1.5),      # 立管压力低
            'anomaly': False       # 下钻操作，正常
        },
        
        # 阶段6: 恢复正常钻进，但后期出现严重异常 (400-500)
        {
            'range': (400, 500),
            'condition': '正常钻进',
            'WOB': (170, 35),      # 后期钻压异常波动
            'RPM': (95, 12),       # 转速不稳定
            'HKLD': (980, 60),     # 大钩载荷波动大
            'TOR': (25, 8),        # 扭矩异常升高
            'SPP': (30, 4),        # 立管压力异常
            'anomaly': True        # 严重异常 - 明显的卡钻前兆
        }
    ]
    
    # 生成数据
    data = {col: np.zeros(n_points) for col in ['WOB', 'RPM', 'HKLD', 'TOR', 'SPP']}
    data['condition'] = [''] * n_points  # 字符串数组
    data.update(base_data)
    
    # 记录异常段（用于验证标注效果）
    anomaly_segments = []
    
    for stage in stages:
        start, end = stage['range']
        
        # 生成该阶段的数据
        for param in ['WOB', 'RPM', 'HKLD', 'TOR', 'SPP']:
            mean, std = stage[param]
            # 添加一些趋势和噪声
            base_values = np.random.normal(mean, std, end - start)
            
            # 如果是异常段，添加更多变异
            if stage['anomaly']:
                # 添加趋势变化
                trend = np.linspace(0, mean * 0.3, end - start)
                base_values += trend
                
                # 添加突发异常点
                anomaly_points = np.random.choice(end - start, size=max(1, (end - start) // 10), replace=False)
                for point in anomaly_points:
                    base_values[point] += np.random.normal(0, std * 2)
            
            data[param][start:end] = base_values
        
        # 设置工况标签
        data['condition'][start:end] = stage['condition']
        
        # 记录异常段
        if stage['anomaly']:
            anomaly_segments.append({
                'start': start,
                'end': end,
                'condition': stage['condition'],
                'description': '人工设定的异常段'
            })
    
    # 添加时间戳
    data['timestamp'] = pd.date_range('2022-06-23 18:00:00', periods=n_points, freq='10S')
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    print(f"✅ 创建了 {n_points} 个数据点")
    print(f"📊 工况分布: {df['condition'].value_counts().to_dict()}")
    print(f"🚨 异常段数量: {len(anomaly_segments)}")
    
    for i, seg in enumerate(anomaly_segments):
        print(f"  异常段 {i+1}: 索引 {seg['start']}-{seg['end']}, 工况: {seg['condition']}")
    
    return df, anomaly_segments

def demonstrate_labeling_workflow():
    """
    演示完整的标注工作流程
    """
    print("🎯 演示人工标注工作流程")
    print("=" * 60)
    
    # 1. 创建示例数据
    df, true_anomalies = create_realistic_drilling_data()
    
    # 保存示例数据
    sample_file = "sample_drilling_data.csv"
    df.to_csv(sample_file, index=False, encoding='utf-8-sig')
    print(f"✅ 示例数据已保存: {sample_file}")
    
    # 2. 使用预处理器创建标注模板
    config = {
        'window_size': 50,           # 较小的窗口，便于演示
        'step_size': 10,             # 较小的步长
        'min_quality_score': 0.5,    # 较低的质量阈值
        'prediction_horizon': 5,     # 预测提前量
    }
    
    preprocessor = ManualLabelingPreprocessor(config)
    
    try:
        # 加载和标准化数据
        df_std = preprocessor.standardize_columns(df)
        
        # 创建滑动窗口
        windows = preprocessor.create_sliding_windows(df_std)
        
        # 导出标注模板
        template_file = preprocessor.export_for_labeling(windows, "demo_labeling")
        
        print(f"\n📋 标注模板已生成: {template_file}")
        
        # 3. 模拟人工标注过程
        print(f"\n🤖 模拟人工标注过程...")
        simulated_labels = simulate_manual_labeling(windows, true_anomalies)
        
        # 保存模拟标注结果
        demo_labeled_file = "demo_labeling/demo_labeled_template.csv"
        simulated_labels.to_csv(demo_labeled_file, index=False, encoding='utf-8-sig')
        print(f"✅ 模拟标注结果已保存: {demo_labeled_file}")
        
        # 4. 创建训练数据集
        print(f"\n📊 创建训练数据集...")
        dataset_info = preprocessor.create_training_dataset(
            demo_labeled_file, 
            "demo_labeling/window_data",
            "demo_training_dataset"
        )
        
        if dataset_info:
            print(f"\n🎉 演示完成!")
            print(f"📈 标注效果评估:")
            
            # 评估标注效果
            evaluate_labeling_quality(simulated_labels, true_anomalies, windows)
        
        return dataset_info
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def simulate_manual_labeling(windows: list, true_anomalies: list) -> pd.DataFrame:
    """
    模拟人工标注过程
    """
    print(f"🏷️ 模拟标注 {len(windows)} 个窗口...")
    
    # 加载标注模板
    template_file = "demo_labeling/labeling_template.csv"
    template_df = pd.read_csv(template_file, encoding='utf-8-sig')
    
    # 为每个窗口分配标签
    for i, row in template_df.iterrows():
        window_id = row['window_id']
        start_idx = row['start_index']
        end_idx = row['end_index']
        
        # 检查窗口是否与已知异常段重叠
        is_anomaly = False
        overlap_ratio = 0
        
        for anomaly in true_anomalies:
            # 计算重叠
            overlap_start = max(start_idx, anomaly['start'])
            overlap_end = min(end_idx, anomaly['end'])
            
            if overlap_start < overlap_end:
                overlap_length = overlap_end - overlap_start
                window_length = end_idx - start_idx
                current_overlap_ratio = overlap_length / window_length
                
                if current_overlap_ratio > overlap_ratio:
                    overlap_ratio = current_overlap_ratio
        
        # 根据重叠比例决定标签
        if overlap_ratio > 0.3:  # 30%以上重叠认为是异常
            label = 1
            confidence = min(5, int(overlap_ratio * 5) + 3)  # 3-5分
            notes = f"与异常段重叠{overlap_ratio:.1%}"
        else:
            label = 0
            confidence = 4  # 正常样本置信度较高
            notes = "正常操作"
        
        # 添加一些随机性模拟人工判断的不确定性
        if np.random.random() < 0.1:  # 10%的概率调整标签
            if label == 1 and overlap_ratio < 0.5:
                label = 0
                confidence = 2
                notes += " (边界情况，调整为正常)"
            elif label == 0 and np.random.random() < 0.05:
                label = 1
                confidence = 2
                notes += " (可疑异常)"
        
        # 更新模板
        template_df.at[i, 'label'] = label
        template_df.at[i, 'confidence'] = confidence
        template_df.at[i, 'notes'] = notes
    
    # 统计标注结果
    positive_count = (template_df['label'] == 1).sum()
    negative_count = (template_df['label'] == 0).sum()
    
    print(f"📊 模拟标注结果:")
    print(f"  - 正样本: {positive_count}")
    print(f"  - 负样本: {negative_count}")
    print(f"  - 正样本比例: {positive_count/(positive_count+negative_count):.3f}")
    
    return template_df

def evaluate_labeling_quality(labeled_df: pd.DataFrame, true_anomalies: list, windows: list):
    """
    评估标注质量
    """
    print(f"\n📈 标注质量评估:")
    
    # 计算准确率相关指标
    true_positives = 0
    false_positives = 0
    true_negatives = 0
    false_negatives = 0
    
    for _, row in labeled_df.iterrows():
        predicted_label = row['label']
        start_idx = row['start_index']
        end_idx = row['end_index']
        
        # 判断真实标签
        actual_anomaly = False
        for anomaly in true_anomalies:
            overlap_start = max(start_idx, anomaly['start'])
            overlap_end = min(end_idx, anomaly['end'])
            if overlap_start < overlap_end:
                overlap_ratio = (overlap_end - overlap_start) / (end_idx - start_idx)
                if overlap_ratio > 0.3:
                    actual_anomaly = True
                    break
        
        # 统计混淆矩阵
        if predicted_label == 1 and actual_anomaly:
            true_positives += 1
        elif predicted_label == 1 and not actual_anomaly:
            false_positives += 1
        elif predicted_label == 0 and not actual_anomaly:
            true_negatives += 1
        elif predicted_label == 0 and actual_anomaly:
            false_negatives += 1
    
    # 计算指标
    total = len(labeled_df)
    accuracy = (true_positives + true_negatives) / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"  - 准确率 (Accuracy): {accuracy:.3f}")
    print(f"  - 精确率 (Precision): {precision:.3f}")
    print(f"  - 召回率 (Recall): {recall:.3f}")
    print(f"  - F1分数: {f1_score:.3f}")
    
    print(f"\n📊 混淆矩阵:")
    print(f"  - 真正例 (TP): {true_positives}")
    print(f"  - 假正例 (FP): {false_positives}")
    print(f"  - 真负例 (TN): {true_negatives}")
    print(f"  - 假负例 (FN): {false_negatives}")

def main():
    """
    主演示函数
    """
    print("🎯 钻井卡钻人工标注系统演示")
    print("=" * 60)
    
    print("📋 本演示将展示:")
    print("1. 创建包含异常的真实钻井数据")
    print("2. 生成滑动窗口和标注模板")
    print("3. 模拟人工标注过程")
    print("4. 创建训练数据集")
    print("5. 评估标注质量")
    
    print(f"\n🚀 开始演示...")
    
    try:
        result = demonstrate_labeling_workflow()
        
        if result:
            print(f"\n" + "="*60)
            print("🎉 演示完成!")
            print("="*60)
            
            print(f"\n💡 实际使用时:")
            print(f"1. 使用您的真实钻井数据替换示例数据")
            print(f"2. 根据您的经验进行人工标注")
            print(f"3. 使用生成的训练数据集训练PatchTST模型")
            print(f"4. 部署模型进行实时卡钻预警")
            
            print(f"\n📁 生成的文件:")
            print(f"  - 示例数据: sample_drilling_data.csv")
            print(f"  - 标注模板: demo_labeling/labeling_template.csv")
            print(f"  - 训练数据: demo_training_dataset/")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

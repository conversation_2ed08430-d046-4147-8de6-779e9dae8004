# 工况感知的早期征兆检测训练脚本
# 集成到现有TSlib框架中的完整训练方案

import os
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 导入我们的自定义模块
from single_model_strategy import UnifiedConditionDataLoader, UnifiedPatchTSTModel
from multi_condition_strategy import MultiConditionDataLoader, MultiConditionPredictor

class ConditionAwareDataset(Dataset):
    """工况感知的数据集类"""
    
    def __init__(self, sequences, condition_features, condition_labels, drilling_labels):
        self.sequences = torch.FloatTensor(sequences)
        self.condition_features = torch.FloatTensor(condition_features)
        self.condition_labels = torch.LongTensor(condition_labels)
        self.drilling_labels = torch.LongTensor(drilling_labels)
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return {
            'sequence': self.sequences[idx],
            'condition_features': self.condition_features[idx],
            'condition_labels': self.condition_labels[idx],
            'drilling_label': self.drilling_labels[idx]
        }

class ConditionAwareTrainer:
    """工况感知训练器"""
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数
        self.drilling_criterion = nn.CrossEntropyLoss()
        self.condition_criterion = nn.CrossEntropyLoss()
        
        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
        
        # 训练历史
        self.train_history = {
            'train_loss': [], 'val_loss': [],
            'train_drilling_acc': [], 'val_drilling_acc': [],
            'train_condition_acc': [], 'val_condition_acc': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        drilling_correct = 0
        condition_correct = 0
        total_samples = 0
        
        for batch in train_loader:
            # 数据移到设备
            sequences = batch['sequence'].to(self.device)
            condition_features = batch['condition_features'].to(self.device)
            condition_labels = batch['condition_labels'].to(self.device)
            drilling_labels = batch['drilling_label'].to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(sequences, condition_features, condition_labels)
            
            # 计算损失
            drilling_loss = self.drilling_criterion(outputs['drilling_risk'], drilling_labels)
            
            # 工况损失 - 使用序列中的主要工况
            main_conditions = torch.mode(condition_labels, dim=1)[0]
            condition_loss = self.condition_criterion(outputs['condition_prediction'], main_conditions)
            
            # 总损失 (可调整权重)
            total_batch_loss = drilling_loss + 0.3 * condition_loss
            
            # 反向传播
            total_batch_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 统计
            total_loss += total_batch_loss.item()
            
            # 计算准确率
            drilling_pred = torch.argmax(outputs['drilling_risk'], dim=1)
            drilling_correct += (drilling_pred == drilling_labels).sum().item()
            
            condition_pred = torch.argmax(outputs['condition_prediction'], dim=1)
            condition_correct += (condition_pred == main_conditions).sum().item()
            
            total_samples += sequences.size(0)
        
        avg_loss = total_loss / len(train_loader)
        drilling_acc = drilling_correct / total_samples
        condition_acc = condition_correct / total_samples
        
        return avg_loss, drilling_acc, condition_acc
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        drilling_correct = 0
        condition_correct = 0
        total_samples = 0
        
        all_drilling_preds = []
        all_drilling_labels = []
        all_condition_preds = []
        all_condition_labels = []
        
        with torch.no_grad():
            for batch in val_loader:
                sequences = batch['sequence'].to(self.device)
                condition_features = batch['condition_features'].to(self.device)
                condition_labels = batch['condition_labels'].to(self.device)
                drilling_labels = batch['drilling_label'].to(self.device)
                
                outputs = self.model(sequences, condition_features, condition_labels)
                
                # 计算损失
                drilling_loss = self.drilling_criterion(outputs['drilling_risk'], drilling_labels)
                main_conditions = torch.mode(condition_labels, dim=1)[0]
                condition_loss = self.condition_criterion(outputs['condition_prediction'], main_conditions)
                total_batch_loss = drilling_loss + 0.3 * condition_loss
                
                total_loss += total_batch_loss.item()
                
                # 预测
                drilling_pred = torch.argmax(outputs['drilling_risk'], dim=1)
                condition_pred = torch.argmax(outputs['condition_prediction'], dim=1)
                
                drilling_correct += (drilling_pred == drilling_labels).sum().item()
                condition_correct += (condition_pred == main_conditions).sum().item()
                total_samples += sequences.size(0)
                
                # 收集预测结果用于详细分析
                all_drilling_preds.extend(drilling_pred.cpu().numpy())
                all_drilling_labels.extend(drilling_labels.cpu().numpy())
                all_condition_preds.extend(condition_pred.cpu().numpy())
                all_condition_labels.extend(main_conditions.cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        drilling_acc = drilling_correct / total_samples
        condition_acc = condition_correct / total_samples
        
        return avg_loss, drilling_acc, condition_acc, (all_drilling_preds, all_drilling_labels, 
                                                      all_condition_preds, all_condition_labels)
    
    def train(self, train_loader, val_loader, epochs=100, save_path='./models'):
        """完整训练流程"""
        os.makedirs(save_path, exist_ok=True)
        best_val_loss = float('inf')
        patience_counter = 0
        max_patience = 20
        
        print("开始训练工况感知的早期征兆检测模型...")
        print(f"设备: {self.device}")
        print(f"训练样本数: {len(train_loader.dataset)}")
        print(f"验证样本数: {len(val_loader.dataset)}")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_drilling_acc, train_condition_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_drilling_acc, val_condition_acc, predictions = self.validate_epoch(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['train_drilling_acc'].append(train_drilling_acc)
            self.train_history['val_drilling_acc'].append(val_drilling_acc)
            self.train_history['train_condition_acc'].append(train_condition_acc)
            self.train_history['val_condition_acc'].append(val_condition_acc)
            
            # 打印进度
            if epoch % 10 == 0 or epoch == epochs - 1:
                print(f"Epoch {epoch+1}/{epochs}")
                print(f"  训练 - 损失: {train_loss:.4f}, 卡钻准确率: {train_drilling_acc:.4f}, 工况准确率: {train_condition_acc:.4f}")
                print(f"  验证 - 损失: {val_loss:.4f}, 卡钻准确率: {val_drilling_acc:.4f}, 工况准确率: {val_condition_acc:.4f}")
                print(f"  学习率: {self.optimizer.param_groups[0]['lr']:.6f}")
                print("-" * 60)
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                
                # 保存模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'train_history': self.train_history
                }, os.path.join(save_path, 'best_condition_aware_model.pth'))
                
                print(f"保存最佳模型 (验证损失: {val_loss:.4f})")
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= max_patience:
                print(f"验证损失连续{max_patience}个epoch未改善，提前停止训练")
                break
        
        # 最终评估
        self.final_evaluation(predictions, save_path)
        
        return self.train_history
    
    def final_evaluation(self, predictions, save_path):
        """最终评估和可视化"""
        drilling_preds, drilling_labels, condition_preds, condition_labels = predictions
        
        # 卡钻预测评估
        print("\n=== 卡钻预测评估 ===")
        drilling_acc = accuracy_score(drilling_labels, drilling_preds)
        print(f"卡钻预测准确率: {drilling_acc:.4f}")
        print("\n卡钻预测分类报告:")
        print(classification_report(drilling_labels, drilling_preds, 
                                  target_names=['正常', '卡钻征兆']))
        
        # 工况预测评估
        print("\n=== 工况预测评估 ===")
        condition_acc = accuracy_score(condition_labels, condition_preds)
        print(f"工况预测准确率: {condition_acc:.4f}")
        print("\n工况预测分类报告:")
        condition_names = ['起钻', '下钻', '正常钻进', '正划眼', '倒划眼']
        print(classification_report(condition_labels, condition_preds, 
                                  target_names=condition_names))
        
        # 绘制混淆矩阵
        self.plot_confusion_matrices(drilling_labels, drilling_preds, 
                                    condition_labels, condition_preds, save_path)
        
        # 绘制训练历史
        self.plot_training_history(save_path)
    
    def plot_confusion_matrices(self, drilling_labels, drilling_preds, 
                               condition_labels, condition_preds, save_path):
        """绘制混淆矩阵"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 卡钻预测混淆矩阵
        drilling_cm = confusion_matrix(drilling_labels, drilling_preds)
        sns.heatmap(drilling_cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['正常', '卡钻征兆'], 
                   yticklabels=['正常', '卡钻征兆'], ax=axes[0])
        axes[0].set_title('卡钻预测混淆矩阵')
        axes[0].set_xlabel('预测标签')
        axes[0].set_ylabel('真实标签')
        
        # 工况预测混淆矩阵
        condition_cm = confusion_matrix(condition_labels, condition_preds)
        condition_names = ['起钻', '下钻', '正常钻进', '正划眼', '倒划眼']
        sns.heatmap(condition_cm, annot=True, fmt='d', cmap='Greens', 
                   xticklabels=condition_names, yticklabels=condition_names, ax=axes[1])
        axes[1].set_title('工况预测混淆矩阵')
        axes[1].set_xlabel('预测标签')
        axes[1].set_ylabel('真实标签')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'confusion_matrices.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_training_history(self, save_path):
        """绘制训练历史"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        axes[0, 0].plot(self.train_history['train_loss'], label='训练损失')
        axes[0, 0].plot(self.train_history['val_loss'], label='验证损失')
        axes[0, 0].set_title('损失曲线')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 卡钻预测准确率
        axes[0, 1].plot(self.train_history['train_drilling_acc'], label='训练准确率')
        axes[0, 1].plot(self.train_history['val_drilling_acc'], label='验证准确率')
        axes[0, 1].set_title('卡钻预测准确率')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('准确率')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 工况预测准确率
        axes[1, 0].plot(self.train_history['train_condition_acc'], label='训练准确率')
        axes[1, 0].plot(self.train_history['val_condition_acc'], label='验证准确率')
        axes[1, 0].set_title('工况预测准确率')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('准确率')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 学习率变化（如果有记录的话）
        axes[1, 1].text(0.5, 0.5, '训练完成\n\n最佳验证损失:\n{:.4f}'.format(min(self.train_history['val_loss'])), 
                       ha='center', va='center', transform=axes[1, 1].transAxes, fontsize=14)
        axes[1, 1].set_title('训练总结')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'training_history.png'), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主训练函数"""
    print("=== 工况感知的早期征兆检测训练 ===")
    
    # 1. 数据加载
    print("1. 加载数据...")
    data_loader = UnifiedConditionDataLoader('./dataset/earlysignaldetection', seq_len=152)
    sequences, condition_features, condition_labels, drilling_labels = data_loader.load_unified_dataset()
    
    if len(sequences) == 0:
        print("错误: 未找到有效数据")
        return
    
    print(f"数据加载完成: {len(sequences)} 个样本")
    print(f"正样本比例: {np.mean(drilling_labels):.3f}")
    
    # 2. 数据分割
    print("2. 分割数据集...")
    train_seq, val_seq, train_cond_feat, val_cond_feat, train_cond_label, val_cond_label, train_drill_label, val_drill_label = train_test_split(
        sequences, condition_features, condition_labels, drilling_labels, 
        test_size=0.2, random_state=42, stratify=drilling_labels
    )
    
    # 3. 创建数据集和数据加载器
    train_dataset = ConditionAwareDataset(train_seq, train_cond_feat, train_cond_label, train_drill_label)
    val_dataset = ConditionAwareDataset(val_seq, val_cond_feat, val_cond_label, val_drill_label)
    
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=16, shuffle=False, num_workers=0)
    
    # 4. 创建模型
    print("3. 创建模型...")
    model = UnifiedPatchTSTModel(
        seq_len=152,
        d_model=128,
        n_heads=8,
        num_layers=3,
        condition_feature_dim=condition_features.shape[-1]
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 5. 训练
    print("4. 开始训练...")
    trainer = ConditionAwareTrainer(model)
    history = trainer.train(train_loader, val_loader, epochs=100, save_path='./condition_aware_models')
    
    print("训练完成!")

if __name__ == "__main__":
    main()

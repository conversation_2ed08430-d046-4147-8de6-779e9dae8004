{"total_files": 2392, "processed_files": 314, "total_segments": 136, "condition_distribution": {"UNKNOWN": 79, "正常钻进": 44, "起钻": 2, "正划眼": 11}, "quality_distribution": {"80%-90%": 9, "70%-80%": 105, "90%-100%": 6, "60%-70%": 12, "100%-110%": 4}, "processing_errors": [{"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-15-00_to_2022-03-04_17-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-18-00_to_2022-03-04_17-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-21-00_to_2022-03-04_17-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-24-00_to_2022-03-04_17-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-27-00_to_2022-03-04_17-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H35-2_220304_2022-03-04_17-30-00_to_2022-03-04_17-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-3_220420_2022-04-20_06-00-00_to_2022-04-20_06-03-00.csv", "error": "'utf-8' codec can't decode byte 0xb6 in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-3_220420_2022-04-20_06-03-00_to_2022-04-20_06-06-00.csv", "error": "'utf-8' codec can't decode byte 0xb6 in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-3_220420_2022-04-20_06-06-00_to_2022-04-20_06-09-00.csv", "error": "'utf-8' codec can't decode byte 0xb6 in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-4_220315_2022-03-15_08-00-00_to_2022-03-15_08-03-00.csv", "error": "'utf-8' codec can't decode byte 0xb6 in position 5: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-4_220315_2022-03-15_08-03-00_to_2022-03-15_08-06-00.csv", "error": "'utf-8' codec can't decode byte 0xb6 in position 5: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220709_2022-07-09_12-27-00_to_2022-07-09_12-30-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220709_2022-07-09_12-30-00_to_2022-07-09_12-33-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220709_2022-07-09_12-33-00_to_2022-07-09_12-36-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220709_2022-07-09_12-36-00_to_2022-07-09_12-39-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220709_2022-07-09_12-39-00_to_2022-07-09_12-42-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-33-00_to_2022-07-18_13-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-36-00_to_2022-07-18_13-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-39-00_to_2022-07-18_13-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-42-00_to_2022-07-18_13-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-45-00_to_2022-07-18_13-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-48-00_to_2022-07-18_13-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H54-6_220718_2022-07-18_13-51-00_to_2022-07-18_13-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-15-00_to_2022-10-07_04-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-18-00_to_2022-10-07_04-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-21-00_to_2022-10-07_04-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-24-00_to_2022-10-07_04-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-27-00_to_2022-10-07_04-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H56-4_221007_2022-10-07_04-30-00_to_2022-10-07_04-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-15-00_to_2023-06-10_06-18-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-18-00_to_2023-06-10_06-21-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-21-00_to_2023-06-10_06-24-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-24-00_to_2023-06-10_06-27-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-27-00_to_2023-06-10_06-30-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_220610_2023-06-10_06-30-00_to_2023-06-10_06-33-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-18-00_to_2023-01-12_04-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-21-00_to_2023-01-12_04-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-24-00_to_2023-01-12_04-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-27-00_to_2023-01-12_04-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-30-00_to_2023-01-12_04-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-5_230112_2023-01-12_04-33-00_to_2023-01-12_04-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-8_221220_2022-12-20_18-45-00_to_2022-12-20_18-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-8_221220_2022-12-20_18-48-00_to_2022-12-20_18-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-8_221220_2022-12-20_18-51-00_to_2022-12-20_18-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-8_221220_2022-12-20_18-54-00_to_2022-12-20_18-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H62-8_221220_2022-12-20_18-57-00_to_2022-12-20_19-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_03-45-00_to_2022-10-05_03-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_03-48-00_to_2022-10-05_03-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_03-51-00_to_2022-10-05_03-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_03-54-00_to_2022-10-05_03-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_03-57-00_to_2022-10-05_04-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_04-00-00_to_2022-10-05_04-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_20221005_2022-10-05_04-03-00_to_2022-10-05_04-06-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_221118_2022-11-18_00-27-00_to_2022-11-18_00-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_221118_2022-11-18_00-30-00_to_2022-11-18_00-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_221118_2022-11-18_00-33-00_to_2022-11-18_00-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_221118_2022-11-18_00-36-00_to_2022-11-18_00-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-5_221118_2022-11-18_00-39-00_to_2022-11-18_00-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-30-00_to_2023-01-26_07-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-33-00_to_2023-01-26_07-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-36-00_to_2023-01-26_07-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-39-00_to_2023-01-26_07-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-42-00_to_2023-01-26_07-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-45-00_to_2023-01-26_07-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-48-00_to_2023-01-26_07-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-51-00_to_2023-01-26_07-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-54-00_to_2023-01-26_07-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_07-57-00_to_2023-01-26_08-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-6_23126_2023-01-26_08-00-00_to_2023-01-26_08-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-8_220831_2022-08-31_12-45-00_to_2022-08-31_12-48-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-8_220831_2022-08-31_12-48-00_to_2022-08-31_12-51-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-8_220831_2022-08-31_12-51-00_to_2022-08-31_12-54-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-8_220831_2022-08-31_12-54-00_to_2022-08-31_12-57-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H69-8_220831_2022-08-31_12-57-00_to_2022-08-31_13-00-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_17-54-00_to_2022-04-12_17-57-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_17-57-00_to_2022-04-12_18-00-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_18-00-00_to_2022-04-12_18-03-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_18-03-00_to_2022-04-12_18-06-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_18-06-00_to_2022-04-12_18-09-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_18-09-00_to_2022-04-12_18-12-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自201H70-2_220412_2022-04-12_18-12-00_to_2022-04-12_18-15-00.csv", "error": "'utf-8' codec can't decode byte 0xce in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-15-00_to_2022-06-23_18-18-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-18-00_to_2022-06-23_18-21-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-21-00_to_2022-06-23_18-24-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-24-00_to_2022-06-23_18-27-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-27-00_to_2022-06-23_18-30-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-4_220623_2022-06-23_18-30-00_to_2022-06-23_18-33-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-39-00_to_2022-05-24_15-42-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-42-00_to_2022-05-24_15-45-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-45-00_to_2022-05-24_15-48-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-48-00_to_2022-05-24_15-51-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-51-00_to_2022-05-24_15-54-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220524_2022-05-24_15-54-00_to_2022-05-24_15-57-00.csv", "error": "'utf-8' codec can't decode byte 0xbf in position 2: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-27-00_to_2022-06-12_06-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-30-00_to_2022-06-12_06-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-33-00_to_2022-06-12_06-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-36-00_to_2022-06-12_06-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-39-00_to_2022-06-12_06-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-7_220612_2022-06-12_06-42-00_to_2022-06-12_06-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_12-45-00_to_2023-08-18_12-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_12-48-00_to_2023-08-18_12-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_12-51-00_to_2023-08-18_12-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_12-54-00_to_2023-08-18_12-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_12-57-00_to_2023-08-18_13-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H1-8_230818_2023-08-18_13-00-00_to_2023-08-18_13-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_221028_2022-10-28_20-15-00_to_2022-10-28_20-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_221028_2022-10-28_20-18-00_to_2022-10-28_20-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_221028_2022-10-28_20-21-00_to_2022-10-28_20-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_221028_2022-10-28_20-24-00_to_2022-10-28_20-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_221028_2022-10-28_20-27-00_to_2022-10-28_20-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231101_2023-11-01_13-42-00_to_2023-11-01_13-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231101_2023-11-01_13-45-00_to_2023-11-01_13-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231101_2023-11-01_13-48-00_to_2023-11-01_13-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231101_2023-11-01_13-51-00_to_2023-11-01_13-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-33-00_to_2023-11-27_04-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-36-00_to_2023-11-27_04-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-39-00_to_2023-11-27_04-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-42-00_to_2023-11-27_04-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-45-00_to_2023-11-27_04-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-3_231127_2023-11-27_04-48-00_to_2023-11-27_04-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-03-00_to_2023-08-27_04-06-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-06-00_to_2023-08-27_04-09-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-09-00_to_2023-08-27_04-12-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-12-00_to_2023-08-27_04-15-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-15-00_to_2023-08-27_04-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自205H2-4_230827_2023-08-27_04-18-00_to_2023-08-27_04-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-36-00_to_2022-01-26_19-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-39-00_to_2022-01-26_19-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-42-00_to_2022-01-26_19-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-45-00_to_2022-01-26_19-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-48-00_to_2022-01-26_19-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220126_2022-01-26_19-51-00_to_2022-01-26_19-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc8 in position 2: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-15-00_to_2022-08-06_00-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-18-00_to_2022-08-06_00-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-21-00_to_2022-08-06_00-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-24-00_to_2022-08-06_00-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-27-00_to_2022-08-06_00-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-30-00_to_2022-08-06_00-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-33-00_to_2022-08-06_00-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-36-00_to_2022-08-06_00-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-39-00_to_2022-08-06_00-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-42-00_to_2022-08-06_00-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-45-00_to_2022-08-06_00-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-48-00_to_2022-08-06_00-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-51-00_to_2022-08-06_00-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-54-00_to_2022-08-06_00-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_00-57-00_to_2022-08-06_01-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-00-00_to_2022-08-06_01-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-03-00_to_2022-08-06_01-06-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-06-00_to_2022-08-06_01-09-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-09-00_to_2022-08-06_01-12-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-12-00_to_2022-08-06_01-15-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-15-00_to_2022-08-06_01-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-18-00_to_2022-08-06_01-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-21-00_to_2022-08-06_01-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-24-00_to_2022-08-06_01-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-27-00_to_2022-08-06_01-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-30-00_to_2022-08-06_01-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-33-00_to_2022-08-06_01-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-36-00_to_2022-08-06_01-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-39-00_to_2022-08-06_01-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-42-00_to_2022-08-06_01-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-45-00_to_2022-08-06_01-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-48-00_to_2022-08-06_01-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自225_220806_2022-08-06_01-51-00_to_2022-08-06_01-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_231017_2023-10-17_04-15-00_to_2023-10-17_04-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_231017_2023-10-17_04-18-00_to_2023-10-17_04-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_231017_2023-10-17_04-21-00_to_2023-10-17_04-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_231017_2023-10-17_04-24-00_to_2023-10-17_04-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-30-00_to_2024-01-07_08-33-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-33-00_to_2024-01-07_08-36-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-36-00_to_2024-01-07_08-39-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-39-00_to_2024-01-07_08-42-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-42-00_to_2024-01-07_08-45-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240107_2024-01-07_08-45-00_to_2024-01-07_08-48-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240123_2024-01-23_13-00-00_to_2024-01-23_13-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240123_2024-01-23_13-03-00_to_2024-01-23_13-06-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240123_2024-01-23_13-06-00_to_2024-01-23_13-09-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240123_2024-01-23_13-09-00_to_2024-01-23_13-12-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自227_240123_2024-01-23_13-12-00_to_2024-01-23_13-15-00.csv", "error": "'utf-8' codec can't decode byte 0xc1 in position 0: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_19-48-00_to_2023-12-17_19-51-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_19-51-00_to_2023-12-17_19-54-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_19-54-00_to_2023-12-17_19-57-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_19-57-00_to_2023-12-17_20-00-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_20-00-00_to_2023-12-17_20-03-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231217_2023-12-17_20-03-00_to_2023-12-17_20-06-00.csv", "error": "'utf-8' codec can't decode byte 0xc6 in position 0: invalid continuation byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231224_2023-12-24_04-15-00_to_2023-12-24_04-18-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231224_2023-12-24_04-18-00_to_2023-12-24_04-21-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231224_2023-12-24_04-21-00_to_2023-12-24_04-24-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231224_2023-12-24_04-24-00_to_2023-12-24_04-27-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}, {"file_path": "dataset\\earlysignaldetection\\自卡钻\\自230_231224_2023-12-24_04-27-00_to_2023-12-24_04-30-00.csv", "error": "'utf-8' codec can't decode byte 0xc0 in position 4: invalid start byte", "error_type": "UnicodeDecodeError"}]}
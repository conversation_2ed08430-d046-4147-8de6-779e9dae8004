#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新训练真正的PatchTST模型
确保使用正确的PatchTST架构而不是简化版模型
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from condition_specific_trainer import ConditionSpecificTrainer
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset

def verify_patchtst_available():
    """验证PatchTST模型是否可用"""
    try:
        from models.PatchTST import Model as PatchTST
        print("PatchTST模型导入成功")
        return True
    except ImportError as e:
        print(f"PatchTST模型导入失败: {e}")
        print("请安装依赖: pip install reformer-pytorch einops")
        return False

def load_condition_data(condition_name):
    """加载指定工况的训练数据"""
    print(f"\n=== 加载 {condition_name} 数据 ===")
    
    # 数据路径
    symptom_dir = Path("dataset/processed_data/symptom_data") / condition_name
    normal_dir = Path("dataset/processed_data/normal_data") / condition_name
    
    all_data = []
    all_labels = []
    
    # 加载征兆数据 (标签=1)
    if symptom_dir.exists():
        symptom_files = list(symptom_dir.glob("*.csv"))
        print(f"征兆数据文件: {len(symptom_files)} 个")
        
        for file_path in symptom_files:
            try:
                df = pd.read_csv(file_path)
                # 选择特征列
                feature_cols = ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'HOKHEI', 'DRITIME', 'CSIP']

                if len(df) >= 180:
                    # 如果数据足够，取前180个点
                    sample = df.iloc[:180]
                    features = sample[feature_cols].values
                elif len(df) >= 60:
                    # 如果数据不足180但大于60，进行填充
                    sample = df[feature_cols].values
                    # 重复填充到180个点
                    repeat_times = (180 + len(sample) - 1) // len(sample)
                    features = np.tile(sample, (repeat_times, 1))[:180]
                else:
                    # 数据太少，跳过
                    continue

                all_data.append(features)
                all_labels.append(1)  # 异常
            except Exception as e:
                print(f"读取征兆数据文件失败 {file_path}: {e}")
    
    # 加载正常数据 (标签=0)
    if normal_dir.exists():
        normal_files = list(normal_dir.glob("*.csv"))
        print(f"正常数据文件: {len(normal_files)} 个")
        
        for file_path in normal_files:
            try:
                df = pd.read_csv(file_path)
                # 选择特征列
                feature_cols = ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'HOKHEI', 'DRITIME', 'CSIP']

                if len(df) >= 180:
                    # 如果数据足够，取前180个点
                    sample = df.iloc[:180]
                    features = sample[feature_cols].values
                elif len(df) >= 60:
                    # 如果数据不足180但大于60，进行填充
                    sample = df[feature_cols].values
                    # 重复填充到180个点
                    repeat_times = (180 + len(sample) - 1) // len(sample)
                    features = np.tile(sample, (repeat_times, 1))[:180]
                else:
                    # 数据太少，跳过
                    continue

                all_data.append(features)
                all_labels.append(0)  # 正常
            except Exception as e:
                print(f"读取正常数据文件失败 {file_path}: {e}")
    
    if len(all_data) == 0:
        print(f"没有找到 {condition_name} 的数据")
        return None, None
    
    # 转换为numpy数组
    X = np.array(all_data)  # shape: (n_samples, seq_len, n_features)
    y = np.array(all_labels)
    
    print(f"数据形状: {X.shape}")
    print(f"标签分布: 正常={np.sum(y==0)}, 异常={np.sum(y==1)}")
    
    return X, y

def train_patchtst_model(model, X_train, y_train, X_val, y_val, trainer):
    """训练PatchTST模型"""
    device = trainer.device

    # 创建数据加载器
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train),
        torch.LongTensor(y_train)
    )
    val_dataset = TensorDataset(
        torch.FloatTensor(X_val),
        torch.LongTensor(y_val)
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=trainer.training_config['batch_size'],
        shuffle=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=trainer.training_config['batch_size'],
        shuffle=False
    )

    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=trainer.training_config['learning_rate']
    )

    # 训练历史
    train_history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': []
    }

    best_val_acc = 0.0
    best_model = None
    patience_counter = 0
    patience = trainer.training_config.get('patience', 10)

    print(f"开始训练，总轮数: {trainer.training_config['epochs']}")

    for epoch in range(trainer.training_config['epochs']):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        for batch_X, batch_y in train_loader:
            batch_X = batch_X.to(device)
            batch_y = batch_y.to(device)

            optimizer.zero_grad()
            # PatchTST的earlysignaldet任务需要x_enc和x_dec参数
            # 对于早期信号检测，x_dec可以是空的或者与x_enc相同
            outputs = model(batch_X, None, batch_X, None)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_y.size(0)
            train_correct += (predicted == batch_y).sum().item()

        train_acc = train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X = batch_X.to(device)
                batch_y = batch_y.to(device)

                # PatchTST的earlysignaldet任务需要x_enc和x_dec参数
                outputs = model(batch_X, None, batch_X, None)
                loss = criterion(outputs, batch_y)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += batch_y.size(0)
                val_correct += (predicted == batch_y).sum().item()

        val_acc = val_correct / val_total
        avg_val_loss = val_loss / len(val_loader)

        # 记录历史
        train_history['train_loss'].append(avg_train_loss)
        train_history['train_acc'].append(train_acc)
        train_history['val_loss'].append(avg_val_loss)
        train_history['val_acc'].append(val_acc)

        print(f"Epoch {epoch+1:3d}/{trainer.training_config['epochs']}: "
              f"Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.4f}, "
              f"Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.4f}")

        # 早停检查
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1

        if patience_counter >= patience:
            print(f"早停触发，在第 {epoch+1} 轮停止训练")
            break

    # 加载最佳模型
    if best_model is not None:
        model.load_state_dict(best_model)

    print(f"训练完成，最佳验证准确率: {best_val_acc:.4f}")
    return model, train_history

def retrain_condition_model(condition_name):
    """重新训练指定工况的PatchTST模型"""
    print(f"\n{'='*60}")
    print(f"重新训练 {condition_name} PatchTST模型")
    print(f"{'='*60}")
    
    # 加载数据
    X, y = load_condition_data(condition_name)
    if X is None:
        print(f"跳过 {condition_name}：无数据")
        return False
    
    # 数据预处理
    print("数据预处理...")
    
    # 标准化
    scaler = StandardScaler()
    n_samples, seq_len, n_features = X.shape
    X_reshaped = X.reshape(-1, n_features)
    X_scaled = scaler.fit_transform(X_reshaped)
    X_scaled = X_scaled.reshape(n_samples, seq_len, n_features)
    
    # 划分训练集和验证集
    X_train, X_val, y_train, y_val = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {X_train.shape}, 验证集: {X_val.shape}")
    
    # 创建训练器
    trainer = ConditionSpecificTrainer(
        condition_name=condition_name,
        model_config={
            'seq_len': 180,
            'd_model': 128,
            'd_ff': 256,
            'n_heads': 8,
            'e_layers': 3,
            'dropout': 0.1,
            'patch_len': 16,
            'stride': 8,
            'n_classes': 2,
            'enc_in': 10,
            'c_out': 2,
        },
        training_config={
            'batch_size': 16,
            'learning_rate': 0.001,
            'epochs': 100,
            'patience': 10,
        }
    )
    
    # 创建PatchTST模型（强制不使用简化版）
    print("创建PatchTST模型...")
    model = trainer.create_model(force_simple=False)
    
    # 训练模型
    print("开始训练...")
    best_model, train_history = train_patchtst_model(
        model, X_train, y_train, X_val, y_val, trainer
    )
    
    # 评估模型
    print("评估模型...")
    best_model.eval()
    with torch.no_grad():
        X_val_tensor = torch.FloatTensor(X_val).to(trainer.device)
        val_outputs = best_model(X_val_tensor)
        val_predictions = torch.argmax(val_outputs, dim=1).cpu().numpy()
    
    val_accuracy = accuracy_score(y_val, val_predictions)
    print(f"验证准确率: {val_accuracy:.4f}")
    
    # 保存模型
    checkpoint_dir = Path("checkpoints") / condition_name
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    checkpoint = {
        'condition_name': condition_name,
        'epoch': len(train_history['train_loss']),
        'model_state_dict': best_model.state_dict(),
        'optimizer_state_dict': trainer.optimizer.state_dict(),
        'val_acc': val_accuracy,
        'model_config': trainer.model_config,
        'training_config': trainer.training_config,
        'train_history': train_history,
        'scaler_params': {
            'mean': scaler.mean_.tolist(),
            'scale': scaler.scale_.tolist()
        }
    }
    
    torch.save(checkpoint, checkpoint_dir / "best_model.pth")
    print(f"模型已保存到: {checkpoint_dir / 'best_model.pth'}")
    
    return True

def main():
    """主函数"""
    print("开始重新训练PatchTST模型")
    print("=" * 60)
    
    # 验证PatchTST可用性
    if not verify_patchtst_available():
        print("PatchTST不可用，退出")
        return
    
    # 要训练的工况列表
    conditions = ['正常钻进', '起钻', '下钻', '正划眼']
    
    success_count = 0
    for condition in conditions:
        try:
            if retrain_condition_model(condition):
                success_count += 1
        except Exception as e:
            print(f"{condition} 训练失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n训练完成！成功训练 {success_count}/{len(conditions)} 个模型")
    
    if success_count > 0:
        print("\n验证新训练的模型...")
        verify_new_models()

def verify_new_models():
    """验证新训练的模型"""
    conditions = ['正常钻进', '起钻', '下钻', '正划眼']
    
    for condition in conditions:
        checkpoint_path = Path("checkpoints") / condition / "best_model.pth"
        if checkpoint_path.exists():
            try:
                checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
                state_dict = checkpoint['model_state_dict']
                
                print(f"\n=== {condition} 模型验证 ===")
                print(f"权重层数量: {len(state_dict)}")
                
                # 检查是否是PatchTST模型
                if any('patch_embedding' in key for key in state_dict.keys()):
                    print("确认为PatchTST模型 (包含patch_embedding)")
                elif any('encoder.attn_layers' in key for key in state_dict.keys()):
                    print("确认为PatchTST模型 (包含encoder.attn_layers)")
                else:
                    print("可能仍是简化版模型")
                    
                print(f"验证准确率: {checkpoint['val_acc']:.4f}")
                
            except Exception as e:
                print(f"{condition} 模型验证失败: {e}")

if __name__ == "__main__":
    main()

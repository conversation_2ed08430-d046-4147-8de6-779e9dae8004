#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际钻进数据处理脚本

专门处理用户提供的实际钻进数据，解决工况标签频繁切换的问题
基于用户的具体需求和数据特点进行定制化处理

使用方法:
1. 将实际钻进数据CSV文件放在 ./dataset/earlysignaldetection/ 目录下
2. 运行此脚本进行预处理
3. 处理结果将保存在 ./processed_data/ 目录下

作者: TSlib项目组
日期: 2024-06-29
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import warnings
from datetime import datetime
# import matplotlib.pyplot as plt
# import seaborn as sns
from advanced_data_preprocessor import AdvancedDataPreprocessor
from typing import Dict, List

warnings.filterwarnings('ignore')

# 硬编码配置参数（用户偏好）
PROCESSING_CONFIG = {
    # 基础分割参数
    'min_segment_length': 30,          # 最小段长度（基于用户数据中最短工况持续时间）
    'max_segment_length': 300,         # 最大段长度
    'overlap_ratio': 0.1,              # 重叠比例
    'transition_buffer': 8,            # 转换缓冲区大小
    
    # 质量控制参数
    'min_quality_score': 0.65,         # 最小质量分数
    'enable_transition_removal': True,  # 启用转换区域移除
    'enable_short_segment_filtering': True,  # 启用短段过滤
    
    # 时间相关参数
    'min_condition_duration_seconds': 120,  # 最小工况持续秒数
    'min_condition_duration_points': 25,    # 最小工况持续点数
    
    # 数据列配置
    'required_columns': ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP'],
    'condition_column': None,  # 自动检测
    'timestamp_column': None,  # 自动检测
}

# 训练样本配置
TRAINING_CONFIG = {
    '起钻': {
        'target_samples': 500,
        'positive_ratio': 0.25,
        'min_quality': 0.7
    },
    '下钻': {
        'target_samples': 500,
        'positive_ratio': 0.25,
        'min_quality': 0.7
    },
    '正常钻进': {
        'target_samples': 800,
        'positive_ratio': 0.3,
        'min_quality': 0.65
    },
    '正划眼': {
        'target_samples': 300,
        'positive_ratio': 0.2,
        'min_quality': 0.7
    },
    '倒划眼': {
        'target_samples': 200,
        'positive_ratio': 0.15,
        'min_quality': 0.7
    }
}

# 数据路径配置
DATA_PATHS = {
    'input_dir': './dataset/earlysignaldetection/',
    'output_dir': './processed_data/',
    'segments_dir': './processed_data/segments/',
    'training_dir': './processed_data/training_samples/',
    'reports_dir': './processed_data/reports/'
}

def setup_directories():
    """创建必要的目录结构"""
    for path in DATA_PATHS.values():
        Path(path).mkdir(parents=True, exist_ok=True)
    print("✅ 目录结构创建完成")

def analyze_data_characteristics(data_dir: str) -> Dict:
    """分析数据特征"""
    print("🔍 分析数据特征...")
    
    data_path = Path(data_dir)
    csv_files = list(data_path.glob("**/*.csv"))
    
    if not csv_files:
        print(f"❌ 在 {data_dir} 中未找到CSV文件")
        return {}
    
    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    
    # 分析第一个文件的结构
    sample_file = csv_files[0]
    try:
        df = pd.read_csv(sample_file, encoding='utf-8', nrows=1000)  # 只读前1000行进行分析
        
        analysis = {
            'file_count': len(csv_files),
            'sample_file': str(sample_file),
            'columns': list(df.columns),
            'data_shape': df.shape,
            'column_types': df.dtypes.to_dict(),
        }
        
        # 检测工况列
        condition_candidates = []
        for col in df.columns:
            if df[col].dtype == 'object':
                unique_values = df[col].dropna().unique()
                if 2 <= len(unique_values) <= 20:  # 可能的工况列
                    condition_candidates.append({
                        'column': col,
                        'unique_values': list(unique_values),
                        'unique_count': len(unique_values)
                    })
        
        analysis['condition_candidates'] = condition_candidates
        
        # 检测时间列
        time_candidates = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['time', '时间', 'date', '日期']):
                time_candidates.append(col)
        
        analysis['time_candidates'] = time_candidates
        
        print(f"📊 数据分析完成:")
        print(f"  - 列数: {len(df.columns)}")
        print(f"  - 工况列候选: {[c['column'] for c in condition_candidates]}")
        print(f"  - 时间列候选: {time_candidates}")
        
        return analysis
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        return {}

def process_drilling_data():
    """处理钻进数据的主函数"""
    print("🚀 开始处理实际钻进数据")
    print("=" * 60)
    
    # 1. 设置目录
    setup_directories()
    
    # 2. 分析数据特征
    data_analysis = analyze_data_characteristics(DATA_PATHS['input_dir'])
    
    if not data_analysis:
        print("❌ 数据分析失败，请检查数据目录和文件格式")
        return
    
    # 保存数据分析结果
    with open(Path(DATA_PATHS['reports_dir']) / 'data_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(data_analysis, f, ensure_ascii=False, indent=2, default=str)
    
    # 3. 初始化预处理器
    print("\n🔧 初始化数据预处理器...")
    preprocessor = AdvancedDataPreprocessor(PROCESSING_CONFIG)
    
    # 4. 批量处理数据
    print("\n📄 开始批量处理数据...")
    try:
        processing_result = preprocessor.process_directory(
            DATA_PATHS['input_dir'],
            DATA_PATHS['segments_dir']
        )
        
        print(f"✅ 批量处理完成:")
        print(f"  - 处理文件: {processing_result['processed_files']}/{processing_result['total_files']}")
        print(f"  - 总段数: {processing_result['total_segments']}")
        
        # 显示工况分布
        print(f"  - 工况分布:")
        for condition, segments in processing_result['segments_by_condition'].items():
            print(f"    * {condition}: {len(segments)} 段")
        
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
        return
    
    # 5. 创建训练样本
    print("\n📊 创建训练样本...")
    all_segments = []
    for segments_list in processing_result['segments_by_condition'].values():
        all_segments.extend(segments_list)
    
    training_results = {}
    
    for target_condition, config in TRAINING_CONFIG.items():
        try:
            # 过滤高质量段
            quality_segments = [
                seg for seg in all_segments 
                if seg['quality_score'] >= config['min_quality']
            ]
            
            if not quality_segments:
                print(f"⚠️  没有找到质量足够的 {target_condition} 段")
                continue
            
            training_samples = preprocessor.create_training_samples(
                quality_segments,
                target_condition=target_condition,
                positive_ratio=config['positive_ratio']
            )
            
            training_results[target_condition] = training_samples
            
            # 保存训练样本
            condition_dir = Path(DATA_PATHS['training_dir']) / target_condition
            condition_dir.mkdir(exist_ok=True)
            
            # 保存正样本
            for i, segment in enumerate(training_samples['positive_samples']):
                segment_file = condition_dir / f"positive_{i:04d}.csv"
                segment['data'].to_csv(segment_file, index=False, encoding='utf-8')
            
            # 保存负样本
            for i, segment in enumerate(training_samples['negative_samples']):
                segment_file = condition_dir / f"negative_{i:04d}.csv"
                segment['data'].to_csv(segment_file, index=False, encoding='utf-8')
            
            # 保存样本信息
            sample_info_file = condition_dir / 'sample_info.json'
            with open(sample_info_file, 'w', encoding='utf-8') as f:
                json.dump(training_samples['sample_info'], f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ {target_condition} 训练样本创建完成:")
            print(f"    正样本: {training_samples['sample_info']['n_positive']}")
            print(f"    负样本: {training_samples['sample_info']['n_negative']}")
            print(f"    正样本比例: {training_samples['sample_info']['positive_ratio']:.3f}")
            
        except Exception as e:
            print(f"❌ {target_condition} 训练样本创建失败: {e}")
    
    # 6. 生成处理报告
    print("\n📋 生成处理报告...")
    report_file = Path(DATA_PATHS['reports_dir']) / 'processing_report.md'
    report_content = preprocessor.generate_preprocessing_report(str(report_file))
    
    # 7. 生成可视化报告
    generate_visualization_report(processing_result, training_results)
    
    # 8. 生成使用指南
    generate_usage_guide(processing_result, training_results)
    
    print("\n" + "=" * 60)
    print("🎉 数据处理完成!")
    print(f"📁 处理结果保存在: {DATA_PATHS['output_dir']}")
    print(f"📊 详细报告: {report_file}")
    
    return {
        'processing_result': processing_result,
        'training_results': training_results,
        'data_analysis': data_analysis
    }

def generate_visualization_report(processing_result: Dict, training_results: Dict):
    """生成可视化报告"""
    print("📈 生成可视化报告...")

    try:
        import matplotlib.pyplot as plt
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('钻进数据预处理结果分析', fontsize=16, fontweight='bold')
        
        # 1. 工况分布饼图
        condition_counts = {k: len(v) for k, v in processing_result['segments_by_condition'].items()}
        if condition_counts:
            axes[0, 0].pie(condition_counts.values(), labels=condition_counts.keys(), autopct='%1.1f%%')
            axes[0, 0].set_title('工况段分布')
        
        # 2. 质量分数分布
        all_segments = []
        for segments_list in processing_result['segments_by_condition'].values():
            all_segments.extend(segments_list)
        
        if all_segments:
            quality_scores = [seg['quality_score'] for seg in all_segments]
            axes[0, 1].hist(quality_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 1].set_title('质量分数分布')
            axes[0, 1].set_xlabel('质量分数')
            axes[0, 1].set_ylabel('段数量')
        
        # 3. 段长度分布
        if all_segments:
            segment_lengths = [seg['length'] for seg in all_segments]
            axes[1, 0].hist(segment_lengths, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
            axes[1, 0].set_title('段长度分布')
            axes[1, 0].set_xlabel('段长度（数据点）')
            axes[1, 0].set_ylabel('段数量')
        
        # 4. 训练样本统计
        if training_results:
            conditions = list(training_results.keys())
            positive_counts = [training_results[c]['sample_info']['n_positive'] for c in conditions]
            negative_counts = [training_results[c]['sample_info']['n_negative'] for c in conditions]
            
            x = np.arange(len(conditions))
            width = 0.35
            
            axes[1, 1].bar(x - width/2, positive_counts, width, label='正样本', color='orange', alpha=0.7)
            axes[1, 1].bar(x + width/2, negative_counts, width, label='负样本', color='lightcoral', alpha=0.7)
            axes[1, 1].set_title('训练样本统计')
            axes[1, 1].set_xlabel('工况类型')
            axes[1, 1].set_ylabel('样本数量')
            axes[1, 1].set_xticks(x)
            axes[1, 1].set_xticklabels(conditions, rotation=45)
            axes[1, 1].legend()
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = Path(DATA_PATHS['reports_dir']) / 'processing_visualization.png'
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 可视化报告已保存: {chart_file}")

    except ImportError:
        print("⚠️  matplotlib未安装，跳过可视化报告生成")
    except Exception as e:
        print(f"⚠️  可视化报告生成失败: {e}")

def generate_usage_guide(processing_result: Dict, training_results: Dict):
    """生成使用指南"""
    print("📖 生成使用指南...")
    
    guide_content = f"""# 钻进数据预处理结果使用指南

## 处理概览
- 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 处理文件数: {processing_result['processed_files']}/{processing_result['total_files']}
- 总段数: {processing_result['total_segments']}

## 工况分布
"""
    
    for condition, segments in processing_result['segments_by_condition'].items():
        guide_content += f"- **{condition}**: {len(segments)} 段\n"
    
    guide_content += f"""
## 训练样本统计
"""
    
    for condition, result in training_results.items():
        info = result['sample_info']
        guide_content += f"""
### {condition}
- 正样本: {info['n_positive']} 个
- 负样本: {info['n_negative']} 个  
- 正样本比例: {info['positive_ratio']:.3f}
- 平均质量分数: {info['avg_positive_quality']:.3f}
"""
    
    guide_content += f"""
## 使用方法

### 1. 训练模型
```python
# 使用处理后的训练样本训练PatchTST模型
from run_earlysignaldet_simple import create_args_for_earlysignaldet
from exp.exp_earlysignaldet import Exp_Earlysignaldet

# 为特定工况创建配置
args = create_args_for_earlysignaldet()
args.data_path = './processed_data/training_samples/起钻/'  # 指定工况目录

# 初始化实验
exp = Exp_Earlysignaldet(args)

# 训练模型
exp.train()
```

### 2. 数据质量检查
- 所有段的质量分数都 >= {PROCESSING_CONFIG['min_quality_score']}
- 段长度在 {PROCESSING_CONFIG['min_segment_length']}-{PROCESSING_CONFIG['max_segment_length']} 之间
- 已移除工况转换区域的数据点

### 3. 注意事项
1. **工况标签**: 已标准化处理，统一为5种标准工况
2. **数据完整性**: 已过滤缺失值过多的段
3. **时间连续性**: 已验证时间序列的连续性
4. **参数合理性**: 已验证钻进参数与工况的一致性

### 4. 文件结构
```
processed_data/
├── segments/           # 按工况分组的段数据
│   ├── 起钻/
│   ├── 下钻/
│   └── ...
├── training_samples/   # 训练样本
│   ├── 起钻/
│   │   ├── positive_0000.csv
│   │   ├── negative_0000.csv
│   │   └── sample_info.json
│   └── ...
└── reports/           # 处理报告
    ├── processing_report.md
    ├── data_analysis.json
    └── processing_visualization.png
```

## 下一步建议
1. 使用 `run_multi_condition_earlysignaldet.py` 进行多工况模型训练
2. 根据实际需求调整训练样本比例
3. 定期验证模型性能并更新训练数据
"""
    
    # 保存使用指南
    guide_file = Path(DATA_PATHS['reports_dir']) / 'usage_guide.md'
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"📖 使用指南已保存: {guide_file}")

def main():
    """主函数"""
    try:
        result = process_drilling_data()
        
        print("\n🎯 处理完成总结:")
        print(f"✅ 成功处理 {result['processing_result']['total_segments']} 个数据段")
        print(f"✅ 创建了 {len(result['training_results'])} 种工况的训练样本")
        print(f"✅ 生成了完整的处理报告和使用指南")
        
        print("\n💡 下一步操作建议:")
        print("1. 查看 ./processed_data/reports/usage_guide.md 了解详细使用方法")
        print("2. 使用 run_multi_condition_earlysignaldet.py 开始模型训练")
        print("3. 根据需要调整 PROCESSING_CONFIG 和 TRAINING_CONFIG 参数")
        
        return result
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行主处理流程
    result = main()
    
    if result:
        print("\n🚀 数据预处理成功完成!")
        print("现在可以使用处理后的数据进行模型训练了。")
    else:
        print("\n❌ 数据预处理失败，请检查错误信息并重试。")

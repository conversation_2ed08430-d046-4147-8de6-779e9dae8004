<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b467d168-d934-4378-a481-423abfab73aa" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2YcBG1vj1vncx3EQgnD2bjF8Cm3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.12.executor": "Run",
    "Python.22.executor": "Run",
    "Python.Embed.executor": "Debug",
    "Python.data_loader.executor": "Debug",
    "Python.exp_earlysignaldet.executor": "Run",
    "Python.minirockettest.executor": "Run",
    "Python.run.executor": "Run",
    "Python.test1.executor": "Run",
    "Python.test2.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/PyCharm/kazuan/kazuan-6.29",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main\dataset\泸203H11-5" />
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main\dataset\泸203H123-4" />
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main\dataset" />
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main\layers" />
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Downloads\Time-Series-Library-main\dataset\泸203H123-4" />
    </key>
  </component>
  <component name="RunManager" selected="Python.run">
    <configuration name="Embed" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/layers" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/layers/Embed.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Embed" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/layers" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/layers/Embed.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="data_loader" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/data_provider" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/data_provider/data_loader.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="exp_earlysignaldet" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/exp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/exp/exp_earlysignaldet.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="exp_earlysignaldet" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/exp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/exp/exp_earlysignaldet.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="minirockettest" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/minirockettest.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/run.py" />
      <option name="PARAMETERS" value="--task_name earlysignaldet --is_training 1 --root_path ./dataset/earlysignaldetection --model_id earlysignaldetection --model PatchTST --data Earlysignaldet --e_layers 3 --batch_size 16 --d_model 128 --d_ff 256 --top_k 3 --des 'Exp' --itr 1 --learning_rate 0.001 --train_epochs 100 --patience 10 --do_predict" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/run.py" />
      <option name="PARAMETERS" value="--task_name earlysignaldet --is_training 1 --root_path ./dataset/earlysignaldetection --model_id earlysignaldetection --model PatchTST --data Earlysignaldet --e_layers 3 --batch_size 16 --d_model 128 --d_ff 256 --top_k 3 --des 'Exp' --itr 1 --learning_rate 0.001 --train_epochs 100 --patience 10 --do_predict" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Time-Series-Library-main" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.Embed" />
      <item itemvalue="Python.data_loader" />
      <item itemvalue="Python.exp_earlysignaldet" />
      <item itemvalue="Python.minirockettest" />
      <item itemvalue="Python.run" />
      <item itemvalue="Python.test2" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.run" />
        <item itemvalue="Python.exp_earlysignaldet" />
        <item itemvalue="Python.minirockettest" />
        <item itemvalue="Python.data_loader" />
        <item itemvalue="Python.Embed" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b467d168-d934-4378-a481-423abfab73aa" name="Changes" comment="" />
      <created>1700814889245</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1700814889245</updated>
      <workItem from="1720161563544" duration="55000" />
      <workItem from="1720161629587" duration="16000" />
      <workItem from="1720161661929" duration="1052000" />
      <workItem from="1720162722359" duration="1214000" />
      <workItem from="1720163944470" duration="611000" />
      <workItem from="1720164564875" duration="330000" />
      <workItem from="1720164903538" duration="14000" />
      <workItem from="1720164948986" duration="424000" />
      <workItem from="1720165383451" duration="1452000" />
      <workItem from="1720167950220" duration="228000" />
      <workItem from="1720168186507" duration="2033000" />
      <workItem from="1720170666462" duration="4273000" />
      <workItem from="1720361212344" duration="14053000" />
      <workItem from="1720498652994" duration="3539000" />
      <workItem from="1720683875285" duration="620000" />
      <workItem from="1721266616974" duration="1378000" />
      <workItem from="1722237963279" duration="369000" />
      <workItem from="1722243430651" duration="3380000" />
      <workItem from="1722672079697" duration="3196000" />
      <workItem from="1722757264623" duration="1122000" />
      <workItem from="1722766921622" duration="3045000" />
      <workItem from="1723084141620" duration="7780000" />
      <workItem from="1723194510035" duration="8000" />
      <workItem from="1723195657836" duration="652000" />
      <workItem from="1723340587774" duration="5917000" />
      <workItem from="1723792740567" duration="5697000" />
      <workItem from="1723800534058" duration="1226000" />
      <workItem from="1723897075282" duration="1297000" />
      <workItem from="1723942893013" duration="3713000" />
      <workItem from="1724029613580" duration="3767000" />
      <workItem from="1724504650772" duration="1299000" />
      <workItem from="1725435463864" duration="923000" />
      <workItem from="1725506807318" duration="513000" />
      <workItem from="1725630028852" duration="3404000" />
      <workItem from="1725779965318" duration="10865000" />
      <workItem from="1727180858361" duration="608000" />
      <workItem from="1727681042391" duration="1401000" />
      <workItem from="1727854882574" duration="4811000" />
      <workItem from="1728030415402" duration="8103000" />
      <workItem from="1728268568980" duration="14251000" />
      <workItem from="1729909884883" duration="1718000" />
      <workItem from="1731379360924" duration="1871000" />
      <workItem from="1731396376765" duration="1448000" />
      <workItem from="1731569492682" duration="519000" />
      <workItem from="1731982225187" duration="615000" />
      <workItem from="1732005805933" duration="5157000" />
      <workItem from="1732199279456" duration="15765000" />
      <workItem from="1733209922757" duration="11133000" />
      <workItem from="1733882550423" duration="117000" />
      <workItem from="1733882687705" duration="8454000" />
      <workItem from="1734436945526" duration="970000" />
      <workItem from="1734659806013" duration="770000" />
      <workItem from="1735356787913" duration="11638000" />
      <workItem from="1735471621576" duration="4328000" />
      <workItem from="1735782865332" duration="7073000" />
      <workItem from="1738569297264" duration="1407000" />
      <workItem from="1739280937960" duration="2000" />
      <workItem from="1739348392040" duration="62175000" />
      <workItem from="1740300504275" duration="18629000" />
      <workItem from="1740640889542" duration="34585000" />
      <workItem from="1740921426109" duration="24600000" />
      <workItem from="1741243505520" duration="6434000" />
      <workItem from="1741506636916" duration="1003000" />
      <workItem from="1741571920758" duration="4569000" />
      <workItem from="1747021093669" duration="366000" />
      <workItem from="1751196323131" duration="1082000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_anomaly_detection.py</url>
          <line>129</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_anomaly_detection.py</url>
          <line>174</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_anomaly_detection.py</url>
          <line>181</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_classification.py</url>
          <line>110</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_classification.py</url>
          <line>101</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/Crossformer.py</url>
          <line>83</line>
          <option name="timeStamp" value="37" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_classification.py</url>
          <line>23</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_earlysignaldet.py</url>
          <line>307</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_earlysignaldet.py</url>
          <line>298</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/DLinear.py</url>
          <line>94</line>
          <option name="timeStamp" value="62" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_earlysignaldet.py</url>
          <line>51</line>
          <option name="timeStamp" value="85" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/utils/losses.py</url>
          <line>167</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/utils/losses.py</url>
          <line>149</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/DLinear.py</url>
          <line>89</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/DLinear.py</url>
          <line>117</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data_provider/data_loader.py</url>
          <line>1292</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data_provider/data_loader.py</url>
          <line>1287</line>
          <option name="timeStamp" value="114" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/minirockettest.py</url>
          <line>38</line>
          <option name="timeStamp" value="149" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/minirockettest.py</url>
          <line>60</line>
          <option name="timeStamp" value="152" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/minirockettest.py</url>
          <line>54</line>
          <option name="timeStamp" value="154" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>212</line>
          <option name="timeStamp" value="158" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>184</line>
          <option name="timeStamp" value="159" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>121</line>
          <option name="timeStamp" value="160" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>104</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>115</line>
          <option name="timeStamp" value="162" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/TimesNet.py</url>
          <line>111</line>
          <option name="timeStamp" value="163" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_anomaly_detection.py</url>
          <line>177</line>
          <option name="timeStamp" value="169" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_anomaly_detection.py</url>
          <line>158</line>
          <option name="timeStamp" value="192" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_earlysignaldet.py</url>
          <line>92</line>
          <option name="timeStamp" value="199" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/PatchTST.py</url>
          <line>69</line>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/exp/exp_earlysignaldet.py</url>
          <line>76</line>
          <option name="timeStamp" value="204" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/MyLSTM.py</url>
          <line>94</line>
          <option name="timeStamp" value="205" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$test1.coverage" NAME="test1 Coverage Results" MODIFIED="1722757316212" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$run.coverage" NAME="run Coverage Results" MODIFIED="1731569575119" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PatchTST_py$data_loader.coverage" NAME="data_loader Coverage Results" MODIFIED="1732437643797" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data_provider" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$22.coverage" NAME="22 Coverage Results" MODIFIED="1722767329857" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PatchTST_py$minirockettest.coverage" NAME="minirockettest Coverage Results" MODIFIED="1735471636892" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PatchTST_py$exp_earlysignaldet.coverage" NAME="exp_earlysignaldet Coverage Results" MODIFIED="1741012051623" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/exp" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$test2.coverage" NAME="test2 Coverage Results" MODIFIED="1723088826410" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PatchTST_py$run.coverage" NAME="run Coverage Results" MODIFIED="1741574384560" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$Embed.coverage" NAME="Embed Coverage Results" MODIFIED="1723897420725" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/layers" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$12.coverage" NAME="12 Coverage Results" MODIFIED="1722757285074" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$data_loader.coverage" NAME="data_loader Coverage Results" MODIFIED="1728302554019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data_provider" />
    <SUITE FILE_PATH="coverage/Time_Series_Library_main$exp_earlysignaldet.coverage" NAME="exp_earlysignaldet Coverage Results" MODIFIED="1723346124556" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/exp" />
  </component>
</project>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新训练的PatchTST模型
验证模型架构和预测性能
"""

import os
import torch
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_patchtst_model(condition_name):
    """加载PatchTST模型"""
    from condition_specific_trainer import ConditionSpecificTrainer
    
    checkpoint_path = Path("checkpoints") / condition_name / "best_model.pth"
    if not checkpoint_path.exists():
        print(f"模型文件不存在: {checkpoint_path}")
        return None, None
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 创建训练器和模型
    trainer = ConditionSpecificTrainer(condition_name)
    model = trainer.create_model(force_simple=False)
    
    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"成功加载 {condition_name} PatchTST模型")
    print(f"   - 验证准确率: {checkpoint['val_acc']:.4f}")
    print(f"   - 训练轮数: {checkpoint['epoch']}")
    print(f"   - 模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    return model, checkpoint

def verify_model_architecture(model, condition_name):
    """验证模型架构"""
    print(f"\n=== {condition_name} 模型架构验证 ===")
    
    # 检查是否为PatchTST
    has_patch_embedding = any('patch_embedding' in name for name, _ in model.named_modules())
    has_encoder = any('encoder' in name for name, _ in model.named_modules())
    has_projection = any('projection' in name for name, _ in model.named_modules())
    
    if has_patch_embedding and has_encoder and has_projection:
        print("确认为PatchTST架构")
        print(f"   - 包含patch_embedding: {has_patch_embedding}")
        print(f"   - 包含encoder: {has_encoder}")
        print(f"   - 包含projection: {has_projection}")
    else:
        print("不是PatchTST架构")
        return False
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"   - 总参数数: {total_params:,}")
    print(f"   - 可训练参数: {trainable_params:,}")
    
    return True

def load_test_data(condition_name, max_samples=100):
    """加载测试数据"""
    test_data_dir = Path("dataset/processed_data/test_data")
    test_files = list(test_data_dir.glob("*.csv"))
    
    if not test_files:
        print("未找到测试数据文件")
        return None, None
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    X_test = []
    y_test = []
    file_info = []
    
    for i, file_path in enumerate(test_files[:max_samples]):
        try:
            df = pd.read_csv(file_path)
            
            # 检查工况
            if 'RIGSTA' in df.columns:
                file_condition = df['RIGSTA'].iloc[0] if len(df) > 0 else "未知"
                if file_condition != condition_name:
                    continue  # 跳过不匹配的工况
            
            # 特征列
            feature_cols = ['DEP', 'BITDEP', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'HOKHEI', 'DRITIME', 'CSIP']
            
            # 分割成3分钟窗口
            window_size = 180
            for start_idx in range(0, len(df) - window_size + 1, window_size):
                window_data = df.iloc[start_idx:start_idx + window_size]
                
                if len(window_data) == window_size:
                    features = window_data[feature_cols].values
                    X_test.append(features)
                    y_test.append(0)  # 假设测试数据都是正常的
                    file_info.append({
                        'file': file_path.name,
                        'window': start_idx // window_size,
                        'condition': file_condition
                    })
                    
        except Exception as e:
            print(f"处理文件失败 {file_path.name}: {e}")
            continue
    
    if not X_test:
        print(f"未找到 {condition_name} 工况的测试数据")
        return None, None, None
    
    X_test = np.array(X_test)
    y_test = np.array(y_test)
    
    print(f"加载了 {len(X_test)} 个测试样本")
    return X_test, y_test, file_info

def test_model_prediction(model, X_test, y_test, condition_name):
    """测试模型预测"""
    print(f"\n=== {condition_name} 模型预测测试 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 标准化数据
    scaler = StandardScaler()
    X_test_scaled = scaler.fit_transform(X_test.reshape(-1, X_test.shape[-1])).reshape(X_test.shape)
    
    # 预测
    model.eval()
    predictions = []
    probabilities = []
    
    with torch.no_grad():
        for i in range(0, len(X_test_scaled), 32):  # 批处理
            batch = X_test_scaled[i:i+32]
            batch_tensor = torch.FloatTensor(batch).to(device)
            
            # PatchTST预测
            outputs = model(batch_tensor, None, batch_tensor, None)
            probs = torch.softmax(outputs, dim=1)
            preds = torch.argmax(outputs, dim=1)
            
            predictions.extend(preds.cpu().numpy())
            probabilities.extend(probs.cpu().numpy())
    
    predictions = np.array(predictions)
    probabilities = np.array(probabilities)
    
    # 计算指标
    accuracy = accuracy_score(y_test, predictions)
    
    print(f"测试样本数: {len(X_test)}")
    print(f"预测准确率: {accuracy:.4f}")
    print(f"异常检出率: {np.mean(predictions):.4f}")
    print(f"平均异常概率: {np.mean(probabilities[:, 1]):.4f}")
    
    return predictions, probabilities

def main():
    """主函数"""
    print("开始测试新训练的PatchTST模型")
    print("=" * 60)
    
    # 测试的工况
    conditions = ['正常钻进', '起钻', '下钻', '正划眼']
    
    results = {}
    
    for condition in conditions:
        print(f"\n{'='*60}")
        print(f"测试 {condition} 工况模型")
        print(f"{'='*60}")
        
        # 加载模型
        model, checkpoint = load_patchtst_model(condition)
        if model is None:
            continue
        
        # 验证架构
        if not verify_model_architecture(model, condition):
            continue
        
        # 加载测试数据
        result = load_test_data(condition, max_samples=20)
        if result[0] is None:
            continue
        X_test, y_test, file_info = result
        
        # 测试预测
        predictions, probabilities = test_model_prediction(model, X_test, y_test, condition)
        
        results[condition] = {
            'model': model,
            'checkpoint': checkpoint,
            'predictions': predictions,
            'probabilities': probabilities,
            'test_data': (X_test, y_test),
            'file_info': file_info
        }
    
    print(f"\n{'='*60}")
    print("测试完成！")
    print(f"成功测试了 {len(results)} 个PatchTST模型")
    
    # 总结
    for condition, result in results.items():
        checkpoint = result['checkpoint']
        predictions = result['predictions']
        probabilities = result['probabilities']
        
        print(f"\n{condition}:")
        print(f"  - 验证准确率: {checkpoint['val_acc']:.4f}")
        print(f"  - 测试样本数: {len(predictions)}")
        print(f"  - 异常检出率: {np.mean(predictions):.4f}")
        print(f"  - 平均异常概率: {np.mean(probabilities[:, 1]):.4f}")

if __name__ == "__main__":
    main()

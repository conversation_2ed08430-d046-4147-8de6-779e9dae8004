import sqlite3
import csv
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import os
import calendar

def export_filtered_data(db_file, output_folder, start_time, end_time, base_time):
    """
    从数据库文件中提取数据并导出为CSV
    
    参数:
        db_file: 数据库文件路径
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        base_time: 基准时间，用于参考
        
    返回:
        (成功标志, 首行时间, 尾行时间, 数据行数)
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    try:
        db_path = Path(db_file)
        if not db_path.exists():
            print(f"❌ 数据库文件不存在: {db_file}")
            return False, "", "", 0
            
        parent_folder_name = db_path.parent.name
        
        # 连接数据库执行查询
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # SQL查询
        sql = f"""
        SELECT 
            DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
            FLOWIN, FLOWOUT, SPP, CSIP, RIGSTA, (WELLDATE || ' ' || WELLTIME) AS date
        FROM data
        WHERE datetime(WELLDATE || ' ' || WELLTIME) 
            BETWEEN '{start_time}' AND '{end_time}'
        ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
        """

        cursor.execute(sql)
        rows = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        if not rows:
            print(f"⏩ 无数据: {db_path.name}")
            conn.close()
            return False, "", "", 0

        # 获取首行和尾行时间
        first_row_time = rows[0][0]
        last_row_time = rows[-1][0]

        # 构建输出文件名
        output_name = f"{parent_folder_name}_[{time_range}].csv"
        csv_path = Path(output_folder) / output_name
        
        # 写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(rows)
        
        conn.close()
        
        print(f"✅ 导出成功: {csv_path} (共 {len(rows)} 条数据)")
        print(f"   首行时间: {first_row_time}")
        print(f"   尾行时间: {last_row_time}")
        return True, first_row_time, last_row_time, len(rows)
        
    except sqlite3.Error as e:
        print(f"❌ 数据库错误 ({db_file}): {str(e)}")
    except Exception as e:
        print(f"❌ 错误 ({db_file}): {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()
    
    return False, "", "", 0

def export_filtered_data_cross_month(well_path, output_folder, start_time, end_time, base_time):
    """
    处理跨月时间范围的数据导出，查询多个月份的数据库文件并合并结果
    
    参数:
        well_path: 井文件夹路径
        output_folder: 输出文件夹路径
        start_time: 开始时间
        end_time: 结束时间
        base_time: 基准时间
        
    返回:
        (成功标志, 首行时间, 尾行时间, 数据行数)
    """
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # 将时间范围格式化为YYYYMMDD-YYYYMMDD（如20220528-20220529）
    def format_time_range(start, end):
        try:
            start_clean = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").strftime("%y%m%d%H%M")
            end_clean = datetime.strptime(end, "%Y-%m-%d %H:%M:%S").strftime("%d%H%M")
            return f"{start_clean}-{end_clean}"
        except ValueError:
            raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

    time_range = format_time_range(start_time, end_time)
    
    # 解析时间范围
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    
    # 确定需要查询的所有月份
    months_to_query = []
    current = datetime(start_dt.year, start_dt.month, 1)
    
    while current <= end_dt:
        months_to_query.append((current.year, current.month))
        # 移动到下个月
        if current.month == 12:
            current = datetime(current.year + 1, 1, 1)
        else:
            current = datetime(current.year, current.month + 1, 1)
    
    # 从每个月份的数据库中提取数据
    all_rows = []
    columns = None
    parent_folder_name = Path(well_path).name
    
    for year, month in months_to_query:
        # 生成数据库文件名: 0YYMM.db
        db_filename = f"0{str(year)[2:4]}{month:02d}.db"
        db_path = os.path.join(well_path, db_filename)
        
        if not os.path.exists(db_path):
            print(f"⚠️ 数据库文件不存在，跳过: {db_path}")
            continue
        
        try:
            # 连接数据库执行查询
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # SQL查询
            sql = f"""
            SELECT 
                DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, 
                FLOWIN, FLOWOUT, SPP, CSIP, RIGSTA, (WELLDATE || ' ' || WELLTIME) AS date
            FROM data
            WHERE datetime(WELLDATE || ' ' || WELLTIME) 
                BETWEEN '{start_time}' AND '{end_time}'
            ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
            """

            cursor.execute(sql)
            rows = cursor.fetchall()
            
            if not columns:
                columns = [desc[0] for desc in cursor.description]
            
            if rows:
                all_rows.extend(rows)
                print(f"✅ 从 {db_filename} 提取了 {len(rows)} 条数据")
            else:
                print(f"⏩ 无数据: {db_filename}")
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"❌ 数据库错误 ({db_path}): {str(e)}")
        except Exception as e:
            print(f"❌ 错误 ({db_path}): {str(e)}")
    
    # 如果有数据，写入CSV
    if all_rows and columns:
        # 过滤掉时间列为None的行，然后按时间排序
        all_rows = [row for row in all_rows if row[13] is not None]
        if not all_rows:  # 如果过滤后没有数据了
            print(f"❌ 过滤后没有有效数据（所有时间列都为None）")
            return False, "", "", 0
        all_rows.sort(key=lambda row: row[13])
        
        # 获取首行和尾行时间
        first_row_time = all_rows[0][0]
        last_row_time = all_rows[-1][0]
        
        # 构建输出文件名
        output_name = f"{parent_folder_name}_[{time_range}].csv"
        csv_path = Path(output_folder) / output_name
        
        # 写入CSV文件
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(all_rows)
        
        print(f"✅ 合并导出成功: {csv_path} (共 {len(all_rows)} 条数据)")
        print(f"   首行时间: {first_row_time}")
        print(f"   尾行时间: {last_row_time}")
        return True, first_row_time, last_row_time, len(all_rows)
    else:
        print(f"❌ 没有找到任何数据")
        return False, "", "", 0

def calculate_time_range(time_base):
    """
    根据基准时间计算时间范围：
    - 开始时间：基准时间前两天
    - 结束时间：基准时间后一天
    
    参数:
        time_base: 字符串，格式为 "YYYY-MM-DD HH:MM:SS"
    
    返回:
        start_time, end_time: 两个字符串，格式同上
    """
    try:
        # 将字符串转换为datetime对象
        base_time = datetime.strptime(time_base, "%Y-%m-%d %H:%M:%S")
        
        # 计算开始时间（前两天）
        start_time = base_time - timedelta(days=1)
        
        # 计算结束时间（后一天）
        end_time = base_time + timedelta(hours=1)
        
        # 转换回字符串格式
        return start_time.strftime("%Y-%m-%d %H:%M:%S"), end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

def generate_db_filename(date_time):
    """
    根据日期时间生成数据库文件名
    文件名格式为: 0YYMM.db (如2022年4月 -> 02204.db)
    """
    try:
        dt = datetime.strptime(date_time, "%Y-%m-%d %H:%M:%S")
        return f"0{dt.strftime('%y%m')}.db"
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD HH:MM:SS'")

def is_cross_month(start_time, end_time):
    """
    判断时间范围是否跨越了月份
    
    参数:
        start_time: 开始时间字符串
        end_time: 结束时间字符串
    
    返回:
        是否跨月
    """
    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    
    if start_dt.year == end_dt.year and start_dt.month == end_dt.month:
        return False
    return True

def load_wells_from_csv(csv_file):
    """
    从CSV文件加载井名和时间数据
    
    参数:
        csv_file: CSV文件路径，需要包含"井名"和"时间"两列
        
    返回:
        井名和时间的列表，格式为 [[井名1, 时间1], [井名2, 时间2], ...]
    """
    wells_data = []
    
    try:
        # 尝试使用pandas读取
        df = pd.read_csv(csv_file, encoding='utf-8')
        
        # 检查是否包含必要的列
        required_columns = ["井名", "时间"]
        for col in required_columns:
            if col not in df.columns:
                print(f"❌ CSV文件缺少必要的列: {col}")
                return wells_data
        
        # 读取数据
        for _, row in df.iterrows():
            wells_data.append([row['井名'], row['时间']])
        
        print(f"✅ 从CSV文件成功读取了{len(wells_data)}条井名和时间数据")
        
    except Exception as e:
        print(f"❌ 读取CSV文件出错: {str(e)}")
        # 尝试使用基本的csv模块读取
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader)  # 读取表头
                
                # 查找井名和时间列的索引
                well_idx, time_idx = -1, -1
                for i, header in enumerate(headers):
                    if header == "井名":
                        well_idx = i
                    elif header == "时间":
                        time_idx = i
                
                if well_idx == -1 or time_idx == -1:
                    print(f"❌ CSV文件缺少必要的列: 井名或时间")
                    return wells_data
                
                # 读取数据
                for row in reader:
                    if len(row) > max(well_idx, time_idx):
                        wells_data.append([row[well_idx], row[time_idx]])
            
            print(f"✅ 从CSV文件成功读取了{len(wells_data)}条井名和时间数据")
            
        except Exception as e2:
            print(f"❌ 使用基本csv模块读取也失败: {str(e2)}")
    
    return wells_data

if __name__ == "__main__":
    # ===== 基础配置 =====
    BASE_PATH = r"D:\采数据\PROCESS"  # 数据库文件的根目录
    OUTPUT_FOLDER = r"D:\采数据\PROCESS\clean630"  # 所有文件输出到此目录
    
    # ===== 从CSV读取井名和时间 =====
    # 优先从井名时间表.csv读取数据
    csv_file = os.path.join(os.path.dirname(__file__), "井名时间---修正.csv")
    
    if os.path.exists(csv_file):
        print(f"✅ 发现井名时间表: {csv_file}")
        WELLS_DATA_LIST = load_wells_from_csv(csv_file)
    else:
        print(f"⚠️ 未找到井名时间表CSV文件: {csv_file}")
        print(f"⚠️ 将使用示例数据，建议创建CSV文件以便批量处理")
        
        # 使用示例数据
        WELLS_DATA_LIST = [
            ["自201H35-2", "2022-03-04 17:33:08"],
            ["泸203H153-1", "2022-01-17 05:00:00"],
            ["自225", "2022-01-26 19:51:00"],
            # 仅保留几个示例数据
        ]
    
    if not WELLS_DATA_LIST:
        print("❌ 没有数据可处理，程序退出")
        exit(1)
    
    # 转换成字典用于向后兼容
    WELLS_DATA = {item[0]: item[1] for item in WELLS_DATA_LIST}
    
    # ===== 开始批处理 =====
    print(f"\n{'='*30} 开始批处理 {'='*30}")
    print(f"共有 {len(WELLS_DATA_LIST)} 条井名时间数据需要处理")
    
    # 使用列表数据处理每一个井名和时间点
    results = []
    
    for well_data in WELLS_DATA_LIST:
        well_name, time_base = well_data
        
        print(f"\n{'='*50}")
        print(f"⏳ 正在处理: {well_name}, 基准时间: {time_base}")
        
        # 计算时间范围
        start_time, end_time = calculate_time_range(time_base)
        
        print(f"📅 时间范围: {start_time} 至 {end_time}")
        
        # 井的路径
        well_path = os.path.join(BASE_PATH, well_name)
        
        # 用于存储首行和尾行时间以及数据行数
        first_row_time = ""
        last_row_time = ""
        row_count = 0
        
        # 检查是否跨月
        if is_cross_month(start_time, end_time):
            print(f"🔄 检测到跨月时间范围，将查询多个月份的数据")
            success, first_row_time, last_row_time, row_count = export_filtered_data_cross_month(
                well_path=well_path,
                output_folder=OUTPUT_FOLDER,
                start_time=start_time,
                end_time=end_time,
                base_time=time_base
            )
        else:
            # 单月处理
            db_filename = generate_db_filename(time_base)
            db_path = os.path.join(well_path, db_filename)
            
            print(f"📂 数据库文件: {db_path}")
            
            success, first_row_time, last_row_time, row_count = export_filtered_data(
                db_file=db_path,
                output_folder=OUTPUT_FOLDER,
                start_time=start_time,
                end_time=end_time,
                base_time=time_base
            )
        
        # 记录结果，不再在井名中添加时间戳
        results.append({
            "井名": well_name,  # 不再添加时间戳后缀
            "基准时间": time_base,
            "时间范围": f"{start_time} 至 {end_time}",
            "跨月": "是" if is_cross_month(start_time, end_time) else "否",
            "处理结果": "成功" if success else "失败",
            "数据首行时间": first_row_time,
            "数据尾行时间": last_row_time,
            "数据行数": row_count  # 添加数据行数
        })
    
    # 生成处理报告
    report_path = os.path.join(OUTPUT_FOLDER, "处理报告.xlsx")
    pd.DataFrame(results).to_excel(report_path, index=False)
    print(f"\n✅ 处理完成, 报告已保存至: {report_path}")

# 保留原来的process_wells函数，但不在本次处理中使用
# def process_wells(wells_data, base_path, output_folder):
#     """
#     批量处理多个井的数据
#     
#     参数:
#         wells_data: 字典，键为井名，值为基准时间
#         base_path: 根目录路径，格式如 "D:\\采数据\\PROCESS\\"
#         output_folder: 输出文件夹路径
#     """
#     results = []
#     
#     for well_name, time_base in wells_data.items():
#         print(f"\n{'='*50}")
#         print(f"⏳ 正在处理: {well_name}, 基准时间: {time_base}")
#         
#         # 生成数据库文件名
#         db_filename = generate_db_filename(time_base)
#         
#         # 组合完整路径
#         db_path = os.path.join(base_path, well_name, db_filename)
#         
#         # 计算时间范围
#         start_time, end_time = calculate_time_range(time_base)
#         
#         print(f"📅 时间范围: {start_time} 至 {end_time}")
#         print(f"📂 数据库文件: {db_path}")
#         
#         # 导出数据
#         success = export_filtered_data(
#             db_file=db_path,
#             output_folder=output_folder,
#             start_time=start_time,
#             end_time=end_time,
#             base_time=time_base
#         )
#         
#         results.append({
#             "井名": well_name,
#             "基准时间": time_base,
#             "数据库文件": db_path,
#             "处理结果": "成功" if success else "失败"
#         })
#     
#     # 生成处理报告
#     report_path = os.path.join(output_folder, "处理报告.xlsx")
#     pd.DataFrame(results).to_excel(report_path, index=False)
#     print(f"\n✅ 处理完成, 报告已保存至: {report_path}") 
# 工况感知的增强数据加载器
# 基于现有TSlib框架的EarlysignaldetLoader进行扩展

import os
import pandas as pd
import numpy as np
from torch.utils.data import Dataset
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split

class ConditionAwareEarlysignalLoader(Dataset):
    """工况感知的早期征兆检测数据加载器"""
    
    def __init__(self, root_path, enc_in, file_list=None, limit_size=None, flag=None, 
                 enable_condition_features=True):
        """
        初始化工况感知数据加载器
        
        Args:
            root_path: 数据根路径
            enc_in: 输入特征维度
            flag: 'Train' 或 'Test'
            enable_condition_features: 是否启用工况特征提取
        """
        self.flag = flag
        self.enc_in = enc_in
        self.enable_condition_features = enable_condition_features
        
        # Step 1: 获取所有文件路径和标签
        self.file_paths, self.labels = self.get_file_paths_and_labels(root_path)
        
        # Step 2: 收集分类特征值并训练编码器
        self.cw_values, self.rigsta_values = self.collect_categorical_values(self.file_paths)
        
        # 初始化编码器
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()
        if self.cw_values:
            self.cw_encoder.fit(self.cw_values)
        if self.rigsta_values:
            self.rigsta_encoder.fit(self.rigsta_values)
        
        # 获取最大序列长度
        self.max_seq_len = self.get_max_length(self.file_paths)
        
        # 数据分割
        self.train_file, self.test_file, self.train_labels, self.test_labels = train_test_split(
            self.file_paths, self.labels, test_size=0.2, random_state=42, stratify=self.labels
        )
        
        if self.flag == 'Train':
            self.file_paths = self.train_file
            self.labels = self.train_labels
        else:
            self.file_paths = self.test_file
            self.labels = self.test_labels
            
        self.class_names = ['normal', 'earlysignal']
        self.scaler = StandardScaler()
        
        # 工况分类器
        if self.enable_condition_features:
            self.condition_classifier = WorkConditionClassifier()
        
        print(f"数据加载完成: {len(self.file_paths)} 个文件")
        print(f"最大序列长度: {self.max_seq_len}")
        print(f"正样本比例: {np.mean(self.labels):.3f}")

    def get_file_paths_and_labels(self, base_folder):
        """获取文件路径和对应标签"""
        file_paths = []
        labels = []
        
        # 遍历所有子文件夹
        for root, dirs, files in os.walk(base_folder):
            for file_name in files:
                if file_name.endswith('.csv'):
                    file_path = os.path.join(root, file_name)
                    
                    # 根据文件夹路径确定标签
                    if 'earlysignal' in root.lower():
                        label = 1  # 卡钻征兆
                    else:
                        label = 0  # 正常
                    
                    file_paths.append(file_path)
                    labels.append(label)
        
        return file_paths, labels

    def collect_categorical_values(self, file_paths):
        """收集所有文件中的分类特征值"""
        cw_values = []
        rigsta_values = []
        
        for file_path in file_paths:
            try:
                data = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    data = pd.read_csv(file_path, encoding='gbk')
                except:
                    continue
            
            if 'CW' in data.columns:
                cw_values.extend(data['CW'].dropna().unique().tolist())
            if 'RIGSTA' in data.columns:
                rigsta_values.extend(data['RIGSTA'].dropna().unique().tolist())
        
        return list(set(cw_values)), list(set(rigsta_values))

    def load_time_series_data(self, file_path):
        """加载时间序列数据"""
        try:
            data = pd.read_csv(file_path, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                data = pd.read_csv(file_path, encoding='gbk')
            except:
                return None, None, None
        
        # 确保有必要的列
        required_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
        for col in required_cols:
            if col not in data.columns:
                data[col] = 0.0
        
        # 处理分类特征
        non_temp = pd.DataFrame(np.zeros((data.shape[0], 2)), columns=['CW', 'RIGSTA'])
        
        if 'CW' in data.columns and len(self.cw_values) > 0:
            try:
                data['CW'] = self.cw_encoder.transform(data['CW'].fillna(method='ffill'))
                non_temp['CW'] = data['CW']
            except:
                non_temp['CW'] = 0
        
        if 'RIGSTA' in data.columns and len(self.rigsta_values) > 0:
            try:
                data['RIGSTA'] = data['RIGSTA'].astype(str).fillna(method='ffill')
                data['RIGSTA'] = self.rigsta_encoder.transform(data['RIGSTA'])
                non_temp['RIGSTA'] = data['RIGSTA']
            except:
                non_temp['RIGSTA'] = 0
        
        # 提取时间序列特征
        if 'date' in data.columns:
            data = data.drop(['date'], axis=1)
        if 'CW' in data.columns:
            data = data.drop(['CW'], axis=1)
        if 'RIGSTA' in data.columns:
            data = data.drop(['RIGSTA'], axis=1)
        
        time_series = data[required_cols].values
        non_temp = non_temp.values
        
        # 工况特征提取
        condition_features = None
        if self.enable_condition_features:
            try:
                # 重新构建DataFrame用于工况分析
                analysis_df = pd.DataFrame(time_series, columns=required_cols)
                if 'date' not in analysis_df.columns:
                    analysis_df['date'] = pd.date_range(start='2023-01-01', periods=len(analysis_df), freq='5S')
                
                condition_features = self.condition_classifier.extract_condition_features(analysis_df)
            except Exception as e:
                print(f"工况特征提取失败: {e}")
                condition_features = np.zeros((len(time_series), 16))  # 默认16维工况特征
        
        return time_series, non_temp, condition_features

    def get_max_length(self, file_paths):
        """获取最大序列长度"""
        max_length = 0
        for file_path in file_paths[:100]:  # 只检查前100个文件以节省时间
            result = self.load_time_series_data(file_path)
            if result[0] is not None:
                time_series = result[0]
                if len(time_series) > max_length:
                    max_length = len(time_series)
        
        # 设置合理的最大长度上限
        return min(max_length, 500)

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        """获取单个样本"""
        file_path = self.file_paths[idx]
        label = self.labels[idx]
        
        # 加载数据
        result = self.load_time_series_data(file_path)
        if result[0] is None:
            # 如果加载失败，返回零填充的数据
            padded_time_series = np.zeros((self.max_seq_len, self.enc_in))
            padded_non_temp = np.zeros((self.max_seq_len, 2))
            condition_features = np.zeros((self.max_seq_len, 16)) if self.enable_condition_features else None
        else:
            time_series, non_temp, condition_features = result
            
            # 序列长度处理
            length = len(time_series)
            
            # 时间序列数据填充
            if length < self.max_seq_len:
                padded_time_series = np.zeros((self.max_seq_len, self.enc_in))
                padded_time_series[:length] = time_series[:, :self.enc_in]
            else:
                padded_time_series = time_series[:self.max_seq_len, :self.enc_in]
            
            # 非时序特征填充
            if length < self.max_seq_len:
                padded_non_temp = np.zeros((self.max_seq_len, non_temp.shape[1]))
                padded_non_temp[:length] = non_temp
            else:
                padded_non_temp = non_temp[:self.max_seq_len]
            
            # 工况特征填充
            if self.enable_condition_features and condition_features is not None:
                if length < self.max_seq_len:
                    padded_condition_features = np.zeros((self.max_seq_len, condition_features.shape[1]))
                    padded_condition_features[:length] = condition_features
                else:
                    padded_condition_features = condition_features[:self.max_seq_len]
                condition_features = padded_condition_features
        
        # 数据标准化
        self.scaler.fit(padded_time_series)
        padded_time_series = self.scaler.transform(padded_time_series)
        
        if self.enable_condition_features:
            return padded_time_series, padded_non_temp, condition_features, label, file_path
        else:
            return padded_time_series, padded_non_temp, label, file_path


class WorkConditionClassifier:
    """工况分类器 - 简化版本"""
    
    def __init__(self):
        self.condition_mapping = {
            0: '起钻',      # Tripping up
            1: '下钻',      # Tripping down  
            2: '正常钻进',   # Normal drilling
            3: '正划眼',    # Forward reaming
            4: '倒划眼'     # Reverse reaming
        }
    
    def extract_condition_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取工况相关的统计特征"""
        condition_features = []
        window_size = min(10, len(data))
        
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            window_data = data.iloc[start_idx:i+1]
            
            features = []
            
            # WOB相关特征
            features.extend([
                window_data['WOB'].mean(),
                window_data['WOB'].std(),
                (window_data['WOB'] > 50).sum() / len(window_data),
                (window_data['WOB'] < 10).sum() / len(window_data),
            ])
            
            # RPM相关特征  
            features.extend([
                window_data['RPM'].mean(),
                window_data['RPM'].std(),
                (window_data['RPM'] > 60).sum() / len(window_data),
            ])
            
            # 深度变化特征
            dep_diff = window_data['DEP'].diff().fillna(0)
            features.extend([
                dep_diff.mean(),
                dep_diff.std(),
                (dep_diff > 0.1).sum() / len(window_data),
                (dep_diff < -0.1).sum() / len(window_data),
            ])
            
            # HKLD和TOR特征
            features.extend([
                window_data['HKLD'].mean(),
                window_data['HKLD'].std(),
                window_data['TOR'].mean(),
                window_data['TOR'].std(),
            ])
            
            # 钻进效率特征
            drilling_rate = abs(dep_diff.sum()) / max(len(window_data), 1)
            features.append(drilling_rate)
            
            condition_features.append(features)
        
        return np.array(condition_features)
    
    def classify_conditions(self, data: pd.DataFrame) -> np.ndarray:
        """基于规则的工况分类"""
        conditions = np.zeros(len(data))
        
        # 计算关键指标的滑动平均
        window = min(5, len(data))
        wob_ma = data['WOB'].rolling(window=window, center=True).mean()
        rpm_ma = data['RPM'].rolling(window=window, center=True).mean()
        dep_diff = data['DEP'].diff().rolling(window=window, center=True).mean()
        
        for i in range(len(data)):
            wob = wob_ma.iloc[i] if not pd.isna(wob_ma.iloc[i]) else data['WOB'].iloc[i]
            rpm = rpm_ma.iloc[i] if not pd.isna(rpm_ma.iloc[i]) else data['RPM'].iloc[i]
            dep_change = dep_diff.iloc[i] if not pd.isna(dep_diff.iloc[i]) else 0
            
            # 工况判断逻辑
            if wob < 20 and dep_change < -0.05:  # 起钻
                conditions[i] = 0
            elif wob < 20 and dep_change > 0.05:  # 下钻
                conditions[i] = 1
            elif wob > 40 and rpm > 50 and abs(dep_change) > 0.02:  # 正常钻进
                conditions[i] = 2
            elif 20 <= wob <= 60 and rpm > 30 and abs(dep_change) < 0.02:  # 正划眼
                conditions[i] = 3
            else:  # 倒划眼或其他
                conditions[i] = 4
                
        return conditions.astype(int)


# 使用示例
if __name__ == "__main__":
    # 创建工况感知数据加载器
    train_loader = ConditionAwareEarlysignalLoader(
        root_path='./dataset/earlysignaldetection',
        enc_in=10,
        flag='Train',
        enable_condition_features=True
    )
    
    # 测试数据加载
    sample = train_loader[0]
    print(f"样本数据形状:")
    print(f"  时间序列: {sample[0].shape}")
    print(f"  非时序特征: {sample[1].shape}")
    print(f"  工况特征: {sample[2].shape}")
    print(f"  标签: {sample[3]}")
    print(f"  文件路径: {sample[4]}")
